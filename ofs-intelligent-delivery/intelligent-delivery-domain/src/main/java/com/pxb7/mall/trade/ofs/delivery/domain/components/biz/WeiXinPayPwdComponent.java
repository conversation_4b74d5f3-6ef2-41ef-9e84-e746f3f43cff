package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.FormComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 微信支付组件
 */
@Slf4j
@Extensions(
    bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
    useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
    scenario = {ComponentTypeEnum.Constants.B_WEIXIN_PAY_PWD_2})
public class WeiXinPayPwdComponent implements ComponentExtPt {

    private String KEY_WEIXIN_PAY_PWD  = DeliveryDataCodeEnum.wechatPayPassword.getCode();

    private final String CHECK_PAY_PWD_REGEX = "\\d{6}";

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;


    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_WEIXIN_PAY_PWD);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
            ()-> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), List.of(KEY_WEIXIN_PAY_PWD))
        ).getOrElseThrow(e-> new BizException("", e.getMessage()));
    }

    @Override
    public void formCheck(ComponentContextData data) {
        Map<String, String> formData = data.getDeliveryData();

        checkNonNull(List.of(KEY_WEIXIN_PAY_PWD), formData);
        checkPwdFormat(formData.get(KEY_WEIXIN_PAY_PWD));
    }

    private void checkPwdFormat(String value) {
        if (!value.matches(CHECK_PAY_PWD_REGEX)) {
            throw new BizException("","请输入6位微信支付密码");
        }
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), List.of(KEY_WEIXIN_PAY_PWD));
    }
}
