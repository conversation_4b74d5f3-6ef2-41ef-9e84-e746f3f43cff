package com.pxb7.mall.trade.ofs.delivery.domain.service;



import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ofs.delivery.domain.model.FaqTopicReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.FaqTopicRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.mapping.FaqTopicDomainMapping;
import com.pxb7.mall.trade.ofs.delivery.infra.model.FaqTopicReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FaqTopic;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.FaqTopicRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;



/**
 * faq主题domain服务
 *
 * <AUTHOR>
 * @since 2025-03-31 16:53:38
 */
@Service
public class FaqTopicDomainService {

    @Resource
    private FaqTopicRepository faqTopicRepository;
 
    public boolean insert(FaqTopicReqBO.AddBO param) {
        FaqTopicReqPO.AddPO addPO = FaqTopicDomainMapping.INSTANCE.faqTopicBO2AddPO(param);
        return faqTopicRepository.insert(addPO);
    }

    public boolean update(FaqTopicReqBO.UpdateBO param) {
        FaqTopicReqPO.UpdatePO updatePO = FaqTopicDomainMapping.INSTANCE.faqTopicBO2UpdatePO(param);
        return faqTopicRepository.update(updatePO);
    }

    public boolean deleteById(FaqTopicReqBO.DelBO param) {
        FaqTopicReqPO.DelPO delPO = FaqTopicDomainMapping.INSTANCE.faqTopicBO2DelPO(param);
        return faqTopicRepository.deleteById(delPO);
    }

    public FaqTopicRespBO.DetailBO findById(Long id) {
        FaqTopic entity = faqTopicRepository.findById(id);
        return FaqTopicDomainMapping.INSTANCE.faqTopicPO2DetailBO(entity);

    }

    public List<FaqTopicRespBO.DetailBO> list(FaqTopicReqBO.SearchBO param) {
        FaqTopicReqPO.SearchPO searchPO = FaqTopicDomainMapping.INSTANCE.faqTopicBO2SearchPO(param);
        List<FaqTopic> list = faqTopicRepository.list(searchPO);
        return FaqTopicDomainMapping.INSTANCE.faqTopicPO2ListBO(list);
    }

    public Page<FaqTopicRespBO.DetailBO> page(FaqTopicReqBO.PageBO param) {
        FaqTopicReqPO.PagePO pagePO = FaqTopicDomainMapping.INSTANCE.faqTopicBO2PagePO(param);
        Page<FaqTopic> page = faqTopicRepository.page(pagePO);
        return FaqTopicDomainMapping.INSTANCE.faqTopicPO2PageBO(page);
    }
   
}

