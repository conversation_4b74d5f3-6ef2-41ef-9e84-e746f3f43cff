package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ofs.delivery.domain.mapping.ComponentsExampleDiagramConfigDomainMapping;
import com.pxb7.mall.trade.ofs.delivery.domain.model.ComponentsExampleDiagramConfigReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.ComponentsExampleDiagramConfigRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.ComponentsExampleDiagramConfigReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.ComponentsExampleDiagramConfigRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ComponentsExampleDiagramConfig;

import cn.hutool.core.collection.CollUtil;
import io.vavr.control.Option;
import jakarta.annotation.Resource;

/**
 * 组件示例图配置domain服务
 *
 * <AUTHOR>
 * @since 2025-03-12 14:31:01
 */
@Service
public class ComponentsExampleDiagramConfigDomainService {

    @Resource
    private ComponentsExampleDiagramConfigRepository componentsExampleDiagramConfigRepository;


    public ComponentsExampleDiagramConfigRespBO.DetailBO findById(Long id) {
        ComponentsExampleDiagramConfig entity = componentsExampleDiagramConfigRepository.findById(id);
        return ComponentsExampleDiagramConfigDomainMapping.INSTANCE.componentsExampleDiagramConfigPO2DetailBO(entity);
    }

    public List<ComponentsExampleDiagramConfigRespBO.DetailBO> list(ComponentsExampleDiagramConfigReqBO.SearchBO param) {
        ComponentsExampleDiagramConfigReqPO.SearchPO searchPO = ComponentsExampleDiagramConfigDomainMapping.INSTANCE.componentsExampleDiagramConfigBO2SearchPO(param);
        List<ComponentsExampleDiagramConfig> list = componentsExampleDiagramConfigRepository.list(searchPO);
        return ComponentsExampleDiagramConfigDomainMapping.INSTANCE.componentsExampleDiagramConfigPO2ListBO(list);
    }

    public List<ComponentsExampleDiagramConfigRespBO.DetailBO> queryByComponentType(Integer componentType) {
       return  Option.of(componentsExampleDiagramConfigRepository.queryBy(componentType, null))
            .filter(CollUtil::isNotEmpty)
            .map(ComponentsExampleDiagramConfigDomainMapping.INSTANCE::componentsExampleDiagramConfigPO2ListBO)
            .getOrElse(ArrayList::new);
    }

    public List<ComponentsExampleDiagramConfigRespBO.DetailBO> queryByComponentTypeAndGameId(Integer componentType, String gameId) {
        return  Option.of(componentsExampleDiagramConfigRepository.queryBy(componentType, gameId))
            .filter(CollUtil::isNotEmpty)
            .map(ComponentsExampleDiagramConfigDomainMapping.INSTANCE::componentsExampleDiagramConfigPO2ListBO)
            .getOrElse(ArrayList::new);
    }

}

