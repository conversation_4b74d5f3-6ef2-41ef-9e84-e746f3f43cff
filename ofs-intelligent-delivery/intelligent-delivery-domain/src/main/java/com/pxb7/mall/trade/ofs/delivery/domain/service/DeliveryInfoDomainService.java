package com.pxb7.mall.trade.ofs.delivery.domain.service;

import com.google.common.collect.Lists;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryInfoDTO;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataDetailBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.BatchGetDeliveryDataReqBO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessLog;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/06/27 14:40
 **/
@Service
@Slf4j
public class DeliveryInfoDomainService {

    @Resource
    private DeliveryInfoDbRepository deliveryInfoRepository;
    @Resource
    private DeliveryDataDomainService deliveryDataDomainService;
    @Resource
    private DeliveryProcessLogDomainService deliveryProcessLogDomainService;

    public DeliveryInfoDTO getDeliveryInfo(String orderItemId) {
        DeliveryInfoDTO deliveryInfoDTO = new DeliveryInfoDTO();
        deliveryInfoDTO.setOrderItemId(orderItemId);
        deliveryInfoDTO.setRepackage(false);
        DeliveryProcessLog processInsLog = deliveryProcessLogDomainService.getProcessInsLog(orderItemId);
        if (processInsLog != null) {
            BatchGetDeliveryDataReqBO reqBO = new BatchGetDeliveryDataReqBO();
            reqBO.setProcessInsId(processInsLog.getProcessInsId());
            reqBO.setOrderItemId(orderItemId);
            reqBO.setCodes(List.of(DeliveryDataCodeEnum.gameAccount.getCode(), DeliveryDataCodeEnum.uid.getCode(),
                DeliveryDataCodeEnum.productSource.getCode(), DeliveryDataCodeEnum.repackage.getCode()));

            List<DeliveryDataDetailBO> dataValues = deliveryDataDomainService.batchGetDeliveryData(reqBO);

            if (CollectionUtils.isEmpty(dataValues)) {
                return null;
            }
            for (DeliveryDataDetailBO dataValue : dataValues) {
                String code = dataValue.getCode();
                String value = dataValue.getValue();
                if (DeliveryDataCodeEnum.productSource.getCode().equals(code)) {
                    if ("from_self".equals(value)) {
                        deliveryInfoDTO.setAccountSource(0);
                    } else if ("from_px".equals(value)) {
                        deliveryInfoDTO.setAccountSource(1);
                    } else if ("other".equals(value)) {
                        deliveryInfoDTO.setAccountSource(2);
                    }
                } else if (DeliveryDataCodeEnum.repackage.getCode().equals(code)) {
                    deliveryInfoDTO.setRepackage(Boolean.parseBoolean(value));
                } else if (DeliveryDataCodeEnum.uid.getCode().equals(code)) {
                    deliveryInfoDTO.setAccountUid(value);
                } else if (DeliveryDataCodeEnum.gameAccount.getCode().equals(code)) {
                    deliveryInfoDTO.setGameAccount(value);
                }
            }
            deliveryInfoDTO.setId(processInsLog.getId());
            return deliveryInfoDTO;
        }
        DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(orderItemId);
        if (deliveryInfo == null) {
            return null;
        }
        deliveryInfoDTO.setId(deliveryInfo.getId());
        deliveryInfoDTO.setAccountSource(deliveryInfo.getAccountSource());
        deliveryInfoDTO.setRepackage(deliveryInfo.isRepackage());
        deliveryInfoDTO.setAccountUid(deliveryInfo.getAccountUid());
        deliveryInfoDTO.setGameAccount(deliveryInfo.getGameAccount());
        return deliveryInfoDTO;
    }

    public List<DeliveryInfoDTO> findDeliveryInfoListByOrderItemIds(List<String> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return Lists.newArrayList();
        }
        List<DeliveryInfoDTO> lists = Lists.newArrayListWithCapacity(orderItemIds.size());
        orderItemIds.parallelStream().forEach(s -> {
            DeliveryInfoDTO deliveryInfoDTO = this.getDeliveryInfo(s);
            if (Objects.nonNull(deliveryInfoDTO)) {
                lists.add(deliveryInfoDTO);
            }
        });
        return lists;
    }

    public DeliveryInfo getDeliveryInfoByOrderItemId(String orderItemId) {
        return deliveryInfoRepository.getDeliveryInfo(orderItemId);
    }
}
