package com.pxb7.mall.trade.ofs.delivery.domain.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.util.PhoneNumberValidator;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import org.apache.groovy.util.Maps;

import java.util.Map;

import static com.pxb7.mall.trade.ofs.delivery.domain.components.biz.EighteenBitReBindCodeComponent.isValidChecksum;
import static com.pxb7.mall.trade.ofs.delivery.domain.components.biz.EmailSalesComponent.isValidEmail;
import static com.pxb7.mall.trade.ofs.delivery.domain.components.biz.GameAccountComponent.KEY_ACCOUNT;
import static com.pxb7.mall.trade.ofs.delivery.domain.components.biz.LoginedWeiXinAccountComponent.checkCharacter;
import static com.pxb7.mall.trade.ofs.delivery.domain.components.biz.LoginedWeiXinAccountComponent.isValid;

public interface DataConfigValueStrategy {

    DataConfigValueStrategy check(String value);

    default String format(String value) {
        return CharSequenceUtil.trim(value);
    }

    default void isNull(String value){
        if(CharSequenceUtil.isBlank(value)){
            throw new BizException("DPD004", "不能为空");
        }
    }


    default void checkLength(String desc, String value, int min, int max, String toast) {
        if (CharSequenceUtil.isBlank(value)) {
            return;
        }
        if (value.length() < min || value.length() > max) {
            throw new BizException("DPD004", toast);
        }
    }

    default void checkLength(String desc, String value, int min, int max) {
       checkLength(desc, value, min, max, "%s长度必须在%d到%d之间".formatted(desc, min, max));
    }

    //18位换绑码
    class EighteenBitReBindCode implements DataConfigValueStrategy {
        @Override
        public EighteenBitReBindCode check(String value) {
            isNull(value);
            checkOfferCodeFormat(value);
            return this;
        }

        private void checkOfferCodeFormat(String offerCode) {
            String OFFER_CODE_REGEX = "^[1-9]\\\\d{5}(19|20)\\\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\\\d|3[01])\\\\d{3}[0-9Xx]$";
            if (!offerCode.matches(OFFER_CODE_REGEX) && !isValidChecksum(offerCode)) {
                throw new BizException("","请检查18位换绑码是否输入有误");
            }
        }

        @Override
        public String format(String value) {
            if(CharSequenceUtil.isBlank(value)){
                return "";
            }
            String CODE_FORMAT = "***%s***";
            return String.format(CODE_FORMAT, value.substring(3, value.length() - 3));
        }
    }

    //邮箱出售账号
    class EmailSaleAccount implements DataConfigValueStrategy {

        @Override
        public EmailSaleAccount check(String value) {
            isNull(value);
            if (!isValidEmail(value)) {
                throw new BizException("邮箱格式错误");
            }
            return this;
        }
    }

    //游戏账号
    class GameAccount implements DataConfigValueStrategy {

        @Override
        public GameAccount check(String value) {
            isNull(value);
            Map<String, String> maps = Maps.of(KEY_ACCOUNT, value);
            checkLength("", value, 1, 51, "请检查是否输入有误");
            return this;
        }
    }

    //uid
    class Uid implements DataConfigValueStrategy {

        @Override
        public Uid check(String value) {
            isNull(value);
            return this;
        }
    }

    //登陆后微信号
    class LoginWechatAccount implements DataConfigValueStrategy {

        @Override
        public LoginWechatAccount check(String value) {
            isNull(value);
            checkLength( "登陆后微信号", value, 6, 20);
            checkCharacter(value);
            isValid(value);
            return this;
        }
    }

    //换绑手机号
    class BindingChangeMobile implements DataConfigValueStrategy {
        @Override
        public BindingChangeMobile check(String value) {
            isNull(value);
            if (!PhoneNumberValidator.isValidPhoneNumber(value)) {
                throw new BizException(ErrorCode.RE_BIND_PHONE_NUMBER_INVALID_ERROR.getErrCode(),
                        ErrorCode.RE_BIND_PHONE_NUMBER_INVALID_ERROR.getErrDesc());
            }
            return this;
        }

        @Override
        public String format(String value) {
            if (CharSequenceUtil.isBlank(value)) {
                return "";
            }
            final String CODE_FORMAT = "***%s****";
            return String.format(CODE_FORMAT, value.substring(3, value.length() - 4));
        }
    }

    //psn登陆账号
    class PsnLoginAccount implements DataConfigValueStrategy {
        @Override
        public PsnLoginAccount check(String value) {
            isNull(value);
            return this;
        }
    }

    //psn登陆密码
    class PsnLoginPassword implements DataConfigValueStrategy {
        @Override
        public PsnLoginPassword check(String value) {
            isNull(value);
            return this;
        }
    }

    //tap登陆账号
    class TapLoginAccount implements DataConfigValueStrategy {
        @Override
        public TapLoginAccount check(String value) {
            isNull(value);
            checkLength( "tap登陆账号", value, 1, 51);
            return this;
        }
    }

    //tap登陆密码
    class TapLoginPassword implements DataConfigValueStrategy {
        @Override
        public TapLoginPassword check(String value) {
            isNull(value);
            checkLength( "tap登陆密码", value, 1, 51);
            return this;
        }
    }

    //腾讯账号类型
    class TxProductType implements DataConfigValueStrategy {

        @Override
        public TxProductType check(String value) {
            isNull(value);
            return this;
        }
    }

    class WechatPayPassword implements DataConfigValueStrategy {

        private final String CHECK_PAY_PWD_REGEX = "\\d{6}";

        @Override
        public WechatPayPassword check(String value) {
            isNull(value);
            checkPwdFormat(value);
            return this;
        }

        private void checkPwdFormat(String value) {
            if (!value.matches(CHECK_PAY_PWD_REGEX)) {
                throw new BizException("","请输入6位微信支付密码");
            }
        }
    }

    //网易支付截图
    class NetEaseRealNameImageUrl implements DataConfigValueStrategy {
        @Override
        public NetEaseRealNameImageUrl check(String value) {
            isNull(value);
            return this;
        }
    }

    //二次实名截图
    class SecondRealNameImageUrl implements DataConfigValueStrategy {
        @Override
        public SecondRealNameImageUrl check(String value) {
            isNull(value);
            return this;
        }
    }

    //UID截图
    class UidImageUrl implements DataConfigValueStrategy {
        @Override
        public UidImageUrl check(String value) {
            isNull(value);
            return this;
        }
    }

    //Apple登录账号
    class AppleLoginAccount implements DataConfigValueStrategy {
        @Override
        public DataConfigValueStrategy check(String value) {
            isNull(value);
            checkLength("Apple登录账号", value, 5, 50, "请检查是否输入有误");
            return this;
        }
    }

    //Apple登录密码
    class AppleLoginPassword implements DataConfigValueStrategy {
        @Override
        public DataConfigValueStrategy check(String value) {
            isNull(value);
            checkLength("Apple登录密码", value, 5, 50, "请检查是否输入有误");
            return this;
        }
    }

    //QQ登录账号
    class QQLoginAccount implements DataConfigValueStrategy {
        @Override
        public DataConfigValueStrategy check(String value) {
            isNull(value);
            checkQQAccountFormat(value);
            return this;
        }

        private void checkQQAccountFormat(String account) {
            String trimmedAccount = account.trim();

            // QQ号格式（5-11位数字）
            boolean isQQNumber = trimmedAccount.matches("^[1-9]\\d{4,10}$");

            // 邮箱格式
            boolean isEmail = trimmedAccount.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");

            if (!isQQNumber && !isEmail) {
                throw new BizException("", "请输入正确的QQ号（5-11位数字）或邮箱");
            }
        }
    }

    //QQ登录密码
    class QQLoginPassword implements DataConfigValueStrategy {
        @Override
        public DataConfigValueStrategy check(String value) {
            isNull(value);
            checkLength("QQ登录密码", value, 6, 20);
            return this;
        }
    }

    //微信登录账号
    class WeiXinLoginAccount implements DataConfigValueStrategy {
        @Override
        public DataConfigValueStrategy check(String value) {
            isNull(value);
            checkWeiXinAccountFormat(value);
            return this;
        }

        private void checkWeiXinAccountFormat(String account) {
            String trimmedAccount = account.trim();

            // 微信号格式验证（字母开头，字母数字下划线横线组合，6-20位）
            checkLength("微信账号", trimmedAccount, 6, 20);

            if (containsChinese(trimmedAccount) || trimmedAccount.contains(" ") || !trimmedAccount.matches("^[a-zA-Z_-][a-zA-Z0-9_-]*$")) {
                throw new BizException("", "微信账号禁止使用中文、空格及特殊符号");
            }

            if (!trimmedAccount.matches("[a-zA-Z_-][a-zA-Z0-9_-].*")) {
                throw new BizException("", "微信账号必须以字母(A-Z或a-z)开头");
            }
        }

        // 检测中文字符
        private static boolean containsChinese(String str) {
            return str.codePoints().anyMatch(
                    code -> Character.UnicodeScript.of(code) == Character.UnicodeScript.HAN);
        }
    }

    //微信登录密码
    class WeiXinLoginPassword implements DataConfigValueStrategy {
        @Override
        public DataConfigValueStrategy check(String value) {
            isNull(value);
            checkLength("微信登录密码", value, 6, 20);
            return this;
        }
    }

    enum Strategy {

        EighteenBitReBindCode(DeliveryDataCodeEnum.bindingChangeCode18.getCode(), new EighteenBitReBindCode()),
        EmailSaleAccount(DeliveryDataCodeEnum.emailSaleAccount.getCode(), new EmailSaleAccount()),
        GameAccount(DeliveryDataCodeEnum.gameAccount.getCode(), new GameAccount()),
        Uid(DeliveryDataCodeEnum.uid.getCode(), new Uid()),
        LoginWechatAccount(DeliveryDataCodeEnum.loginWechatAccount.getCode(), new LoginWechatAccount()),
        BindingChangeMobile(DeliveryDataCodeEnum.bindingChangeMobile.getCode(), new BindingChangeMobile()),
        PsnLoginAccount(DeliveryDataCodeEnum.psnLoginAccount.getCode(), new PsnLoginAccount()),
        PsnLoginPassword(DeliveryDataCodeEnum.psnLoginPassword.getCode(), new PsnLoginPassword()),
        TapLoginAccount(DeliveryDataCodeEnum.tapLoginAccount.getCode(), new TapLoginAccount()),
        TapLoginPassword(DeliveryDataCodeEnum.tapLoginPassword.getCode(), new TapLoginPassword()),
        TxProductType(DeliveryDataCodeEnum.txProductType.getCode(), new TxProductType()),
        WechatPayPassword(DeliveryDataCodeEnum.wechatPayPassword.getCode(), new WechatPayPassword()),
        NetEaseRealNameImageUrl(DeliveryDataCodeEnum.netEaseRealNameImageUrl.getCode(), new NetEaseRealNameImageUrl()),
        SecondRealNameImageUrl(DeliveryDataCodeEnum.secondRealNameImageUrl.getCode(), new SecondRealNameImageUrl()),
        UidImageUrl(DeliveryDataCodeEnum.uidImageUrl.getCode(), new UidImageUrl()),
        AppleLoginAccount(DeliveryDataCodeEnum.appleLoginAccount.getCode(), new AppleLoginAccount()),
        AppleLoginPassword(DeliveryDataCodeEnum.appleLoginPwd.getCode(), new AppleLoginPassword())
        ;
        private String dataCode;
        private DataConfigValueStrategy strategy;

        Strategy(String dataCode, DataConfigValueStrategy strategy) {
            this.dataCode = dataCode;
            this.strategy = strategy;
        }

        public static DataConfigValueStrategy getStrategy(String dataCode) {
            for (Strategy strategy : Strategy.values()) {
                if (strategy.dataCode.equals(dataCode)) {
                    return strategy.strategy;
                }
            }
            return null;
        }
    }

    enum DataConfigIsImageEnum{
        productThumbnail(DeliveryDataCodeEnum.productThumbnail.getCode(), "商品主图"),
        secondRealNameImageUrl(DeliveryDataCodeEnum.secondRealNameImageUrl.getCode(), "二次实名截图"),
        uidImageUrl(DeliveryDataCodeEnum.uidImageUrl.getCode(), "UID截图"),
        netEaseRealNameImageUrl(DeliveryDataCodeEnum.netEaseRealNameImageUrl.getCode(), "网易支付截图"),
        ;
        private String dataCode;
        private String desc;

        DataConfigIsImageEnum(String dataCode, String desc) {
            this.dataCode = dataCode;
            this.desc = desc;
        }

        public static boolean isImageDataConfig(String dataCode) {
            for(DataConfigIsImageEnum dataConfigIsImageEnum : DataConfigIsImageEnum.values()) {
                if(dataConfigIsImageEnum.dataCode.equals(dataCode)) {
                    return true;
                }
            }
            return false;
        }

    }
}
