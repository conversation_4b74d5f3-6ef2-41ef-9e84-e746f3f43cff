package com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.card.models;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CardSendData {

    /**
     * 是否debug
     */
    private Boolean debugFlag = Boolean.FALSE;

    /**
     * 压测标识
     */
    private Boolean pressureFlag = Boolean.FALSE;

    /**
     * 一级节点名称id
     */
    private Long node1NameId;
    /**
     * 一级节点名称
     */
    private String node1Name;
    /**
     * 二级节点名称id
     */

    private Long node2NameId;

    /**
     * 二级节点名称
     */
    private String node2Name;

    /**
     * 二级节点配置id
     */
    private Long processNodeConfigId;

    /**
     * 1:普通节点 2:判断节点 3:人工节点 4:并行节点
     */
    private Integer nodeType;

    /**
     * 流程实例id
     */
    private String processInsId;

    /**
     * 节点运行态id
     */
    private String taskInsId;

    /**
     * 订单id
     */
    private String orderItemId;


    /**
     * 是否是流转任务
     */
    private Boolean flowTask = Boolean.FALSE;

    /**
     * 卡片配置id
     */
    private Long cardConfigId;

    /**
     * 艾特发送对象 1：买家，2：卖家
     */
    private String mentioned;

    /**
     * 对买卖家可见 1：买家，2：卖家
     */
    private String showType;

    /**
     * 是否唤起客服
     */
    private Boolean wakeCustomer = Boolean.FALSE;

    // =======================================================================================================================

    /**
     * 卡片下发数据
     */
    private CardSendContent cardSendContent = new CardSendContent();

}
