package com.pxb7.mall.trade.ofs.delivery.domain.model;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 流程节点日志表(DeliveryProcessTaskLog)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:48:39
 */
public class DeliveryProcessTaskLogReqBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SearchBO {

        private String orderItemId;

        private String processInsId;

        private Long processNodeConfigId;

        private String taskInsId;

        private String cardMsgId;

        private Integer taskStatus;

        private String wakeTaskId;

        private String createUserId;

        private String updateUserId;

    }

}
