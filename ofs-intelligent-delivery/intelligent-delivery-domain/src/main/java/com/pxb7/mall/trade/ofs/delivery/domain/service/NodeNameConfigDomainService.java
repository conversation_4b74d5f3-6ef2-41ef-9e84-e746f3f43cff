package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.NodeNameConfigRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.NodeNameConfig;

import jakarta.annotation.Resource;

/**
 * 节点管理domain服务
 *
 * <AUTHOR>
 * @since 2025-02-13 10:44:19
 */
@Service
public class NodeNameConfigDomainService {

    @Resource
    private NodeNameConfigRepository nodeNameConfigRepository;

    public Map<Long, String> getNodeName(Collection<Long> nodeNamIds) {
        LambdaQueryWrapper<NodeNameConfig> wq = new LambdaQueryWrapper<>();
        wq.in(NodeNameConfig::getId, nodeNamIds);
        List<NodeNameConfig> list = nodeNameConfigRepository.list(wq);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(NodeNameConfig::getId, NodeNameConfig::getNodeName));

    }


}
