package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ofs.delivery.domain.mapping.ContentGameAttrDomainMapping;
import com.pxb7.mall.trade.ofs.delivery.domain.model.ContentGameAttrReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.ContentGameAttrRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.ContentGameAttrReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.ContentGameAttrRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ContentGameAttr;

import jakarta.annotation.Resource;

/**
 * 内容游戏属性配置domain服务
 *
 * <AUTHOR>
 * @since 2025-02-13 10:50:19
 */
@Service
public class ContentGameAttrDomainService {

    @Resource
    private ContentGameAttrRepository contentGameAttrRepository;

    public ContentGameAttrRespBO.DetailBO findById(Long id) {
        ContentGameAttr entity = contentGameAttrRepository.findById(id);
        return ContentGameAttrDomainMapping.INSTANCE.contentGameAttrPO2DetailBO(entity);
    }

    public List<ContentGameAttrRespBO.DetailBO> list(ContentGameAttrReqBO.SearchBO param) {
        ContentGameAttrReqPO.SearchPO searchPO =
            ContentGameAttrDomainMapping.INSTANCE.contentGameAttrBO2SearchPO(param);
        List<ContentGameAttr> list = contentGameAttrRepository.list(searchPO);
        return ContentGameAttrDomainMapping.INSTANCE.contentGameAttrPO2ListBO(list);
    }

}
