package com.pxb7.mall.trade.ofs.delivery.domain;

import com.pxb7.mall.trade.ofs.delivery.domain.model.request.GetAnnouncementsBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.GetTradeAlertBO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.GameTradeAnnouncementsRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Announcements;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.TradeAlert;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class GameTradeAnnouncementsDomainService {

    @Resource
    private GameTradeAnnouncementsRepository gameTradeAnnouncementsRepository;

    /**
     * 根据商品信息获取对应的验号注意事项提醒
     *
     * @param getAnnouncementsBO
     */
    public Announcements getAnnouncements(GetAnnouncementsBO getAnnouncementsBO) {
        return gameTradeAnnouncementsRepository.getAnnouncements(getAnnouncementsBO.getGameId(),
            getAnnouncementsBO.getItemIds());
    }

    /**
     * 根据商品信息获取对应的消息提醒数据
     *
     * @param getTradeAlertBO
     */
    public TradeAlert getTradeAlert(GetTradeAlertBO getTradeAlertBO) {
        if (getTradeAlertBO == null || StringUtils.isBlank(getTradeAlertBO.getGameId()) || StringUtils.isBlank(
            getTradeAlertBO.getItemIds())) {
            return null;
        }
        return gameTradeAnnouncementsRepository.getTradeAlert(getTradeAlertBO.getGameId(),
            getTradeAlertBO.getItemIds());
    }

}
