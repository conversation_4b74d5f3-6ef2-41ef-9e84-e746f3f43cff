package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.delivery.domain.mapping.DeliveryDataConfigDomainMapping;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataConfigReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataConfigRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.DeliveryDataConfigReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryDataConfigRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryDataConfig;

import jakarta.annotation.Resource;

/**
 * 数据定义表domain服务
 *
 * <AUTHOR>
 * @since 2025-02-13 10:36:40
 */
@Service
public class DeliveryDataConfigDomainService {

    public static final String CACHE_KEY_PREFIX = "delivery_data_config:";
    public static final String FAQ_CACHE_KEY = "delivery_data_config:faq";
    public static final String JUDGE_CACHE_KEY = "delivery_data_config:judge";
    public static final String STYLE_JSON_CACHE_KEY_PREFIX = "delivery_data_config:json:";

    private static String cacheKey(String key){
        return CACHE_KEY_PREFIX + key;
    }
    private static String styleJsonCacheKey(String key){
        return STYLE_JSON_CACHE_KEY_PREFIX + key;
    }

    @Resource
    private DeliveryDataConfigRepository deliveryDataConfigRepository;

    public DeliveryDataConfigRespBO.DetailBO findById(Long id) {
        DeliveryDataConfig entity = deliveryDataConfigRepository.findById(id);
        return DeliveryDataConfigDomainMapping.INSTANCE.deliveryDataConfigPO2DetailBO(entity);
    }

    public List<DeliveryDataConfigRespBO.DetailBO> list(DeliveryDataConfigReqBO.SearchBO param) {
        DeliveryDataConfigReqPO.SearchPO searchPO =
            DeliveryDataConfigDomainMapping.INSTANCE.deliveryDataConfigBO2SearchPO(param);
        List<DeliveryDataConfig> list = deliveryDataConfigRepository.list(searchPO);
        return DeliveryDataConfigDomainMapping.INSTANCE.deliveryDataConfigPO2ListBO(list);
    }

    public String findStyleJsonByCodeWithCache(String code) {
        String cacheKey = styleJsonCacheKey(code);
        String jsonCache = RedissonUtils.getCacheObject(cacheKey);
        if (Objects.isNull(jsonCache)) {
            DeliveryDataConfig entity = deliveryDataConfigRepository.findByCode(code);
            if(Objects.isNull(entity)) {
                return StringUtils.EMPTY;
            }
            jsonCache = entity.getDataStyleJson();
            RedissonUtils.setCacheObject(cacheKey, jsonCache, Duration.ofHours(1));
        }
        return jsonCache;
    }

    public List<String> findFaqCodesWithCache() {
        String faqCache = RedissonUtils.getCacheObject(FAQ_CACHE_KEY);
        if (StringUtils.isBlank(faqCache)) {
            DeliveryDataConfigReqPO.SearchPO searchPO = new DeliveryDataConfigReqPO.SearchPO().setApplyFaq(true);
            List<DeliveryDataConfig> faqList = deliveryDataConfigRepository.list(searchPO);
            if (CollectionUtils.isEmpty(faqList)) {
                throw new BizException("交付数据字典中不存在FAQ");
            }
            List<String> faqCodes = faqList.stream().map(DeliveryDataConfig::getDataCode).toList();
            faqCache = JSON.toJSONString(faqCodes);
            RedissonUtils.setCacheObject(FAQ_CACHE_KEY, faqCache, Duration.ofHours(1));
        }
        return JSON.parseArray(faqCache, String.class);
    }

    public List<String> findJudgeCodesWithCache() {
        String faqCache = RedissonUtils.getCacheObject(JUDGE_CACHE_KEY);
        if (Objects.isNull(faqCache)) {
            DeliveryDataConfigReqPO.SearchPO searchPO = new DeliveryDataConfigReqPO.SearchPO().setApplyNode(true);
            List<DeliveryDataConfig> judgeDataList = deliveryDataConfigRepository.list(searchPO);
            if (CollectionUtils.isEmpty(judgeDataList)) {
                throw new BizException("交付数据字典中不存在判断节点数据");
            }
            List<String> judgeCodes = judgeDataList.stream().map(DeliveryDataConfig::getDataCode).toList();
            RedissonUtils.setCacheObject(JUDGE_CACHE_KEY, JSON.toJSONString(judgeCodes), Duration.ofHours(1));
        }
        return JSON.parseArray(faqCache, String.class);
    }
}
