package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Apple登录账密组件
 */
@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {ComponentTypeEnum.Constants.B_APPLE_LOGIN_PWD_2})
public class AppleLoginAccountPwdComponent implements ComponentExtPt {

    private final String KEY_APPLE_ACCOUNT = DeliveryDataCodeEnum.appleLoginAccount.getCode();
    private final String KEY_APPLE_PWD = DeliveryDataCodeEnum.appleLoginPwd.getCode();
    private final String KEY_IS_APPLE_BIND = DeliveryDataCodeEnum.appleBind.getCode();
    private final String APPLE_BIND_STATUS = "apple_binded";
    private final String APPLE_NO_BIND_STATUS = "apple_unbind";


    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_APPLE_ACCOUNT, KEY_APPLE_PWD);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
                () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), 
                        List.of(KEY_APPLE_ACCOUNT, KEY_APPLE_PWD, KEY_IS_APPLE_BIND))
        ).getOrElseThrow(e -> new BizException("", e.getMessage()));
    }

    @Override
    public void formCheck(ComponentContextData data) {
        final String appleBindStatus = data.getDeliveryData().getOrDefault(KEY_IS_APPLE_BIND, Strings.EMPTY);
        
        // 如果Apple已绑定，则需要验证账号和密码
        if (Objects.equals(appleBindStatus, APPLE_BIND_STATUS)) {
            checkNonNull(List.of(KEY_APPLE_ACCOUNT, KEY_APPLE_PWD), data.getDeliveryData());
            
            String account = data.getDeliveryData().get(KEY_APPLE_ACCOUNT);
            String password = data.getDeliveryData().get(KEY_APPLE_PWD);
            
            // 验证Apple账号格式（Apple ID通常是邮箱格式）
            checkAppleAccountFormat(account);
            
            // 验证密码长度
            checkLength("Apple登录密码", password, 8, 32);
        }
    }

    private void checkAppleAccountFormat(String account) {
        if (account == null || account.trim().isEmpty()) {
            throw new BizException("", "Apple账号不能为空");
        }
        
        // Apple ID必须是邮箱格式
        String trimmedAccount = account.trim();
        boolean isEmail = trimmedAccount.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");
        
        if (!isEmail) {
            throw new BizException("", "请输入正确的Apple ID（邮箱格式）");
        }
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), List.of(KEY_APPLE_ACCOUNT, KEY_APPLE_PWD));
    }
}
