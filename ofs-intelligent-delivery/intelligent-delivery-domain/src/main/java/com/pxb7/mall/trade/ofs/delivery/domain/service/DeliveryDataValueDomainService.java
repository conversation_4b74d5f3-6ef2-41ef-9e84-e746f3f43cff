package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.pxb7.mall.trade.ofs.delivery.domain.mapping.DeliveryDataValueDomainMapping;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataDetailBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataValueReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataValueRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryDataValueRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryDataValue;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据定义值domain服务
 *
 * <AUTHOR>
 * @since 2025-02-13 10:37:38
 */
@Service
@Slf4j
public class DeliveryDataValueDomainService {

    @Resource
    private DeliveryDataValueRepository deliveryDataValueRepository;

    /**
     * 保存或更新字段值
     * @param param：
     */
    public boolean saveAndUpdateBatchDeliveryDataValue(DeliveryDataValueReqBO.AddBO param) {
        if (CollectionUtils.isEmpty(param.getDataList())) {
            return false;
        }
        List<DeliveryDataValue> deliveryDataValueList = new ArrayList<>();
        for (DeliveryDataValueReqBO.DeliveryDataBO deliveryDataBO : param.getDataList()) {
            DeliveryDataValue deliveryDataValue = new DeliveryDataValue();
            deliveryDataValue.setProcessInsId(Objects.isNull(param.getProcessInsId()) ? StringUtils.EMPTY : param.getProcessInsId());
            deliveryDataValue.setOrderItemId(param.getOrderItemId());
            deliveryDataValue.setDataCode(deliveryDataBO.getDataCode());
            deliveryDataValue.setDataValue(deliveryDataBO.getDataValue());
            deliveryDataValue.setCreateUserId(Objects.isNull(param.getCreateUserId()) ? StringUtils.EMPTY : param.getCreateUserId());
            deliveryDataValue.setUpdateUserId(
                Objects.isNull(param.getUpdateUserId()) ? StringUtils.EMPTY : param.getUpdateUserId());
            deliveryDataValueList.add(deliveryDataValue);
        }
        return deliveryDataValueRepository.saveAndUpdateBatch(deliveryDataValueList);
    }


    /**
     * 查询数据字典值
     * @param processInsId  当前运行的流程实例id
     * @param dataCode 数据定义dataCode
     */
    public DeliveryDataValueRespBO.DetailBO getDeliveryDataValue(String processInsId, String dataCode) {
        DeliveryDataValue deliveryDataValue = deliveryDataValueRepository.getDeliveryDataValue(processInsId, dataCode);
        return DeliveryDataValueDomainMapping.INSTANCE.deliveryDataValuePO2DetailBO(deliveryDataValue);
    }


    public Map<String, String> queryDeliveryDataValueMap(String processInsId, List<String> dataCodes) {
        return Option.when(CharSequenceUtil.isNotBlank(processInsId) && CollUtil.isNotEmpty(dataCodes), () -> Try.of(() -> queryDeliveryDataByCodes(processInsId, dataCodes))
            .map(transToMap)
            .onFailure(e -> {
                log.error("query delivery data failed", e);
            })
            .getOrElse(HashMap::new)).getOrElse(HashMap::new);
    }

    public Map<String, String> queryDeliveryDataValueMapWithOrderItem(String orderItemId, List<String> dataCodes) {
        return Option.when(CharSequenceUtil.isNotBlank(orderItemId) && CollUtil.isNotEmpty(dataCodes), () -> Try.of(() -> queryDeliveryDataByOrderItemAndCodes(orderItemId, dataCodes))
           .map(transToMap).onFailure(e -> {
                    log.error("query delivery data failed", e);
                })
                .getOrElse(HashMap::new)).getOrElse(HashMap::new);
    }

    public String queryDeliveryDataValue(String processInsId, String key) {
        return Try.of(() -> queryDeliveryDataByCodes(processInsId, List.of(key)))
            .map(transToMap)
            .onFailure(e -> {
                log.error("query delivery data  value failed", e);
            }).map(map-> map.getOrDefault(key, Strings.EMPTY)).getOrElse(()-> Strings.EMPTY);
    }


    private final Function<List<DeliveryDataValue>, Map<String, String>> transToMap =
        func -> Option.when(CollUtil.isNotEmpty(func), () -> func.stream()
            .map(DeliveryDataValueDomainMapping.INSTANCE::PO2DetailBO)
            .filter(detail -> StringUtils.isNotBlank(detail.getCode())).collect(
            Collectors.toMap(DeliveryDataDetailBO::getCode,
                    detail -> StringUtils.isNotBlank(detail.getValue()) ? detail.getValue() : Strings.EMPTY
            ))).getOrElse(HashMap::new);


    public List<DeliveryDataValue> queryDeliveryDataByOrderItemAndCodes(String orderItemId, List<String> codes) {
        if(CollUtil.isEmpty(codes)){
            return deliveryDataValueRepository.queryAllByOrderItemId(orderItemId);
        }
        return deliveryDataValueRepository.queryDeliveryDataByCodes(orderItemId, codes);
    }

    public List<DeliveryDataValue> queryDeliveryDataByCodes(String processInsId, List<String> codes) {
        return deliveryDataValueRepository.queryDeliveryDataByCodesWithInsId(processInsId, codes);
    }


    public List<DeliveryDataValue> queryAllByProcessInsId(String processInsId) {
        return deliveryDataValueRepository.queryAllByProcessInsId(processInsId);
    }

}
