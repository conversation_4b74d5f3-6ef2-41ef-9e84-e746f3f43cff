package com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.card;

import java.util.ArrayList;
import java.util.List;

import com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.card.chain.CardHandleChainI;
import com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.card.models.CardSendData;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CardHandleChainSlot {

    private final List<CardHandleChainI> list = new ArrayList<>();

    public void addLast(CardHandleChainI cardHandleChain) {
        list.add(cardHandleChain);
    }

    public void handle(CardSendData cardSendData) {
        list.forEach(cardHandleChain -> cardHandleChain.handle(cardSendData));
    }

}
