package com.pxb7.mall.trade.ofs.delivery.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 卡片关联组件(CardComponentsConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:41:31
 */
public class CardComponentsConfigReqBO {



    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SearchBO {

        private Long cardConfigId;

        private Long componentConfigId;

        private String createUserId;

        private String updateUserId;

    }

}
