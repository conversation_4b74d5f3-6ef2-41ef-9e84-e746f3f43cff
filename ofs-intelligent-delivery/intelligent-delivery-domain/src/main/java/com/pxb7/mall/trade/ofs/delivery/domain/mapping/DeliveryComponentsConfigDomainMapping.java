package com.pxb7.mall.trade.ofs.delivery.domain.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryComponentsConfigRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryComponentsConfig;

@Mapper
public interface DeliveryComponentsConfigDomainMapping {

    DeliveryComponentsConfigDomainMapping INSTANCE = Mappers.getMapper(DeliveryComponentsConfigDomainMapping.class);

    DeliveryComponentsConfigRespBO.DetailBO deliveryComponentsConfigPO2DetailBO(DeliveryComponentsConfig source);

    List<DeliveryComponentsConfigRespBO.DetailBO> deliveryComponentsConfigPO2DetailBOList(List<DeliveryComponentsConfig> sourceList);

    List<DeliveryComponentsConfigRespBO.DetailBO>
        deliveryComponentsConfigPO2ListBO(List<DeliveryComponentsConfig> sourceList);

}
