package com.pxb7.mall.trade.ofs.delivery.domain.components.models;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Data
@Accessors(chain = true)
public class ComponentContextData {

    /**
     * 流程实例id
     */
    private String processInsId;

    /**
     * 组件配置信息
     */
    private ComponentData componentData;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 配置ID
     */
    private Long configId;

    /**
     * 前端提交的表单数据字典
     */
    private Map<String, String> deliveryData = new HashMap<>();

    /**
     * 表单样式控制字段
     */
    private Map<String, String> styleData = new HashMap<>();


    private Boolean customerFlag = Boolean.FALSE;


}
