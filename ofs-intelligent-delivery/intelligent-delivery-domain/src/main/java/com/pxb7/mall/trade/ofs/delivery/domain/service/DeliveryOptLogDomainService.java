package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.util.*;
import java.util.stream.Collectors;

import com.pxb7.mall.auth.dto.ImUserDTO;
import com.pxb7.mall.trade.ofs.common.config.util.DeliveryV3ThreadPoolExecutorUtil;
import com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.model.UserInfoResult;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessTaskLog;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ProcessNodeConfig;
import org.springframework.stereotype.Service;

import com.pxb7.mall.camunda.util.StringUtil;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryOptLog;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.DeliveryOptLogMapper;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: heyc
 * @date: 2025/4/9 20:57
 * @Version 1.0
 **/
@Slf4j
@Service
public class DeliveryOptLogDomainService {

    @Resource
    private ProcessNodeConfigDomainService processNodeConfigDomainService;
    @Resource
    private NodeNameConfigDomainService nodeNameConfigDomainService;

    @Resource
    private DeliveryOptLogMapper deliveryOptLogMapper;

    /**
     * 开启
     *
     * @param procInsId:
     * @param gameName:
     * @param version:
     */
    public void start(String procInsId, String gameName, String version) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole((byte)0);
        delivery.setOptType((byte)1);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setOptUserId("0");
        delivery.setOptDesc("进入 " + gameName + " 智能交付，【自动化流程版本号" + version + "】");
        this.save(delivery);
    }

    /**
     * 流程结束
     *
     * @param procInsId:
     */
    public void finish(String procInsId) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole((byte)0);
        delivery.setOptType((byte)2);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setOptUserId("0");
        delivery.setOptDesc("交付流程结束");
        save(delivery);
    }

    public void abort(String processInsId) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(processInsId);
        delivery.setOptRole((byte)0);
        delivery.setOptType((byte)2);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setOptUserId("0");
        delivery.setOptDesc("订单取消结束交付流程");
        save(delivery);
    }

    public void finish(String processInsId, String customerId, String customerName) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(processInsId);
        delivery.setOptRole((byte)3);
        delivery.setOptType((byte)2);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setOptUserId(customerId);
        delivery.setOptDesc(String.format("客服%s手动结束交付流程", customerName));
        save(delivery);
    }

    /**
     * 激活
     *
     * @param procInsId:
     * @param userId:
     * @param userName:
     */
    public void active(String procInsId, String userId, String userName) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole((byte)3);
        delivery.setOptType((byte)6);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setOptUserId(userId);
        delivery.setOptUserName(userName);
        delivery.setOptDesc("客服 " + userName + " 开启 自动化流程");
        save(delivery);
    }

    /**
     * 暂停
     *
     * @param procInsId:
     * @param userId:
     * @param userName:
     */
    public void pause(String procInsId, String userId, String userName) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole((byte)3);
        delivery.setOptType((byte)7);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setOptUserId(userId);
        delivery.setOptUserName(userName);
        delivery.setOptDesc("客服 " + userName + " 暂停 自动化流程");
        save(delivery);
    }

    public void back(ProcessNodeConfig processNodeConfig, String processInsId, List<DeliveryProcessTaskLog> taskLogs,
        String userId, String userName) {
        // 记录日志
        List<Long> rollbackNodeConfigIdList =
            taskLogs.stream().map(DeliveryProcessTaskLog::getProcessNodeConfigId).toList();
        List<ProcessNodeConfig> rollBackNodeConfigList =
            processNodeConfigDomainService.getProcessNodeConfig(rollbackNodeConfigIdList);
        Set<Long> sourceName2Ids =
            rollBackNodeConfigList.stream().map(ProcessNodeConfig::getNode2NameId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> name2Ids = new HashSet<>(sourceName2Ids);
        if (processNodeConfig.getNode2NameId() != null) {
            name2Ids.add(processNodeConfig.getNode2NameId());
        }
        Map<Long, String> nodeNameMap = new HashMap<>();
        if (!name2Ids.isEmpty()) {
            nodeNameMap = nodeNameConfigDomainService.getNodeName(name2Ids);
        }
        List<String> nameList = sourceName2Ids.stream().map(nodeNameMap::get).filter(Objects::nonNull).toList();
        back(processInsId, userId, userName, processNodeConfig.getProcessConfigId(), processNodeConfig.getTaskDefId(),
            String.join(",", nameList), processNodeConfig.getTaskDefId(),
            nodeNameMap.getOrDefault(processNodeConfig.getNode2NameId(), ""));
    }

    /**
     * 回退
     *
     * @param procInsId:
     * @param userId:
     * @param userName:
     * @param nodeConfigId:
     * @param taskDefId:
     * @param nodeName:
     * @param backTaskDefId:
     * @param backNodeName:
     */
    public void back(String procInsId, String userId, String userName, long nodeConfigId, String taskDefId,
        String nodeName, String backTaskDefId, String backNodeName) {
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole((byte)3);
        delivery.setOptType((byte)5);
        delivery.setNodeType((byte)0);
        delivery.setTaskType((byte)0);
        delivery.setNodeConfigId(nodeConfigId);
        delivery.setTaskDefId(taskDefId);
        delivery.setBackTaskDefId(backTaskDefId);
        delivery.setOptUserId(userId);
        delivery.setOptUserName(userName);
        delivery.setOptDesc("客服 " + userName + " 回退 【" + nodeName + "】 节点 到 【" + backNodeName + "】");
        save(delivery);
    }

    public void pass(String processInsId, DeliveryProcessTaskLog taskLog, String userId, String userName) {
        ProcessNodeConfig processNodeConfig =
            processNodeConfigDomainService.getProcessNodeConfig(taskLog.getProcessNodeConfigId());
        Map<Long, String> nodeNameMap = nodeNameConfigDomainService.getNodeName(
            List.of(processNodeConfig.getNode1NameId(), processNodeConfig.getNode2NameId()));
        String nodeName = nodeNameMap.get(processNodeConfig.getNode1NameId()) + "-" + nodeNameMap.get(
            processNodeConfig.getNode2NameId());
        pass(processInsId, userId, userName, nodeName, processNodeConfig.getTaskDefId(), processNodeConfig.getId(),
            processNodeConfig.getNodeType(), processNodeConfig.getTaskType());
    }

    /**
     * 放行
     *
     * @param procInsId:
     * @param userId:
     * @param userName:
     * @param nodeName:
     * @param taskDefId:
     * @param nodeConfigId:
     * @param iNodeType:
     * @param iTaskType:
     */
    public void pass(String procInsId, String userId, String userName, String nodeName, String taskDefId,
        Long nodeConfigId, Integer iNodeType, Integer iTaskType) {
        byte nodeType = toByteValue(iNodeType);
        byte taskType = toByteValue(iTaskType);
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole((byte)3);
        delivery.setOptType((byte)4);
        delivery.setNodeType(nodeType);
        delivery.setTaskType(taskType);
        delivery.setNodeConfigId(nodeConfigId);
        delivery.setTaskDefId(taskDefId);
        delivery.setOptUserId(userId);
        delivery.setOptUserName(userName);
        if (StringUtil.isNotEmpty(getTaskType(taskType)) && nodeType != 3) {
            delivery.setOptDesc(
                "客服 " + userName + " 放行 【" + nodeName + "】 节点 (" + getNodeType(nodeType) + "-" + getTaskType(
                    taskType) + ")");
        } else {
            delivery.setOptDesc("客服 " + userName + " 放行 【" + nodeName + "】 节点 (" + getNodeType(nodeType) + ")");
        }
        save(delivery);
    }

    public void complete(ProcessNodeConfig processNodeConfig, String processInsId, UserInfoResult userInfo) {
        // 查询节点 记录日志
        Map<Long, String> nodeNameMap = nodeNameConfigDomainService.getNodeName(
            List.of(processNodeConfig.getNode1NameId(), processNodeConfig.getNode2NameId()));
        String nodeName = nodeNameMap.get(processNodeConfig.getNode1NameId()) + "-" + nodeNameMap.get(
            processNodeConfig.getNode2NameId());

        String userId = "0";
        int iRole = 0;
        String userName = null;
        if (userInfo != null) {
            userId = userInfo.getUserId();
            userName = userInfo.getUserName();
            iRole = userInfo.getTradeRoleType() == null || userInfo.getCustomerFlag() ? 3 : userInfo.getTradeRoleType();
        }
        complete(processInsId, userId, userName, iRole, nodeName, processNodeConfig.getTaskDefId(),
            processNodeConfig.getId(), processNodeConfig.getNodeType(), processNodeConfig.getTaskType());

    }

    /**
     * 完成
     *
     * @param procInsId:
     * @param userId:
     * @param userName:
     * @param iRole:
     * @param nodeName:
     * @param taskDefId:
     * @param nodeConfigId:
     * @param iNodeType:
     * @param iTaskType:
     */
    public void complete(String procInsId, String userId, String userName, Integer iRole, String nodeName,
        String taskDefId, Long nodeConfigId, Integer iNodeType, Integer iTaskType) {
        byte role = toByteValue(iRole);
        byte nodeType = toByteValue(iNodeType);
        byte taskType = toByteValue(iTaskType);
        DeliveryOptLog delivery = new DeliveryOptLog();
        delivery.setProcInsId(procInsId);
        delivery.setOptRole(role);
        delivery.setOptType((byte)3);
        delivery.setNodeType(nodeType);
        delivery.setTaskType(taskType);
        delivery.setNodeConfigId(nodeConfigId);
        delivery.setTaskDefId(taskDefId);
        delivery.setOptUserId(userId);
        delivery.setOptUserName(userName);
        if (StringUtil.isNotEmpty(getTaskType(taskType))) {
            delivery.setOptDesc(getRole(role) + Objects.requireNonNullElse(userName,
                "") + " 完成 【" + nodeName + "】 节点 (" + getNodeType(nodeType) + "-" + getTaskType(taskType) + ")");
        } else {
            delivery.setOptDesc(getRole(role) + Objects.requireNonNullElse(userName,
                "") + " 完成 【" + nodeName + "】 节点 (" + getNodeType(nodeType) + ")");
        }
        save(delivery);
    }

    private byte toByteValue(Integer intValue) {
        if (intValue == null) {
            return (byte)0;
        }
        return intValue.byteValue();
    }

    private String getTaskType(byte taskType) {
        return switch (taskType) {
            case (byte)0 -> "发消息";
            case (byte)1 -> "发卡片";
            case (byte)2 -> "系统行为";
            default -> "";
        };
    }

    private String getNodeType(byte nodeType) {
        return switch (nodeType) {
            case (byte)1 -> "普通节点";
            case (byte)2 -> "判断节点";
            case (byte)3 -> "人工节点";
            default -> "";
        };
    }

    private String getRole(byte nodeType) {
        return switch (nodeType) {
            case (byte)0 -> "系统";
            case (byte)1 -> "买家";
            case (byte)2 -> "卖家";
            case (byte)3 -> "客服";
            default -> "";
        };
    }

    public void save(DeliveryOptLog deliveryOptLog) {
        try {
            if (deliveryOptLog.getOptUserId() == null) {
                deliveryOptLog.setOptUserId("");
            }
            if (deliveryOptLog.getCreateTime() == null) {
                deliveryOptLog.setCreateTime(new Date());
            }
            deliveryOptLogMapper.insert(deliveryOptLog);
        } catch (Exception e) {
            log.error("保存操作记录异常 {}", deliveryOptLog, e);
        }
    }

}
