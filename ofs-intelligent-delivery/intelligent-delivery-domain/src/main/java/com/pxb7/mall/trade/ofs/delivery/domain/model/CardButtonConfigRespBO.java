package com.pxb7.mall.trade.ofs.delivery.domain.model;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 卡片关联的按钮定义(CardButtonConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-05 20:24:34
 */
public class CardButtonConfigRespBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        /**
         * 自增ID
         */
        private Long id;

        /**
         * 卡片配置id,card_config.id
         */
        private Long cardConfigId;

        /**
         * 卡片按钮的唯一标识
         */
        private String uniquenessId;
        /**
         * 按钮的类型, 1:单按钮的确认, 2: 操作按钮 (多按钮卡片才有)
         */
        private Integer buttonType;
        /**
         * 按钮关联的数据字典里面的枚举的code, 满意度,delivery_data_config.data_code
         */
        private String buttonEnumCode;
        /**
         * 按钮值的code, 满意度的候选项, (满意, 非常满意)
         */
        private String buttonValueCode;
        /**
         * 关联表单的id,form_config.id
         */
        private Long formConfigId;
        /**
         * 创建者
         */
        private String createUserId;
        /**
         * 更新者
         */
        private String updateUserId;
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
        /**
         * 逻辑删除，0:正常，1:删除
         */
        private Boolean deleted;

    }
}
