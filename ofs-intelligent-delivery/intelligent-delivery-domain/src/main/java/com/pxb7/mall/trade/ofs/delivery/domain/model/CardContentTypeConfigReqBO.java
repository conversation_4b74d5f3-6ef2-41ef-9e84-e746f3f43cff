package com.pxb7.mall.trade.ofs.delivery.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * 卡片关联内容外链配置(CardContentTypeConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-06 10:38:12
 */
public class CardContentTypeConfigReqBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {

        private Long cardConfigId;


        private Long contentTypeId;


        private String createUserId;


        private String updateUserId;

    }

}

