package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DataConfigValueStrategy;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * QQ绑定情况组件
 */
@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {ComponentTypeEnum.Constants.B_QQ_BIND_2})
public class QQBindComponent implements ComponentExtPt {

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    private final List<String> cardDisplayCodes = List.of(DeliveryDataCodeEnum.qqBind.getCode());

    private final List<String> formCodes = List.of(
            DeliveryDataCodeEnum.qqBind.getCode(),
            DeliveryDataCodeEnum.qqLoginPwd.getCode(),
            DeliveryDataCodeEnum.qqLoginAccount.getCode()
    );

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return cardDisplayCodes;
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
                    () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), formCodes))
                    .getOrElseThrow(e -> new BizException("", e.getMessage())
                );
    }

    @Override
    public void formCheck(ComponentContextData data) {
        final String qqBind = "qq_binded";
        final String qqBinded = data.getDeliveryData().getOrDefault(DeliveryDataCodeEnum.qqBind.getCode(), Strings.EMPTY);
        if (!Objects.equals(qqBinded, qqBind)) {
            return;
        }
        final String qqLoginPwd = data.getDeliveryData().getOrDefault(DeliveryDataCodeEnum.qqLoginPwd.getCode(), Strings.EMPTY);
        Objects.requireNonNull(DataConfigValueStrategy.Strategy.getStrategy(DeliveryDataCodeEnum.qqLoginPwd.getCode())).check(qqLoginPwd);
        final String qqLoginAccount = data.getDeliveryData().getOrDefault(DeliveryDataCodeEnum.qqLoginAccount.getCode(), Strings.EMPTY);
        Objects.requireNonNull(DataConfigValueStrategy.Strategy.getStrategy(DeliveryDataCodeEnum.qqLoginAccount.getCode())).check(qqLoginAccount);
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), formCodes);
    }
}
