package com.pxb7.mall.trade.ofs.delivery.domain.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ofs.delivery.domain.mapping.FormComponentsRelDomainMapping;
import com.pxb7.mall.trade.ofs.delivery.domain.model.FormComponentsRelReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.FormComponentsRelRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.FormComponentsRelReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.FormComponentsRelRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FormComponentsRel;

import jakarta.annotation.Resource;

/**
 * 表单关联组件domain服务
 *
 * <AUTHOR>
 * @since 2025-02-13 10:40:10
 */
@Service
public class FormComponentsRelDomainService {

    @Resource
    private FormComponentsRelRepository formComponentsRelRepository;

    public FormComponentsRelRespBO.DetailBO findById(Long id) {
        FormComponentsRel entity = formComponentsRelRepository.findById(id);
        return FormComponentsRelDomainMapping.INSTANCE.formComponentsRelPO2DetailBO(entity);
    }

    public List<FormComponentsRelRespBO.DetailBO> list(FormComponentsRelReqBO.SearchBO param) {
        FormComponentsRelReqPO.SearchPO searchPO =
            FormComponentsRelDomainMapping.INSTANCE.formComponentsRelBO2SearchPO(param);
        List<FormComponentsRel> list = formComponentsRelRepository.list(searchPO);
        return FormComponentsRelDomainMapping.INSTANCE.formComponentsRelPO2ListBO(list);
    }

}
