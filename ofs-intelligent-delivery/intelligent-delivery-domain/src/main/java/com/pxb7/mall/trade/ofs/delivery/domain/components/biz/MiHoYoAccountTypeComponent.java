package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 米哈游账号类型组件
 */

@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {ComponentTypeEnum.Constants.B_MIHOYO_ACCOUNT_TYPE_2})
public class MiHoYoAccountTypeComponent  implements ComponentExtPt {


    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;


    private final String KEY_MIHOYO_TYPE = DeliveryDataCodeEnum.mihoyoProductType.getCode();


    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_MIHOYO_TYPE);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
                () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), List.of(KEY_MIHOYO_TYPE))
        ).getOrElseThrow(e-> new BizException("ofs499", e.getMessage()));
    }

    @Override
    public void formCheck(ComponentContextData data) {

    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), List.of(KEY_MIHOYO_TYPE));
    }
}
