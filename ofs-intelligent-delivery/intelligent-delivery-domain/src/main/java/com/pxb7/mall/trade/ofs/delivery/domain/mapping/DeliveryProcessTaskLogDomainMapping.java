package com.pxb7.mall.trade.ofs.delivery.domain.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryProcessTaskLogReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryProcessTaskLogRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.DeliveryProcessTaskLogReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessTaskLog;

@Mapper
public interface DeliveryProcessTaskLogDomainMapping {

    DeliveryProcessTaskLogDomainMapping INSTANCE = Mappers.getMapper(DeliveryProcessTaskLogDomainMapping.class);


    DeliveryProcessTaskLogReqPO.SearchPO deliveryProcessTaskLogBO2SearchPO(DeliveryProcessTaskLogReqBO.SearchBO source);

    List<DeliveryProcessTaskLogRespBO.DetailBO> deliveryProcessTaskLogPO2ListBO(List<DeliveryProcessTaskLog> source);


}
