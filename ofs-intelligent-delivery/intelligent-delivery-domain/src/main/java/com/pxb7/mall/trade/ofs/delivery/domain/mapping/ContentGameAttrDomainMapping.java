package com.pxb7.mall.trade.ofs.delivery.domain.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.delivery.domain.model.ContentGameAttrReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.ContentGameAttrRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.ContentGameAttrReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ContentGameAttr;

@Mapper
public interface ContentGameAttrDomainMapping {

    ContentGameAttrDomainMapping INSTANCE = Mappers.getMapper(ContentGameAttrDomainMapping.class);

    ContentGameAttrReqPO.SearchPO contentGameAttrBO2SearchPO(ContentGameAttrReqBO.SearchBO source);

    ContentGameAttrRespBO.DetailBO contentGameAttrPO2DetailBO(ContentGameAttr source);

    List<ContentGameAttrRespBO.DetailBO> contentGameAttrPO2ListBO(List<ContentGameAttr> source);

}
