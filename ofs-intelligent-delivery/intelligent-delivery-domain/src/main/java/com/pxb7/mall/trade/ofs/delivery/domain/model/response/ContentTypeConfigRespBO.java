package com.pxb7.mall.trade.ofs.delivery.domain.model.response;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 节点管理(ContentTypeConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 16:39:03
 */
public class ContentTypeConfigRespBO {

    @Data
    @Accessors(chain = true)
    public static class ListBO {
        private Long id;
        private String typeName;
        private Integer isValid;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        private Long id;
        private String typeName;
        private Integer isValid;

        private String createUserId;
        private String createUserName;
        private String updateUserId;
        private String updateUserName;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private Boolean deleted;
    }
}
