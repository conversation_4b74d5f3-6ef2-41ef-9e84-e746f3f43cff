package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.FormComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;

/**
 * Tap登录账密组件
 */
@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {ComponentTypeEnum.Constants.B_TAP_LOGIN_ACC_2, ComponentTypeEnum.Constants.B_TAP_BIND_SITUATION_2})
public class TapLoginAccComponent implements ComponentExtPt {

    /**
     * tap绑定情况
     */
    private final String KEY_TAP_BIND_STATUS = DeliveryDataCodeEnum.tapBind.getCode();

    private final String BINDED = "tap_binded";
    private final String UNBINDED = "tap_unbind";


    /**
     * tap登录账号
     */
    private static final String TAP_LOGIN_ACCOUNT_CODE = DeliveryDataCodeEnum.tapLoginAccount.getCode();

    /**
     * tap登录密码
     */
    private static final String TAP_LOGIN_PASSWORD_CODE = DeliveryDataCodeEnum.tapLoginPassword.getCode();

    private static final String ACCOUNT_RELEASE_TYPE = DeliveryDataCodeEnum.accountReleaseType.getCode();

    private static final String TAP_RADIO_STYLE = DeliveryDataCodeEnum.tapRadioStyle.getCode();

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;


    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(TAP_LOGIN_ACCOUNT_CODE, TAP_LOGIN_PASSWORD_CODE);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
            () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(
                contextData.getProcessInsId(),
                List.of(TAP_LOGIN_ACCOUNT_CODE, TAP_LOGIN_PASSWORD_CODE, KEY_TAP_BIND_STATUS, ACCOUNT_RELEASE_TYPE, TAP_RADIO_STYLE)))
            .getOrElseThrow(e -> new BizException("", e.getMessage()
            )
        );
    }

    @Override
    public void formCheck(ComponentContextData data) {
        Map<String, String> formData = data.getDeliveryData();

        checkNonNull(List.of(KEY_TAP_BIND_STATUS), formData);

        if(formData.getOrDefault(KEY_TAP_BIND_STATUS, Strings.EMPTY).equals(UNBINDED)){
           return;
        }

        String tapLoginAccountValue = formData.get(TAP_LOGIN_ACCOUNT_CODE);
        String tapLoginPasswordValue = formData.get(TAP_LOGIN_PASSWORD_CODE);
        if (StringUtils.isNotBlank(tapLoginAccountValue)
                && tapLoginAccountValue.length() > 50) {
            throw new BizException(ErrorCode.TAP_ACC_PASSWD_LENGTH_ERROR.getErrCode(),
                    ErrorCode.TAP_ACC_PASSWD_LENGTH_ERROR.getErrDesc());
        }
        if (StringUtils.isNotBlank(tapLoginPasswordValue)
                && tapLoginPasswordValue.length() > 50) {
            throw new BizException(ErrorCode.TAP_ACC_PASSWD_LENGTH_ERROR.getErrCode(),
                    ErrorCode.TAP_ACC_PASSWD_LENGTH_ERROR.getErrDesc());
        }
        if ((StringUtils.isBlank(tapLoginAccountValue) && StringUtils.isNotBlank(tapLoginPasswordValue))
                || (StringUtils.isNotBlank(tapLoginAccountValue) && StringUtils.isBlank(tapLoginPasswordValue))) {
            throw new BizException(ErrorCode.TAP_ACC_PASSWD_EMPTY_ERROR.getErrCode(),
                    ErrorCode.TAP_ACC_PASSWD_EMPTY_ERROR.getErrDesc());
        }
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        Map<String, String> resMaps = filterFormSubmit(data.getDeliveryData(), List.of(TAP_LOGIN_ACCOUNT_CODE, TAP_LOGIN_PASSWORD_CODE, KEY_TAP_BIND_STATUS, ACCOUNT_RELEASE_TYPE));
        if(resMaps.getOrDefault(KEY_TAP_BIND_STATUS, Strings.EMPTY).equals(UNBINDED)){
            resMaps.put(TAP_LOGIN_ACCOUNT_CODE, Strings.EMPTY);
            resMaps.put(TAP_LOGIN_PASSWORD_CODE, Strings.EMPTY);
        }
        return resMaps;
    }

}
