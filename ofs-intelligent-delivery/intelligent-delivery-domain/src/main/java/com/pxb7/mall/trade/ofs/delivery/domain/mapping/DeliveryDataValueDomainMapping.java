package com.pxb7.mall.trade.ofs.delivery.domain.mapping;

import java.util.List;

import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataDetailBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.DeliveryDataValueRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryDataValue;

@Mapper
public interface DeliveryDataValueDomainMapping {

    DeliveryDataValueDomainMapping INSTANCE = Mappers.getMapper(DeliveryDataValueDomainMapping.class);

    DeliveryDataValueRespBO.DetailBO deliveryDataValuePO2DetailBO(DeliveryDataValue source);

    List<DeliveryDataDetailBO> PO2DetailBOList(List<DeliveryDataValue> deliveryDataValues);

    @Mapping(source = "dataCode", target = "code")
    @Mapping(source = "dataValue", target = "originalJson")
    DeliveryDataDetailBO PO2DetailBO(DeliveryDataValue deliveryDataValue);

    @AfterMapping
    default void initializeDeliveryDataDetailBO(DeliveryDataValue dataValue, @MappingTarget DeliveryDataDetailBO detailBO) {
        detailBO.initialize();
    }
}
