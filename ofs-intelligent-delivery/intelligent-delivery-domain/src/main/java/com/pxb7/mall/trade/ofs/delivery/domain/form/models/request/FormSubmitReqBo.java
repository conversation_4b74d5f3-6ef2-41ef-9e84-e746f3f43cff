package com.pxb7.mall.trade.ofs.delivery.domain.form.models.request;

import com.pxb7.mall.trade.ofs.common.config.models.UserBaseInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FormSubmitReqBo {

    private String processInsId;

    private String taskInsId;

    private Long formConfigId;

    private Long formLogId;

    private Map<String, String> submitData;
    private Map<String, String> ocrData;

    // 组件list
    private List<FormComponentReqBo> formComponentReqBoList;

    /**
     * 按钮id
     */
    private String uniquenessId;

    private Integer buttonType;
    private String buttonEnumCode;
    private String buttonValueCode;

    private UserBaseInfo loginUserInfo;

    private String orderItemId;

    private boolean customerFlag = Boolean.FALSE;

}
