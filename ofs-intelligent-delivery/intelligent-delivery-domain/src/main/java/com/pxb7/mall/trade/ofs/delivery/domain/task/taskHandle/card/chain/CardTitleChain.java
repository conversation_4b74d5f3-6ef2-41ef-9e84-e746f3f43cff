package com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.card.chain;


import com.alibaba.cola.exception.Assert;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.model.TitleConfigRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.TitleConfigDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.card.models.CardSendData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 卡片标题处理
 */
@Service
@Slf4j
public class CardTitleChain implements CardHandleChainI {

    @Resource
    private TitleConfigDomainService titleConfigDomainService;

    @Override
    public void handle(CardSendData cardSendData) {
        TitleConfigRespBO.DetailBO titleRespBO = titleConfigDomainService.findById(cardSendData.getCardSendContent().getTitleConfigId());
        if (Objects.isNull(titleRespBO)) {
           return;
        }
        cardSendData.getCardSendContent().setTitleName(titleRespBO.getTitleName()).setTitleStyleJson(titleRespBO.getTitleStyleJson());
    }

}
