package com.pxb7.mall.trade.ofs.delivery.domain.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.delivery.domain.model.CardContentTypeConfigReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardContentTypeConfigRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.model.CardContentTypeConfigReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.CardContentTypeConfig;

@Mapper
public interface CardContentTypeConfigDomainMapping {

    CardContentTypeConfigDomainMapping INSTANCE = Mappers.getMapper(CardContentTypeConfigDomainMapping.class);

    CardContentTypeConfigReqPO.SearchPO cardContentTypeConfigBO2SearchPO(CardContentTypeConfigReqBO.SearchBO source);

    CardContentTypeConfigRespBO.DetailBO cardContentTypeConfigPO2DetailBO(CardContentTypeConfig source);

    List<CardContentTypeConfigRespBO.DetailBO> cardContentTypeConfigPO2ListBO(List<CardContentTypeConfig> source);

}
