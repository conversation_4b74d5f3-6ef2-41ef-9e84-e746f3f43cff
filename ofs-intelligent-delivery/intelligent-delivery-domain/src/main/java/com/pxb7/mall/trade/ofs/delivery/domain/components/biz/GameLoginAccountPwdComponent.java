package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.FormComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 游戏登录账密码
 */
@Slf4j
@Extensions(
    bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
    useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
    scenario = {ComponentTypeEnum.Constants.B_GAME_LOGIN_PWD_2})
public class GameLoginAccountPwdComponent implements ComponentExtPt {

    public static final String KEY_ACCOUNT = DeliveryDataCodeEnum.gameAccount.getCode();
    public static final String KEY_PWD = DeliveryDataCodeEnum.gameLoginPwd.getCode();

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_ACCOUNT, KEY_PWD);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
            ()-> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), List.of(KEY_ACCOUNT, KEY_PWD))
        ).getOrElseThrow(
            e-> new BizException(e.getMessage())
        );
    }

    @Override
    public void formCheck(ComponentContextData data) {
        Map<String, String> formData = data.getDeliveryData();
        Option.when(CollUtil.isNotEmpty(formData), () -> formData)
            .filter(GameLoginAccountPwdFormVerify::checkAccountPwd);
    }


    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), List.of(KEY_ACCOUNT, KEY_PWD));
    }


}
