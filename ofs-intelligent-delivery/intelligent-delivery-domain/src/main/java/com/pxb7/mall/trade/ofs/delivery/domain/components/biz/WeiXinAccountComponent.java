package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 微信账号组件
 */
@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {"42"}) // 临时使用42，需要在ComponentTypeEnum中添加B_WEIXIN_ACCOUNT_2常量
public class WeiXinAccountComponent implements ComponentExtPt {

    private final String KEY_WEIXIN_ACCOUNT = DeliveryDataCodeEnum.weixinLoginAccount.getCode();

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_WEIXIN_ACCOUNT);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return null;
    }

    @Override
    public void formCheck(ComponentContextData data) {

    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return Map.of();
    }
}
