package com.pxb7.mall.trade.ofs.delivery.domain.model;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 流程管理(ProcessConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:44:43
 */
public class ProcessConfigRespBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class DetailBO {
        private Long id;
        private String gameId;
        private String sceneCode;
        private Integer openProcess;
        private Integer openRobot;
        private String processDefId;
        private Integer processVersion;
        private Integer processStatus;
        private String createUserId;
        private String updateUserId;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private Boolean deleted;
    }
}
