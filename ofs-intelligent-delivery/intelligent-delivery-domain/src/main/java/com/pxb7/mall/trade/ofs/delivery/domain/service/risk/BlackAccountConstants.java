package com.pxb7.mall.trade.ofs.delivery.domain.service.risk;

public class BlackAccountConstants {

    public static final String BLACK_CHECK_FLAG = "blackSellerFlag";
    public static final String BUYER_ROLE_FLAG = "buyerRole";
    public static final String BUYER_REGISTER_PHONE_FLAG = "buyerRegisterPhone";
    public static final String BUYER_ID_NUMBER_FLAG = "buyerIdNumber";
    public static final String SELLER_ROLE_FLAG = "sellerRole";
    public static final String SELLER_REGISTER_PHONE_FLAG = "sellerRegisterPhone";
    public static final String SELLER_ID_NUMBER_FLAG = "sellerIdNumber";
    public static final String SELLER_ALIPAY_ACCOUNT_LIST_FLAG = "sellerAlipayAccountList";
    public static final String SELLER_WECHAT_ACCOUNT_LIST_FLAG = "sellerWechatAccountList";
    public static final String SELLER_BANK_ACCOUNT_LIST_FLAG = "sellerBankAccountList";
    public static final String SELLER_UNKNOWN_ACCOUNT_LIST_FLAG = "sellerUnknownAccountList";
    public static final String GAME_ACCOUNT_LIST_FLAG = "gameAccountList";
    public static final String PRODUCT_NO_FLAG = "productNo";
    public static final String ORDER_NO_FLAG = "orderNo";
    public static final String SELLER_CONTACT_PHONE_FLAG = "sellerContactPhone";

}
