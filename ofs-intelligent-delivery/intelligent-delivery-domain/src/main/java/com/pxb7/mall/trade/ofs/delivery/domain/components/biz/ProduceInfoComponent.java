package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import static org.apache.dubbo.common.constants.ClusterRules.FAIL_SAFE;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.dubbo.config.annotation.DubboReference;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;

import cn.hutool.core.util.ObjectUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品信息组件
 */
@Slf4j
@Extensions(bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
    useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
    scenario = {ComponentTypeEnum.Constants.B_PRODUCE_INFO_2})
public class ProduceInfoComponent implements ComponentExtPt {

    private final String KEY_PRICE = DeliveryDataCodeEnum.price.getCode();

    private final String KEY_PRODUCT_THUMBNAIL = DeliveryDataCodeEnum.productThumbnail.getCode();
    private final String KEY_FINAL_PRICE = DeliveryDataCodeEnum.finalPrice.getCode();
    private final String KEY_PRODUCT_NAME = DeliveryDataCodeEnum.productName.getCode();

    private final List<String> KEYS = List.of(KEY_PRICE, KEY_FINAL_PRICE, KEY_PRODUCT_NAME, KEY_PRODUCT_THUMBNAIL);

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    @DubboReference(cluster = FAIL_SAFE)
    private OrderInfoDubboServiceI orderInfoDubboServiceISafe;

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return KEYS;
    }

    @Override
    public Map<String, String> filterAndFormatCardView(ComponentContextData componentContextData) {

        Map<String, String> resMap = new HashMap<>(1, 1);

        // 根据订单id查询预估到手价
        SingleResponse<OrderInfoDubboRespDTO> orderInfo = orderInfoDubboServiceISafe.getOrderInfo(componentContextData.getOrderItemId());
        if (ObjectUtil.isNotEmpty(orderInfo)) {

            resMap.put(KEY_FINAL_PRICE, orderInfo.getData().getPayoutAmount() + "");
        }

        return resMap;

    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        Map<String, String> dataMap =
                deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), KEYS);

        // 根据订单id查询预估到手价
        SingleResponse<OrderInfoDubboRespDTO> orderInfo = orderInfoDubboServiceISafe.getOrderInfo(contextData.getOrderItemId());
        if (ObjectUtil.isNotEmpty(orderInfo)) {

            dataMap.put(KEY_FINAL_PRICE, orderInfo.getData().getPayoutAmount() + "");
        }

        log.info("[load 商品信息 form data] :{}", JSON.toJSONString(dataMap));
        return new FormDisplayData().setInitDisplayData(dataMap);
    }

    @Override
    public void formCheck(ComponentContextData data) {}

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), KEYS);
    }

}
