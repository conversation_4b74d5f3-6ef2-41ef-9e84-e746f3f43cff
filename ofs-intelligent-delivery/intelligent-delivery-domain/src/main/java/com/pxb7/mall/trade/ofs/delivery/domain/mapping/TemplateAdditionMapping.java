package com.pxb7.mall.trade.ofs.delivery.domain.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.delivery.domain.model.request.TemplateAdditionRespBO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.TemplateAddition;

@Mapper
public interface TemplateAdditionMapping {

    TemplateAdditionMapping INSTANCE = Mappers.getMapper(TemplateAdditionMapping.class);

    List<TemplateAdditionRespBO> toTemplateAdditionRespDTOList(List<TemplateAddition> payChannels);


}
