package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 微信登录账密组件
 */
@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {"40"}) // 临时使用40，需要在ComponentTypeEnum中添加B_WEIXIN_LOGIN_PWD_2常量
public class WeiXinLoginAccountPwdComponent implements ComponentExtPt {

    private final String KEY_WEIXIN_ACCOUNT = DeliveryDataCodeEnum.weixinLoginAccount.getCode();
    private final String KEY_WEIXIN_PWD = DeliveryDataCodeEnum.weixinLoginPwd.getCode();
    private final String KEY_IS_WEIXIN_BIND = DeliveryDataCodeEnum.weixinBind.getCode();
    private final String WEIXIN_BIND_STATUS = "weixin_binded";
    private final String WEIXIN_NO_BIND_STATUS = "weixin_unbind";

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_WEIXIN_ACCOUNT, KEY_WEIXIN_PWD);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
                () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), 
                        List.of(KEY_WEIXIN_ACCOUNT, KEY_WEIXIN_PWD, KEY_IS_WEIXIN_BIND))
        ).getOrElseThrow(e -> new BizException("", e.getMessage()));
    }

    @Override
    public void formCheck(ComponentContextData data) {
        final String weixinBindStatus = data.getDeliveryData().getOrDefault(KEY_IS_WEIXIN_BIND, Strings.EMPTY);
        
        // 如果微信已绑定，则需要验证账号和密码
        if (Objects.equals(weixinBindStatus, WEIXIN_BIND_STATUS)) {
            checkNonNull(List.of(KEY_WEIXIN_ACCOUNT, KEY_WEIXIN_PWD), data.getDeliveryData());
            
            String account = data.getDeliveryData().get(KEY_WEIXIN_ACCOUNT);
            String password = data.getDeliveryData().get(KEY_WEIXIN_PWD);
            
            // 验证微信账号格式（可以是手机号、邮箱或微信号）
            checkWeixinAccountFormat(account);
            
            // 验证密码长度
            checkLength("微信登录密码", password, 6, 20);
        }
    }

    private void checkWeixinAccountFormat(String account) {
        if (account == null || account.trim().isEmpty()) {
            throw new BizException("", "微信账号不能为空");
        }
        
        // 简单的格式验证：手机号、邮箱或微信号
        String trimmedAccount = account.trim();
        boolean isPhone = trimmedAccount.matches("^1[3-9]\\d{9}$");
        boolean isEmail = trimmedAccount.matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");
        boolean isWeixinId = trimmedAccount.matches("^[a-zA-Z][a-zA-Z0-9_-]{5,19}$");
        
        if (!isPhone && !isEmail && !isWeixinId) {
            throw new BizException("", "请输入正确的微信账号（手机号、邮箱或微信号）");
        }
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), List.of(KEY_WEIXIN_ACCOUNT, KEY_WEIXIN_PWD));
    }
}
