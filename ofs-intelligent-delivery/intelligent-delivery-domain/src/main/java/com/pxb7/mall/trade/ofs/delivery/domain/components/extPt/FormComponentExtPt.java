package com.pxb7.mall.trade.ofs.delivery.domain.components.extPt;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.ExtensionPointI;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import io.vavr.CheckedFunction0;
import io.vavr.control.Try;

import java.util.List;
import java.util.Map;


public interface FormComponentExtPt extends ExtensionPointI {

    FormDisplayData initDisplayData(ComponentContextData contextData);

    void formCheck(ComponentContextData data);

    Map<String, String> handleFormData(ComponentContextData data);

    default Try<FormDisplayData> loadFormValue(CheckedFunction0<Map<String, String>> checkedFunction0) {
        return Try.of(checkedFunction0).map(m -> new FormDisplayData().setInitDisplayData(m));
    }

    default void checkNonNull(List<String> keys, Map<String, String> formData) {
        String errorMessage = "%s 不能为空";
        keys.stream().filter(formData::containsKey).forEach(f-> {
            String value = formData.get(f);
            if (CharSequenceUtil.isBlank(value)) {
                throw new BizException(f, errorMessage.formatted(DeliveryDataCodeEnum.getByCode(f).getDesc()) );
            }
        });
    }
}
