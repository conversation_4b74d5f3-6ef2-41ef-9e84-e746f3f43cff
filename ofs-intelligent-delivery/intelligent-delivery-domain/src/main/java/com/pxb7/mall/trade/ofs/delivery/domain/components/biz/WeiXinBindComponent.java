package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DataConfigValueStrategy;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 微信绑定情况组件
 */
@Slf4j
@Extensions(
        bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
        useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
        scenario = {"37"}) // 临时使用37，需要在ComponentTypeEnum中添加B_WEIXIN_BIND_2常量
public class WeiXinBindComponent implements ComponentExtPt {

    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    private final List<String> cardDisplayCodes = List.of(DeliveryDataCodeEnum.weixinBind.getCode());

    private final List<String> formCodes = List.of(
            DeliveryDataCodeEnum.weixinBind.getCode(),
            DeliveryDataCodeEnum.weixinLoginPwd.getCode(),
            DeliveryDataCodeEnum.weixinLoginAccount.getCode()
    );

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return cardDisplayCodes;
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
                    () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), formCodes))
                    .getOrElseThrow(e -> new BizException("", e.getMessage())
                );
    }

    @Override
    public void formCheck(ComponentContextData data) {
        final String weixinBind = "weixin_binded";
        final String weixinBinded = data.getDeliveryData().getOrDefault(DeliveryDataCodeEnum.weixinBind.getCode(), Strings.EMPTY);
        if (!Objects.equals(weixinBinded, weixinBind)) {
            return;
        }
        final String weixinLoginPwd = data.getDeliveryData().getOrDefault(DeliveryDataCodeEnum.weixinLoginPwd.getCode(), Strings.EMPTY);
        Objects.requireNonNull(DataConfigValueStrategy.Strategy.getStrategy(DeliveryDataCodeEnum.weixinLoginPwd.getCode())).check(weixinLoginPwd);
        final String weixinLoginAccount = data.getDeliveryData().getOrDefault(DeliveryDataCodeEnum.weixinLoginAccount.getCode(), Strings.EMPTY);
        Objects.requireNonNull(DataConfigValueStrategy.Strategy.getStrategy(DeliveryDataCodeEnum.weixinLoginAccount.getCode())).check(weixinLoginAccount);
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        return filterFormSubmit(data.getDeliveryData(), formCodes);
    }
}
