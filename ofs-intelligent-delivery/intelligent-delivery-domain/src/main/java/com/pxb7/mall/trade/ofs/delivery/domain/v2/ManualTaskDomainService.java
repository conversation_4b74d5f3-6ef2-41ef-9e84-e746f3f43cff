package com.pxb7.mall.trade.ofs.delivery.domain.v2;

import com.pxb7.mall.trade.ofs.common.config.util.DeliveryV3ThreadPoolExecutorUtil;
import com.pxb7.mall.trade.ofs.delivery.client.dto.response.NodeConfigDetailDTO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryProcessLogDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.service.GroupInfoDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.NodeTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.TaskStatusEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessTaskLog;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class ManualTaskDomainService implements TaskDomainService {

    @Resource
    private DeliveryProcessLogDomainService deliveryProcessLogDomainService;
    @Resource
    private GroupInfoDomainService groupInfoDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execTask(String processInsId, String orderItemId, String taskInsId,
        NodeConfigDetailDTO nodeTaskConfig) {
        if (!NodeTypeEnum.MANUAL.getCode().equals(nodeTaskConfig.getNodeType())) {
            return;
        }
        deliveryProcessLogDomainService.addTaskLog(orderItemId, processInsId, taskInsId, nodeTaskConfig, null, null,
            TaskStatusEnum.CUSTOMER_VERIFY.getValue());
        groupInfoDomainService.sendUpdateNodeNotify(orderItemId);
        DeliveryV3ThreadPoolExecutorUtil.executeTask(()->groupInfoDomainService.sendRemindMsg(orderItemId));

    }
}
