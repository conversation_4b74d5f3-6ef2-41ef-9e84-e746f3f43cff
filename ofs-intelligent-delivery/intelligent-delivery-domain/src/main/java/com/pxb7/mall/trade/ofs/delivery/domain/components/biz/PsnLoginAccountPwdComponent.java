package com.pxb7.mall.trade.ofs.delivery.domain.components.biz;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extensions;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentCaseEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ComponentTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.ComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.extPt.FormComponentExtPt;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.ComponentContextData;
import com.pxb7.mall.trade.ofs.delivery.domain.components.models.FormDisplayData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataValueDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;

/**
 * psn登录账密组件
 */
@Slf4j
@Extensions(
    bizId = {ComponentCaseEnum.Constants.CARD_CASE, ComponentCaseEnum.Constants.FORM_CASE},
    useCase = {ComponentTypeEnum.Constants.BUSINESS_COMPONENT_1},
    scenario = {ComponentTypeEnum.Constants.B_PSN_LOGIN_PWD_2, ComponentTypeEnum.Constants.B_PSN_BIND_SITUATION_2})
public class PsnLoginAccountPwdComponent implements ComponentExtPt {

    private final String KEY_PSN_ACCOUNT = DeliveryDataCodeEnum.psnLoginAccount.getCode();
    private final String KEY_PSN_PWD = DeliveryDataCodeEnum.psnLoginPassword.getCode();
    private final String KEY_IS_PSN_BIND = DeliveryDataCodeEnum.psnBind.getCode();
    private final String PSN_BIND_STATUS = "psn_binded";
    private final String PSN_NO_BIND_STATUS = "psn_unbind";

    private static final String PSN_RADIO_STYLE = DeliveryDataCodeEnum.psnRadioStyle.getCode();


    @Resource
    private DeliveryDataValueDomainService deliveryDataValueDomainService;

    @Override
    public List<String> loadCodes(ComponentContextData contextData) {
        return List.of(KEY_PSN_ACCOUNT, KEY_PSN_PWD);
    }

    @Override
    public FormDisplayData initDisplayData(ComponentContextData contextData) {
        return loadFormValue(
                () -> deliveryDataValueDomainService.queryDeliveryDataValueMap(contextData.getProcessInsId(), List.of(KEY_PSN_ACCOUNT, KEY_PSN_PWD, KEY_IS_PSN_BIND, PSN_RADIO_STYLE))
        ).getOrElseThrow(e -> new BizException("", e.getMessage()));
    }

    @Override
    public void formCheck(ComponentContextData data) {
        Map<String, String> formData = data.getDeliveryData();
        checkNonNull(List.of(KEY_IS_PSN_BIND), formData);
        if (formData.getOrDefault(KEY_IS_PSN_BIND, Strings.EMPTY).equals(PSN_BIND_STATUS)) {
            checkNonNull(List.of(KEY_PSN_ACCOUNT, KEY_PSN_PWD), formData);
        }
    }

    @Override
    public Map<String, String> handleFormData(ComponentContextData data) {
        Map<String, String> resMaps = filterFormSubmit(data.getDeliveryData(), List.of(KEY_PSN_ACCOUNT, KEY_PSN_PWD, KEY_IS_PSN_BIND));
        boolean unBind = resMaps.getOrDefault(KEY_IS_PSN_BIND, Strings.EMPTY).equals(PSN_NO_BIND_STATUS);
        if (unBind) {
            resMaps.put(KEY_PSN_ACCOUNT, Strings.EMPTY);
            resMaps.put(KEY_PSN_PWD, Strings.EMPTY);
        }
        return resMaps;
    }

}
