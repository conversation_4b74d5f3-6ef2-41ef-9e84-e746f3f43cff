package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Announcements;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.GameTradeAnnouncements;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.TradeAlert;

import java.util.List;

/**
 * 游戏关联验号注意事项与交易提醒模板表(GameTradeAnnouncements)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-24 15:16:27
 */
public interface GameTradeAnnouncementsRepository extends IService<GameTradeAnnouncements> {


    TradeAlert getTradeAlert(String gameId, String itemIds);


    Announcements getAnnouncements(String gameId, String itemIds);
}
