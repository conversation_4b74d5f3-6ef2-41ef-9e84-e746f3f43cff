package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 表单关联组件(FormComponentsRel)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:21:15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "form_components_rel")
@ToString
public class FormComponentsRel implements Serializable {
    private static final long serialVersionUID = -84279382917620295L;
    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 表单id,form_config.id
     */
    @TableField(value = "form_config_id")
    private Long formConfigId;
    /**
     * 组件配置id,delivery_components_config.id
     */
    @TableField(value = "component_config_id")
    private Long componentConfigId;
    /**
     * 创建者
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新者
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:正常，1:删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
