package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 卡片关联的倒计时(CardDelayTimeConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04 17:45:40
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "card_delay_time_config")
@ToString
public class CardDelayTimeConfig implements Serializable {
    private static final long serialVersionUID = 760994198950479405L;
    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 卡片配置id,card_config.id
     */
    @TableField(value = "card_config_id")
    private Long cardConfigId;
    /**
     * 倒计时组件的类型, 0: 等待换绑时长, 1买家未确认自动放款时长, 2挂ip
     */
    @TableField(value = "delay_type")
    private Integer delayType;
    /**
     * 倒计时组件排序
     */
    @TableField(value = "sort")
    private Integer sort;
    /**
     * 业务属性编码--和delivery_data_config.data_code保持一致
     */
    @TableField(value = "attr_code")
    private String attrCode;
    /**
     * 创建者
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新者
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:正常，1:删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
