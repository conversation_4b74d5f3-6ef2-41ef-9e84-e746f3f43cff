package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ofs.delivery.infra.model.DelayConfigRespPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DelayConfig;

/**
 * 倒计时表定义(DelayConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-27 20:42:23
 */
@Mapper
public interface DelayConfigMapper extends BaseMapper<DelayConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<DelayConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DelayConfig> entities);

    List<DelayConfigRespPO> findListByGameIdAndDelayType(@Param("gameId") String gameId,
        @Param("delayType") Integer delayType);

    DelayConfigRespPO findAttrOneByGameIdAndDelayTypeAndAttrCodeAndAttrValueCode(@Param("gameId") String gameId,
        @Param("delayType") Integer delayType, @Param("attrCode") String attrCode,
        @Param("attrValueCode") String attrValueCode);

    DelayConfig getDelayConfig(@Param("gameId") String gameId, @Param("delayType") Integer delayType,
        @Param("attrCode") String attrCode, @Param("attrValueCode") String attrValueCode);
}
