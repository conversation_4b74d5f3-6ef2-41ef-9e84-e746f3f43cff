package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 换绑流程节点表(BindingFlowNode)实体类
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "delivery_automation_binding_flow_node")
public class BindingFlowNode implements Serializable {
    private static final long serialVersionUID = 650064209294565815L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * id（业务主键）
     */
    @TableField(value = "binding_flow_node_id")
    private String bindingFlowNodeId;
    /**
     * 换绑模板id
     */
    @TableField(value = "binding_template_id")
    private String bindingTemplateId;
    /**
     * 节点标题
     */
    @TableField(value = "node_name")
    private String nodeName;
    /**
     * 节点操作方,1，买方，2，卖方，3，挂ip，系统等待
     */
    @TableField(value = "node_compere")
    private Integer nodeCompere;
    /**
     * 节点文本说明
     */
    @TableField(value = "node_text")
    private String nodeText;
    /**
     * 节点图片说明,文件地址
     */
    @TableField(value = "node_image")
    private String nodeImage;
    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除 0 未删除 1 已删除
     */
    @TableField(value = "is_deleted")
    private Boolean deleted;

    @TableField(value = "wait_time")
    private Integer waitTime;

}
