package com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.http.dto.response;

import lombok.Data;

/**
 * 网易实名截图识别返回数据
 *
 * <AUTHOR>
 */
@Data
public class NeteaseCheckRespDTO {

    /**
     * 返回数据
     */
    private RespData data;

    /**
     * 错误码，正常：2000
     */
    private int code;

    /**
     * 请求结果
     */
    private boolean success;

    /**
     * 异常信息
     */
    private String message;

    /**
     * 返回数据
     */
    @Data
    public static class RespData {

        /**
         * 输出为："已实名"或"未实名";图片中未能获取，则输出""
         */
        private String netease_realname;

    }
}