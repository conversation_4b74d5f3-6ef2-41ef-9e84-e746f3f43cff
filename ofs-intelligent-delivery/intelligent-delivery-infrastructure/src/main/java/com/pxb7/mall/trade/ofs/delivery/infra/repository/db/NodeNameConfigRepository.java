package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.delivery.infra.model.NodeNameConfigReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.NodeNameConfig;

/**
 * 节点管理(NodeNameConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-13 10:30:32
 */
public interface NodeNameConfigRepository extends IService<NodeNameConfig> {

    boolean insert(NodeNameConfigReqPO.AddPO param);

    boolean deleteById(NodeNameConfigReqPO.DelPO param);

    NodeNameConfig findById(Long id);

    List<NodeNameConfig> list(NodeNameConfigReqPO.SearchPO param);

}
