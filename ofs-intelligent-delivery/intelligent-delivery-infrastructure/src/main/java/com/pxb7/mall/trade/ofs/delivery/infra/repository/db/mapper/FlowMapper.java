package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Flow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交付流程表(Flow)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:20
 */
@Mapper
public interface FlowMapper extends BaseMapper<Flow> {


}
