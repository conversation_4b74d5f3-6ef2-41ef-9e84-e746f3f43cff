package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper;

import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessPauseLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 交付流程暂停记录表(DeliveryProcessPauseLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-13 10:33:35
 */
@Mapper
public interface DeliveryProcessPauseLogMapper extends BaseMapper<DeliveryProcessPauseLog> {
    /**
     * 批量新增数据
     *
     * @param entities List<DeliveryProcessPauseLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DeliveryProcessPauseLog> entities);

}
