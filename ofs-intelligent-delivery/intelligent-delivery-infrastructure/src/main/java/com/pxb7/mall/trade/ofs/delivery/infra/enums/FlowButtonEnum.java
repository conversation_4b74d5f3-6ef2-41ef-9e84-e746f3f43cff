package com.pxb7.mall.trade.ofs.delivery.infra.enums;

import com.alibaba.fastjson2.JSONObject;

public enum FlowButtonEnum {
    BUYER_CONFIRMATION("已阅读同意", "/ofs/web/user/tradeAlertNotarize","delivery_buyer_confirmation","/ofs/web/user/tradeAlertNotarize","ofs/mobile/user/tradeAlertNotarize","/ofs/h5/user/tradeAlertNotarize"),
    SELLER_CONFIRMATION("已阅读同意", "/ofs/web/user/tradeAlertNotarize","delivery_seller_confirmation","/ofs/web/user/tradeAlertNotarize","ofs/mobile/user/tradeAlertNotarize","/ofs/h5/user/tradeAlertNotarize"),
    SELLER_UPDATE("修改", "/ofs/web/support/getServiceReplace","delivery_seller_submit_delivery","/ofs/web/support/getServiceReplace","/im/account_info_submit","AccountInfoSubmitRef"),
    BUYER_INSURANCE("购买", "/ofs/web/user/buyerConfirmFaceInsurance","delivery_buyer_insurance","/ofs/web/user/buyerConfirmFaceInsurance","ofs/mobile/user/buyerConfirmFaceInsurance","/ofs/h5/user/buyerConfirmFaceInsurance"),
    BUYER_ABANDON("不购买", "/ofs/web/user/buyerWaiveFaceInsurance","delivery_buyer_abandon","/ofs/web/user/buyerWaiveFaceInsurance","ofs/mobile/user/buyerWaiveFaceInsurance","/ofs/h5/user/buyerWaiveFaceInsurance"),
    BUYER_PASS("通过", "/ofs/web/user/buyerAuthenticationAccountPass","delivery_buyer_pass","/ofs/web/user/buyerAuthenticationAccountPass","ofs/mobile/user/buyerAuthenticationAccountPass","/ofs/h5/user/buyerAuthenticationAccountPass"),
    BUYER_REJECT("不通过", "/ofs/web/user/buyerAuthenticationAccountReject","delivery_buyer_reject","/ofs/web/user/buyerAuthenticationAccountReject","/ofs/mobile/user/buyerAuthenticationAccountReject","NumberVerificationFailedRef"),
    SELLER_PASS("通过", "/ofs/web/user/sellerAuthenticationAccountPass","delivery_seller_pass","/ofs/web/user/sellerAuthenticationAccountPass","ofs/mobile/user/sellerAuthenticationAccountPass","/ofs/h5/user/sellerAuthenticationAccountPass"),
    SELLER_REJECT("账号有问题", "/ofs/web/user/sellerAuthenticationAccountReject","delivery_seller_reject","/ofs/web/user/sellerAuthenticationAccountReject","ofs/mobile/user/sellerAuthenticationAccountReject","/ofs/h5/user/sellerAuthenticationAccountReject"),
    AUDIT_UPDATE("修改","/ofs/web/user/buyerAuthenticationAccountUpdate","delivery_audit_update","/ofs/web/user/buyerAuthenticationAccountUpdate","/ofs/mobile/user/buyerAuthenticationAccountUpdate","/ofs/h5/user/buyerAuthenticationAccountUpdate"),
    AUDIT_UPDATE_SUBMIT("确认","/ofs/web/user/buyerAuthenticationAccountUpdateSubmit","delivery_audit_update_submit","/ofs/web/user/buyerAuthenticationAccountUpdateSubmit","/ofs/mobile/user/buyerAuthenticationAccountUpdateSubmit","/ofs/h5/user/buyerAuthenticationAccountUpdateSubmit"),
    SAVE_UID("保存", "/ofs/web/user/supportCommitUnique","delivery_save_uid"),
    APPLY_RETAINAGE("申请放款", "order/web/extVoucher/applyExt","delivery_apply_retainage","/user/order/1/{{orderId}}/{{orderItemId}}","order/mobile/extVoucher/applyExt","LoanPopupRef"),
    BUYER_COLLECT("买家资料提交", "http://www.baidu.com","delivery_buyer_collect"),
    SELLER_COLLECT("卖家资料提交", "http://www.baidu.com","delivery_seller_collect"),
    SERVICE_PASS("同意", "/ofs/web/user/supportRefundPass","delivery_service_pass","/ofs/web/user/supportRefundPass","ofs/mobile/user/supportRefundPass","/ofs/h5/user/supportRefundPass"),
    SERVICE_REJECT("拒绝", "/ofs/web/user/supportRefundReject","delivery_service_reject","/ofs/web/user/supportRefundReject","ofs/mobile/user/supportRefundReject","/ofs/h5/user/supportRefundReject"),
    EXCHANGE_BINDING_FINISH("完成", "/ofs/web/user/buyerExchangeBindingFinish","delivery_exchange_binding_finish","/ofs/web/user/buyerExchangeBindingFinish","ofs/mobile/user/buyerExchangeBindingFinish","/ofs/h5/user/buyerExchangeBindingFinish"),
    EXCHANGE_BINDING_NOT_FINISH("未完成", "/ofs/web/user/buyerExchangeBindingNotFinish","delivery_exchange_binding_finish","/ofs/web/user/buyerExchangeBindingNotFinish","ofs/mobile/user/buyerExchangeBindingNotFinish","/ofs/h5/user/buyerExchangeBindingNotFinish"),
    NEXT_NODE("完成", "/ofs/web/user/exchangeBindingNextNode","delivery_next_node","/ofs/web/user/exchangeBindingNextNode","ofs/mobile/user/exchangeBindingNextNode","/ofs/h5/user/exchangeBindingNextNode"),

    HAS_PROBLEM("有疑问？", "/im/web/im/msg/sendReminderMsg","delivery_next_node","/im/web/im/msg/sendReminderMsg","/im/mobile/im/msg/sendReminderMsg","/im/h5/im/msg/sendReminderMsg"),
    SELLER_SUBMIT_DELIVERY("填写资料信息", "/ofs/web/support/getServiceReplace","delivery_seller_submit_delivery","/ofs/web/support/getServiceReplace","/im/account_info_submit","AccountInfoSubmitRef"),

    //普通交付
    SELLER_SUBMIT_ORDINARY("填写资料信息", "/ofs/web/ordinary/getSellerAccountCollectForm","/ofs/web/ordinary/getSellerAccountCollectForm","/ofs/web/ordinary/getSellerAccountCollectForm","/ofs/web/ordinary/getSellerAccountCollectForm","/pages-im/SellerAccountInformationSubmission/index"),

    ;

    private String title;
    private String web;
    private JSONObject parme;
    private String targetType;
    private String pcUrl;
    private String androidUrl;
    private String wapUrl;

    private FlowButtonEnum(String title, String url) {
        this.title = title;
        this.web = url;
    }
    private FlowButtonEnum(String title, String url,JSONObject parme) {
        this.title = title;
        this.web = url;
        this.parme=parme;
    }

    private FlowButtonEnum(String title, String url,JSONObject parme,String targetType) {
        this.title = title;
        this.web = url;
        this.parme=parme;
        this.targetType=targetType;
    }

    private FlowButtonEnum(String title, String url, String targetType,String pcUrl,String androidUrl,String wapUrl) {
        this.title = title;
        this.web = url;
        this.targetType=targetType;
        this.pcUrl = pcUrl;
        this.androidUrl=androidUrl;
        this.wapUrl=wapUrl;
    }

    private FlowButtonEnum(String title, String url,String targetType) {
        this.title = title;
        this.web = url;
        this.targetType=targetType;
    }

    public String web() {
        return this.web;
    }
    public String pcUrl() {
        return this.pcUrl;
    }
    public String androidUrl() {
        return this.androidUrl;
    }
    public String wapUrl() {
        return this.wapUrl;
    }
    public String targetType() {
        return this.targetType;
    }

    public String title() {
        return this.title;
    }

    public JSONObject parme() {
        return this.parme;
    }

    public String getTitle() {
        return this.title;
    }

    public String getWeb() {
        return this.web;
    }
}
