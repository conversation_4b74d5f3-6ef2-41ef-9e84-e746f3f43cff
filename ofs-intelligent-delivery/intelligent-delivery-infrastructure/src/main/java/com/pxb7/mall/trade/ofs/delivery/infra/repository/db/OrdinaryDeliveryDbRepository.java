package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.OrdinaryDelivery;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.OrdinaryDeliveryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 普通交付表服务实现类
 */
@Slf4j
@Repository
public class OrdinaryDeliveryDbRepository extends ServiceImpl<OrdinaryDeliveryMapper, OrdinaryDelivery>
    implements OrdinaryDeliveryRepository {

    @Override
    public OrdinaryDelivery getOrdinaryDeliveryByOrderItemId(String orderItemId) {
        LambdaQueryWrapper<OrdinaryDelivery> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdinaryDelivery::getOrderItemId, orderItemId).eq(OrdinaryDelivery::getDeleted, 0);
        List<OrdinaryDelivery> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);

    }

    @Override
    public void updateByItemId(OrdinaryDelivery ordinaryDelivery) {
        this.lambdaUpdate().eq(OrdinaryDelivery::getOrderItemId, ordinaryDelivery.getOrderItemId())
            .update(ordinaryDelivery);
    }
}
