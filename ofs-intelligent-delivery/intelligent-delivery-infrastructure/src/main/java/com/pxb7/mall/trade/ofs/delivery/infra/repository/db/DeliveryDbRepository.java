package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Delivery;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.DeliveryMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 智能交付配置表(Delivery)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:20
 */
@Slf4j
@Repository
public class DeliveryDbRepository extends ServiceImpl<DeliveryMapper, Delivery> implements DeliveryRepository {

    @Resource
    private DeliveryMapper deliveryMapper;



    @Override
    public boolean isOpenDelivery(String gameId) {
        String openGameIds = deliveryMapper.getOpenGameIds();
        List<String> openGameList = JSONArray.parseArray(openGameIds, String.class);
        return openGameList.contains(gameId);
    }

    public List<String> getOpenGameIds() {
        String openGameIds = deliveryMapper.getOpenGameIds();
        return JSONArray.parseArray(openGameIds, String.class);
    }



    /**
     * 获取开启智能faq的游戏
     */
    public List<String> getDeliveryFAQGameIds() {
        Delivery delivery = this.lambdaQuery().eq(Delivery::getType, 2).one();
        if (delivery == null) {
            return Collections.emptyList();
        }

        return JSONArray.parseArray(delivery.getGameIds(), String.class);
    }
}
