package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.BindingFlowExplain;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.BindingFlowExplainMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 换绑流程说明表(BindingFlowExplain)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:10
 */
@Slf4j
@Repository
public class BindingFlowExplainDbRepository extends ServiceImpl<BindingFlowExplainMapper, BindingFlowExplain>
    implements BindingFlowExplainRepository {

}
