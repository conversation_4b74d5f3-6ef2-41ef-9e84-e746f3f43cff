package com.pxb7.mall.trade.ofs.delivery.infra.enums;

import com.alibaba.cola.exception.BizException;
import io.vavr.API;
import lombok.Getter;

import static io.vavr.API.$;
import static io.vavr.API.Case;

@Getter
 public enum ExampleDiagramTypeEnum {

    OCR_SECONDARY_REAL_NAME(1, "二次实名情况"),
    OCR_GAME_UID_SCREENSHOT(2, "游戏UID截图"),
    OCR_LOGOUT_NETEASE_PAY_SCREENSHOT(3, "网易支付截图")

    ;
    private final Integer code;
    private final String name;

    ExampleDiagramTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExampleDiagramTypeEnum queryByCode(Integer code) {
        for (ExampleDiagramTypeEnum v : ExampleDiagramTypeEnum.values()) {
            if (v.getCode().equals(code)) {
                return v;
            }
        }
        throw new BizException("ofs_example001", "无效的示例图编码");
    }

    public static String getNameByCode(Integer code) {
        return API.Match(code)
            .of(
                Case($(OCR_SECONDARY_REAL_NAME.getCode()), OCR_SECONDARY_REAL_NAME::getName),
                Case($(OCR_GAME_UID_SCREENSHOT.getCode()), OCR_GAME_UID_SCREENSHOT::getName),
                Case($(OCR_LOGOUT_NETEASE_PAY_SCREENSHOT.getCode()), OCR_LOGOUT_NETEASE_PAY_SCREENSHOT::getName),
                Case($(), () -> {
                throw new BizException("ofs_example001", "无效的示例图编码");})
            );
    }
}
