package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.DeliveryInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 交付信息表(DeliveryInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-28 10:35:04
 */
@Slf4j
@Repository
public class DeliveryInfoDbRepository extends ServiceImpl<DeliveryInfoMapper, DeliveryInfo> {

    public void initDeliveryInfo(DeliveryInfo deliveryInfo) {
        String orderItemId = deliveryInfo.getOrderItemId();
        if (this.getDeliveryInfo(orderItemId) != null) {
            log.warn("[the order already exist] ,orderItemId:{}",orderItemId);
            return;
        }
        this.save(deliveryInfo);
    }

    /**
     * 账号更新来源
     *
     * @param orderItemId
     * @param source
     */
    public void updateAccountSource(String orderItemId, Integer source) {
        LambdaUpdateWrapper<DeliveryInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        updateWrapper.set(DeliveryInfo::getAccountSource, source);
        this.update(updateWrapper);
    }

    /**
     * 更新是否续包
     *
     * @param orderItemId
     * @param repackage
     */
    public void updateRepackage(String orderItemId, boolean repackage) {
        LambdaUpdateWrapper<DeliveryInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        updateWrapper.set(DeliveryInfo::isRepackage, repackage);
        this.update(updateWrapper);
    }

    /**
     * 获取交付信息
     *
     * @param orderItemId
     * @return
     */
    public DeliveryInfo getDeliveryInfo(String orderItemId) {
        if (StringUtils.isBlank(orderItemId)) {
            return null;
        }
        LambdaQueryWrapper<DeliveryInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        queryWrapper.last(" limit 1");
        return this.getOne(queryWrapper);
    }

    public void updateGameAccountAndSource(String orderItemId, String gameAccount, Integer source) {
        LambdaUpdateWrapper<DeliveryInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        updateWrapper.set(DeliveryInfo::getAccountSource, source);
        updateWrapper.set(DeliveryInfo::getGameAccount, gameAccount);
        this.update(updateWrapper);
    }

    public void updateAccountUid(String orderItemId, String uid) {
        LambdaUpdateWrapper<DeliveryInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        updateWrapper.set(DeliveryInfo::getAccountUid, uid);
        this.update(updateWrapper);
    }

    public void updateDeliveryInfo(String orderItemId, String gameAccount, Integer source, Boolean repackage) {
        LambdaUpdateWrapper<DeliveryInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        updateWrapper.set(DeliveryInfo::getAccountSource, source);
        updateWrapper.set(DeliveryInfo::getGameAccount, gameAccount);
        repackage = repackage != null && repackage;
        updateWrapper.set(DeliveryInfo::isRepackage, repackage);
        this.update(updateWrapper);
    }

    public void updateUidAndSource(String orderItemId, String accountUid, Integer source) {
        LambdaUpdateWrapper<DeliveryInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeliveryInfo::getOrderItemId, orderItemId);
        updateWrapper.set(DeliveryInfo::getAccountSource, source);
        updateWrapper.set(DeliveryInfo::getAccountUid, accountUid);
        this.update(updateWrapper);
    }

    public List<DeliveryInfo> listDeliveryInfo(List<String> orderItemIdList) {
        LambdaQueryWrapper<DeliveryInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeliveryInfo::getOrderItemId, orderItemIdList);
        return this.list(queryWrapper);
    }
}
