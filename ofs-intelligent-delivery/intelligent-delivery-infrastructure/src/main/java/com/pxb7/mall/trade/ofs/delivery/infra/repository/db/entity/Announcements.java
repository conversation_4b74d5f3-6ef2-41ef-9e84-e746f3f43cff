package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 交易注意事项(Announcements)实体类
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "delivery_automation_announcements")
public class Announcements implements Serializable {
    private static final long serialVersionUID = -75559998604007188L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * id（业务主键）
     */
    @TableField(value = "announcements_id")
    private String announcementsId;
    /**
     * 模板名称
     */
    @TableField(value = "template_name")
    private String templateName;
    /**
     * 模板类型，1、文本，2、图片
     */
    @TableField(value = "template_type")
    private Integer templateType;
    /**
     * 注意事项,类型为图片时传入图片文件路径
     */
    @TableField(value = "advert_matter")
    private String advertMatter;
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除 0 未删除 1 已删除
     */
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
