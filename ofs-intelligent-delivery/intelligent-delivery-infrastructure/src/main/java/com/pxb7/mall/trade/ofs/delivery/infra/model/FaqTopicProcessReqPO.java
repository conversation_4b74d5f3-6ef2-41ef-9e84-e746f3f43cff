package com.pxb7.mall.trade.ofs.delivery.infra.model;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * faq主题流程关联表(FaqTopicProcess)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 11:18:41
 */
public class FaqTopicProcessReqPO {
     
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO  {
    
       
         private Long  faqTopicId;

       
         private Long  processConfigNodeId;

       
         private String  createUserId;

       
         private String  updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {
     
      
         private Long  id;
         
     
      
         private Long  faqTopicId;
         
     
      
         private Long  processConfigNodeId;
         
     
      
         private String  createUserId;
         
     
      
         private String  updateUserId;
         
     
     
     
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public  static class  DelPO{
        private  Long  id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class  SearchPO{
       
         private Long  faqTopicId;

       
         private Long  processConfigNodeId;

       
         private String  createUserId;

       
         private String  updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO{
    
       
         private Long  faqTopicId;

       
         private Long  processConfigNodeId;

       
         private String  createUserId;

       
         private String  updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

