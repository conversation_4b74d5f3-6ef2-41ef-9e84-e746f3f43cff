package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.model.DelayGameAttrReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DelayGameAttr;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.DelayGameAttrMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 倒计时游戏属性关联表(DelayGameAttr)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-27 20:42:23
 */
@Slf4j
@Repository
public class DelayGameAttrDbRepository extends ServiceImpl<DelayGameAttrMapper, DelayGameAttr>
    implements DelayGameAttrRepository {

    @Override
    public DelayGameAttr findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<DelayGameAttr> list(DelayGameAttrReqPO.SearchPO param) {
        LambdaQueryWrapper<DelayGameAttr> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(param.getDelayConfigId())) {
            queryWrapper.eq(DelayGameAttr::getDelayConfigId, param.getDelayConfigId());
        }
        if (StringUtils.isNotBlank(param.getAttrCode())) {
            queryWrapper.eq(DelayGameAttr::getAttrCode, param.getAttrCode());
        }
        if (StringUtils.isNotBlank(param.getAttrValueCode())) {
            queryWrapper.eq(DelayGameAttr::getAttrValueCode, param.getAttrValueCode());
        }
        if (Objects.nonNull(param.getDelayTime())) {
            queryWrapper.eq(DelayGameAttr::getDelayTime, param.getDelayTime());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(DelayGameAttr::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(DelayGameAttr::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

}
