package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.model.ContentGameAttrReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ContentGameAttr;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.ContentGameAttrMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 内容游戏属性配置(ContentGameAttr)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:34:55
 */
@Slf4j
@Repository
public class ContentGameAttrDbRepository extends ServiceImpl<ContentGameAttrMapper, ContentGameAttr>
    implements ContentGameAttrRepository {

    @Override
    public ContentGameAttr findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ContentGameAttr> list(ContentGameAttrReqPO.SearchPO param) {
        LambdaQueryWrapper<ContentGameAttr> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(ContentGameAttr::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getContentConfigId())) {
            queryWrapper.eq(ContentGameAttr::getContentConfigId, param.getContentConfigId());
        }
        if (StringUtils.isNotBlank(param.getGameAttrIds())) {
            queryWrapper.eq(ContentGameAttr::getGameAttrIds, param.getGameAttrIds());
        }
        if (StringUtils.isNotBlank(param.getGameItemIds())) {
            queryWrapper.eq(ContentGameAttr::getGameItemIds, param.getGameItemIds());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(ContentGameAttr::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(ContentGameAttr::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

}
