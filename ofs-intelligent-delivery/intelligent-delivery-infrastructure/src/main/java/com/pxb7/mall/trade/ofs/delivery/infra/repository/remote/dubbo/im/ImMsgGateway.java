package com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.im;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.GroupNotifyMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.SendFormMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendImgMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTableColumnMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.enums.GroupNodeMsgType;
import com.pxb7.mall.im.client.dto.request.msg.ComponentExpansionReqDTO;
import com.pxb7.mall.im.client.dto.request.msg.ComponentMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.msg.DeliveryProgressMsgReqDTO;
import com.pxb7.mall.im.client.enums.RemindVersionEnum;
import com.pxb7.mall.trade.ofs.common.config.util.IntelligentDeliveryThreadPoolExecutorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class ImMsgGateway {

    @DubboReference
    private SendMsgServiceI sendMsgService;
    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgService;

    public void sendDeliveryProgressMsg(String roomId, String sendUserId, int total, int current) {
        DeliveryProgressMsgReqDTO reqDTO = new DeliveryProgressMsgReqDTO();
        reqDTO.setFromUserId(sendUserId);
        reqDTO.setTargetId(roomId);
        reqDTO.setTotal(total);
        reqDTO.setCurrent(current);
        try {
            sendCommonMsgService.sendDeliveryProgressMsg(reqDTO);
        } catch (Exception e) {
            log.error("[An exception occurred while calling SendCommonMsgServiceI#sendRichTextMsg],req:{}", reqDTO);

        }
    }

    public void asyncUpdateExpansion(SetExpansionDTO card) {
        IntelligentDeliveryThreadPoolExecutorUtil.executeTask(() -> sendMsgServiceI.setExpansion(card));
    }

    public boolean sendRichTextMsg(SendRichTextMsgReqDTO reqDTO) {
        try {
            SingleResponse<String> response = sendCommonMsgService.sendRichTextMsg(reqDTO);
            return response.isSuccess();
        } catch (Throwable e) {
            log.error("An exception occurred while calling SendCommonMsgServiceI#sendRichTextMsg,req:{}", reqDTO);
            return false;
        }
    }

    public Boolean sendTextMsg(SendTextMsgReqDTO msgReqDTO) {
        try {
            SingleResponse<Boolean> res = sendMsgServiceI.sendTextMsg(msgReqDTO);
            return res.getData();
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendTextMsg,req:{}", msgReqDTO);
        }

        return Boolean.FALSE;
    }

    public Boolean sendImgMsg(SendImgMsgReqDTO msgReqDTO) {
        try {
            SingleResponse<Boolean> res = sendMsgServiceI.sendImgMsg(msgReqDTO);
            return res.getData();
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendImgMsg,req:{}", msgReqDTO);
        }

        return Boolean.FALSE;
    }

    public boolean sendTableColumnCardMsg(SendTableColumnMsgReqDTO sendTableColumnMsgReqDTO) {
        try {
            IntelligentDeliveryThreadPoolExecutorUtil.executeTask(
                () -> sendCommonMsgService.sendTableColumnCardMsg(sendTableColumnMsgReqDTO));
            return true;
        } catch (Throwable e) {
            log.error("An exception occurred while calling SendCommonMsgServiceI#sendTableColumnCardMsg,req:{}",
                sendTableColumnMsgReqDTO);
            return false;
        }

    }

    public boolean sendFormCardMsg(SendFormMsgReqDTO reqDTO) {
        try {
            sendCommonMsgService.sendFormCardMsg(reqDTO);
            return true;
        } catch (Throwable e) {
            log.error("An exception occurred while calling SendCommonMsgServiceI#sendFormCardMsg ,req:{}", reqDTO);
            return false;
        }

    }

    /**
     * 发送卡片消息
     */
    public String sendComponentMsg(ComponentMsgReqDTO reqDTO) {
        try {
            SingleResponse<String> response = sendCommonMsgService.sendComponentMsg(reqDTO);
            return response.getData();
        } catch (Throwable e) {
            log.error("An exception occurred while calling SendCommonMsgServiceI#sendComponentMsg,req:{}", reqDTO);
            return null;
        }
    }

    /**
     * 催一催提醒客服
     *
     * @param roomId 房间ID
     * @param userId 提醒人
     */
    public void asyncSendRemindMsg(String roomId, String userId, String content, List<String> targetUserIds,
        int isIncludeSender) {
        SendReminderMsgReqDTO reqDTO = new SendReminderMsgReqDTO();
        reqDTO.setGroupId(roomId);
        reqDTO.setUserId(userId);
        reqDTO.setContent(content);
        reqDTO.setTargetUserIds(targetUserIds);
        reqDTO.setIsIncludeSender(isIncludeSender);
        reqDTO.setRemindVersion(RemindVersionEnum.AUTO_DELIVERY_V3);
        IntelligentDeliveryThreadPoolExecutorUtil.executeTask(() -> sendMsgService.sendRemindMsg(reqDTO));
    }


    /**
     * 催一催提醒客服
     *
     * @param roomId 房间ID
     * @param userId 提醒人
     */
    public void asyncSendRemindMsg(String roomId, String userId) {
        SendReminderMsgReqDTO reqDTO = new SendReminderMsgReqDTO();
        reqDTO.setGroupId(roomId);
        reqDTO.setUserId(userId);
        IntelligentDeliveryThreadPoolExecutorUtil.executeTask(() -> sendMsgService.sendRemindMsg(reqDTO));
    }

    /**
     * 通过融云通知手机端通过接口更新节点信息
     *
     * @param serviceId
     * @param roomId
     */
    public void updateNode(String serviceId, String roomId) {
        try {
            sendMsgServiceI.sendGroupNotify(new GroupNotifyMsgReqDTO().setFromUserId(serviceId).setGroupId(roomId)
                .setOperation(GroupNodeMsgType.INTELLIGENT_DELIVERY_NODE_MESSAGE.getValue()));
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendGroupNotify. roomId:{}", roomId, e);
        }
    }

    /**
     * 通过融云通知手机端通过接口更新节点信息 智能交付3.0
     *
     * @param serviceId
     * @param roomId
     */
    public void updateProcessNode(String serviceId, String roomId, String content) {
        try {
            sendMsgServiceI.sendGroupNotify(new GroupNotifyMsgReqDTO().setFromUserId(serviceId).setGroupId(roomId)
                .setOperation(GroupNodeMsgType.UPDATE_PROCESS_NODE.getValue()).setContent(content));
        } catch (Exception e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendGroupNotify. roomId:{}", roomId, e);
        }
    }

    /**
     * 发送群通知消息
     * @param serviceId
     * @param roomId
     * @param operator
     * @param content
     */
    public void sendGroupNotify(String serviceId, String roomId, String operator, String content) {
        try {
            sendMsgServiceI.sendGroupNotify(new GroupNotifyMsgReqDTO().setFromUserId(serviceId).setGroupId(roomId)
                    .setOperation(operator).setContent(content));
        } catch (Exception e) {
            log.error("An exception occurred while calling sendMsgServiceI#sendGroupNotify. roomId:{}", roomId, e);
        }
    }

    public void asyncUpdateNode(String serviceId, String roomId) {

        IntelligentDeliveryThreadPoolExecutorUtil.executeTask(() -> sendMsgServiceI.sendGroupNotify(
            new GroupNotifyMsgReqDTO().setFromUserId(serviceId).setGroupId(roomId)
                .setOperation(GroupNodeMsgType.INTELLIGENT_DELIVERY_NODE_MESSAGE.getValue())));
    }

    public void componentExpansion(ComponentExpansionReqDTO dto) {
        try {
            SingleResponse<String> res = sendCommonMsgService.setComponentExpansion(dto);
            log.info("[im componentExpansion] res: {}", res.getData());
        } catch (Throwable e) {
            log.error("An exception occurred while calling sendMsgServiceI#componentExpansion. roomId:{}",
                dto.getMsgUID(), e);
        }
    }

    public void sendRichTextMsgToUserIds(SendRichTextMsgReqDTO reqDTO) {
        try {
            sendCommonMsgService.sendRichTextMsgToUserIds(reqDTO);
        } catch (Exception e) {
            log.error("An exception occurred while calling SendCommonMsgServiceI#sendRichTextMsgToUserIds,req:{}",
                    JSONObject.toJSONString(reqDTO));
        }

    }
}
