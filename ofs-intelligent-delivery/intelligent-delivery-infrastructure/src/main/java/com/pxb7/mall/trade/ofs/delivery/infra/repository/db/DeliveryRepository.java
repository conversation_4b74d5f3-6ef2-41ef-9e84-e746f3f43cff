package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Delivery;

import java.util.List;

/**
 * 智能交付配置表(Delivery)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:19
 */
public interface DeliveryRepository extends IService<Delivery> {

    boolean isOpenDelivery(String gameId);

    /**
     * 获取开启了 智能faq的所有游戏
     *
     * @return
     */
    List<String> getDeliveryFAQGameIds();

}
