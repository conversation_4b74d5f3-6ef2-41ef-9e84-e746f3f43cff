package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.delivery.infra.model.FormConfigReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FormConfig;

/**
 * 表单定义(FormConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-13 10:20:50
 */
public interface FormConfigRepository extends IService<FormConfig> {

    FormConfig findById(Long id);

    List<FormConfig> list(FormConfigReqPO.SearchPO param);

}
