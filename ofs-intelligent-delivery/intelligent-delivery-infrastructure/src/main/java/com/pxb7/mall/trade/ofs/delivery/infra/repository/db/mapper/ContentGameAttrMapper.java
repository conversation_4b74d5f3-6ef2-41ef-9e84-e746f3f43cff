package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper;

import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ContentGameAttr;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 内容游戏属性配置(ContentGameAttr)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-13 10:34:55
 */
@Mapper
public interface ContentGameAttrMapper extends BaseMapper<ContentGameAttr> {
    /**
     * 批量新增数据
     *
     * @param entities List<ContentGameAttr> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ContentGameAttr> entities);

}
