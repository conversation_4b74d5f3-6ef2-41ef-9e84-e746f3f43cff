package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.CommonComponentDataConfig;

import jakarta.validation.constraints.NotNull;

/**
 * 卡片通用组件字典值(CardCommonComponentDataConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-25 20:22:38
 */
public interface CommonComponentDataConfigRepository extends IService<CommonComponentDataConfig> {

    CommonComponentDataConfig findById(Long id);

    List<CommonComponentDataConfig> queryCommonComponentConfig(@NotNull Long configId, Integer useCaseId, Integer componentType);

}
