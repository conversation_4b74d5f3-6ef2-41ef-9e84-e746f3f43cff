package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 组件表定义(DeliveryComponentsConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:16:29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "delivery_components_config")
@ToString
public class DeliveryComponentsConfig implements Serializable {
    private static final long serialVersionUID = -65063156355510814L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 组件的名字
     */
    @TableField(value = "component_name")
    private String componentName;
    /**
     * 组件一级类型, 1:通用组件,2:业务组件,3:OCR组件,4:定制组件
     */
    @TableField(value = "component_type_level1")
    private Integer componentTypeLevel1;
    /**
     * 组件二级类型, 业务组件里面 具体那个业务类型, OCR组件里面的那个
     */
    @TableField(value = "component_type_level2")
    private Integer componentTypeLevel2;
    /**
     * 组件描述
     */
    @TableField(value = "component_desc")
    private String componentDesc;
    /**
     * 前端组件定义的卡片样式json
     */
    @TableField(value = "card_style_json")
    private String cardStyleJson;

    /**
     * 前端组件定义的表单样式json
     */
    @TableField(value = "form_style_json")
    private String formStyleJson;

    /**
     * 数据字典定义,delivery_data_config.data_code
     */
    @TableField(value = "data_config")
    private String dataConfig;
    /**
     * 组件的使用场景, 1:卡片, 2:表单, 3卡片和表单
     */
    @TableField(value = "component_use_case")
    private Integer componentUseCase;
    /**
     * 组件的排序值
     */
    @TableField(value = "sort_value")
    private Integer sortValue;
    /**
     * 创建者
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新者
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:正常，1:删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
