package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FormConfigSnapshot;

/**
 * 表单快照定义(FormConfigSnapshot)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-27 16:33:09
 */
@Mapper
public interface FormConfigSnapshotMapper extends BaseMapper<FormConfigSnapshot> {
    /**
     * 批量新增数据
     *
     * @param entities List<FormConfigSnapshot> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FormConfigSnapshot> entities);

}
