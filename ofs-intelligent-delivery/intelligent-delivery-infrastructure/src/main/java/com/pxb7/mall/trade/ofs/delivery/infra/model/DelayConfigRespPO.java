package com.pxb7.mall.trade.ofs.delivery.infra.model;

import com.pxb7.mall.trade.ofs.delivery.infra.enums.DelayTypeEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/02/28 14:47
 **/
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class DelayConfigRespPO {

    /**
     * 倒计时组件的类型, 0: 等待换绑时长, 1买家未确认自动换绑时长, 2挂ip
     * 
     * @see DelayTypeEnum
     */
    private Integer delayType;
    /**
     * 等待时长(分钟)
     */
    private Integer delayTime;
    /**
     * 游戏id, 0是默认游戏, 一个类型的默认游戏只能一个
     */
    private String gameId;
    /**
     * 是否关联游戏属性, 0没有关联, 1关联
     */
    private Boolean relGameAttr;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 属性
     */
    private String attrCode;
    /**
     * 属性值
     */
    private String attrValueCode;
}
