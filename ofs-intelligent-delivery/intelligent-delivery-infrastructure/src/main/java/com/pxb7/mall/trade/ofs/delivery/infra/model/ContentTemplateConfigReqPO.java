package com.pxb7.mall.trade.ofs.delivery.infra.model;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 内容配置(ContentTemplateConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:34:28
 */
public class ContentTemplateConfigReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SearchPO {

        private Long typeId;

        private String contentName;

        private String contentTitle;

        private Integer gameAttrRel;

        private String gameIds;

        private String showType;

        private String content;

        private String createUserId;

        private String updateUserId;

    }

}
