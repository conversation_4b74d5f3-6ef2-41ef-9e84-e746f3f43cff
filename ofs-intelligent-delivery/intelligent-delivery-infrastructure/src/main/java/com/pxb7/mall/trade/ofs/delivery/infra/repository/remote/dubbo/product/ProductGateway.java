package com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.product;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.product.client.api.ProductMappingAttrServiceI;
import com.pxb7.mall.product.client.api.ProductServiceI;
import com.pxb7.mall.product.client.api.admin.DeliveryBizAttrServiceI;
import com.pxb7.mall.product.client.dto.response.delivery.ProductMappingAttrRespDTO;
import com.pxb7.mall.product.client.dto.response.product.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class ProductGateway {

    @DubboReference
    private ProductServiceI productServiceI;

    @DubboReference
    private ProductMappingAttrServiceI productMappingAttrServiceI;

    @DubboReference
    private DeliveryBizAttrServiceI deliveryBizAttrServiceI;

    public GameAccountSourceRespDTO getGameAccountAndSource(String productId) {

        try {
            SingleResponse<GameAccountSourceRespDTO> response = productServiceI.getGameAccountAndSource(productId);
            if (response == null || !response.isSuccess()) {
                log.warn(
                    "Failed to retrieve product data from the productServiceI#getGameAccountAndSource,productId:{},errorCode:{},errorMsg:{}",
                    productId, response.getErrCode(), response.getErrMessage());
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("An exception occurred while calling productServiceI#getGameAccountAndSource,productId:{}",
                productId, e);
            return null;
        }

    }

    public ProductRespDTO getProduct(String productId) {

        List<ProductRespDTO> productList = getProduct(List.of(productId));
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }
        return productList.get(0);

    }

    public List<ProductRespDTO> getProduct(Collection<String> productIds) {
        try {
            MultiResponse<ProductRespDTO> response = productServiceI.selectList(new ArrayList<>(productIds));
            if (response.isSuccess()) {
                return response.getData();
            }
            log.warn(
                "Failed to retrieve product data from the productServiceI#selectList,productIds:{},errorCode:{},errorMsg:{}",
                JSONObject.toJSONString(productIds), response.getErrCode(), response.getErrMessage());
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("An exception occurred while calling productServiceI#selectList.productIds:{}",
                JSONObject.toJSONString(productIds), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据商品ID，操作类型，查询商品属性ID
     *
     * @param productId  商品ID
     * @param optionType 操作类型 0 全部 1 单选 2 多选 3 文本
     * @return
     */
    public List<ProductAttrIdRespDTO> getProductAttrList(String productId, Integer optionType) {
        try {
            MultiResponse<ProductAttrIdRespDTO> response = productServiceI.selectAttrIdList(productId, optionType);
            if (response.isSuccess()) {
                return response.getData();
            }
            log.warn(
                "Fail to retrieve data from the productServiceI#selectAttrIdList,productId:{},optionType:{},errorCode:{},errorMsg:{}",
                productId, optionType, response.getErrCode(), response.getErrMessage());
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("An exception occurred while calling productServiceI#selectAttrIdList.productId:{},optionType:{}",
                productId, optionType, e);
            return new ArrayList<>();
        }

    }

    /**
     * 根据商品id 查询映射过的商品交付数据
     * @param productId 商品id
     */
    public List<ProductMappingAttrRespDTO> getProductMappingAttrList(String productId){
        try {
            MultiResponse<ProductMappingAttrRespDTO> response =
                productMappingAttrServiceI.getProductMappingAttrList(productId);
            if (response.isSuccess()) {
                return response.getData();
            }
            log.warn(
                "Fail to retrieve data from the productMappingAttrServiceI#getProductMappingAttrList,productId:{},errorCode:{},errorMsg:{}",
                productId, response.getErrCode(), response.getErrMessage());
            return null;
        } catch (Exception e) {
            log.error("An exception occurred while calling productMappingAttrServiceI#getProductMappingAttrList.productId:{},",
                productId, e);
            return null;
        }
    }

    /**
     * 查询业务属性值信息
     *
     * @param bizAttrValueIds 业务属性值IDs
     */
    public List<BizAttrValueRpcRespDTO> getBizAttrValueList(List<Long> bizAttrValueIds){
        try {
            MultiResponse<BizAttrValueRpcRespDTO> response =
                    deliveryBizAttrServiceI.getBizAttrValueList(bizAttrValueIds);
            if (response.isSuccess()) {
                return response.getData();
            }
            log.warn(
                    "Fail to retrieve data from the getBizAttrValueList,bizAttrValueIds:{},errorCode:{},errorMsg:{}",
                    bizAttrValueIds, response.getErrCode(), response.getErrMessage());
            return null;
        } catch (Exception e) {
            log.error("An exception occurred while calling getBizAttrValueList.bizAttrValueIds:{},",
                    bizAttrValueIds, e);
            return null;
        }
    }

    /**
     * 获取业务属性信息
     *
     * @param bizAttrIds
     * @return
     */
    public List<BizAttrRpcRespDTO> getBizAttrList(List<Long> bizAttrIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(bizAttrIds)) {
            return Collections.emptyList();
        }

        MultiResponse<BizAttrRpcRespDTO> multiResponse = deliveryBizAttrServiceI.getBizAttrList(bizAttrIds);
        if (!multiResponse.isSuccess() || org.apache.commons.collections4.CollectionUtils.isEmpty(multiResponse.getData())) {
            return Collections.emptyList();
        }

        return multiResponse.getData();
    }
}
