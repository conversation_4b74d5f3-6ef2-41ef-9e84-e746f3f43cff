package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.CommitAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提交账户信息配置表(commitAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:16
 */
@Mapper
public interface CommitAccountMapper extends BaseMapper<CommitAccount> {

    CommitAccount getTemplateByGameId(@Param("gameId") String gameId);
}
