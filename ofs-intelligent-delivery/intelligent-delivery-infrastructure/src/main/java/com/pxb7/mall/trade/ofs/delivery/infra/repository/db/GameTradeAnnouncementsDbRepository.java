package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Announcements;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.GameTradeAnnouncements;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.TradeAlert;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.GameTradeAnnouncementsMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 游戏关联验号注意事项与交易提醒模板表(GameTradeAnnouncements)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-24 15:16:27
 */
@Slf4j
@Repository
public class GameTradeAnnouncementsDbRepository extends
    ServiceImpl<GameTradeAnnouncementsMapper, GameTradeAnnouncements> implements GameTradeAnnouncementsRepository {

    @Resource
    private GameTradeAnnouncementsMapper gameTradeAnnouncementsMapper;


    @Override
    public TradeAlert getTradeAlert(String gameId, String itemIds) {
        return gameTradeAnnouncementsMapper.getTradeAlert(gameId, itemIds);
    }


    @Override
    public Announcements getAnnouncements(String gameId, String itemIds) {
        return gameTradeAnnouncementsMapper.getAnnouncements(gameId, itemIds);
    }
}
