package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.BindingFlowNode;

import java.util.List;

/**
 * 换绑流程节点表(BindingFlowNode)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-20 18:32:12
 */
public interface BindingFlowNodeRepository extends IService<BindingFlowNode> {
    /**
     * 获取换绑流程模板对应的流程节点数据
     * 
     * @param bindingFlowTemplateId
     * @return
     */
    List<BindingFlowNode> getBindingFlowNodeByTemplateId(String bindingFlowTemplateId);


}
