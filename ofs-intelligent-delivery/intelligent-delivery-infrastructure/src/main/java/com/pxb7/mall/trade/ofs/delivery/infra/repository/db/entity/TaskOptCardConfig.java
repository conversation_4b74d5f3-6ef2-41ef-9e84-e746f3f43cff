package com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 发操作卡片任务配置(TaskOptCardConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:32:19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "task_opt_card_config")
@ToString
public class TaskOptCardConfig implements Serializable {
    private static final long serialVersionUID = -53483527696247375L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 流程节点id, process_node_config.id
     */
    @TableField(value = "node_config_id")
    private Long nodeConfigId;
    /**
     * 卡片配置id, card_config.id
     */
    @TableField(value = "card_config_id")
    private Long cardConfigId;
    /**
     * 艾特发送对象 0:无，1：买家，2：卖家
     */
    @TableField(value = "mentioned")
    private String mentioned;
    /**
     * 对买卖家可见 0：买卖家可见，1：买家，2：卖家
     */
    @TableField(value = "show_type")
    private String showType;
    /**
     * 触达策略id
     */
    @TableField(value = "wake_config_id")
    private String wakeConfigId;
    /**
     * 是否唤起客服 0：否，1：是
     */
    @TableField(value = "wake_customer")
    private Boolean wakeCustomer;

    /**
     * 逻辑删除，0:正常，1:删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
