package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.DeliveryResultEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryResult;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.DeliveryResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 交付结果表(DeliveryResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20 19:38:34
 */
@Slf4j
@Repository
public class DeliveryResultDbRepository extends ServiceImpl<DeliveryResultMapper, DeliveryResult> {

    public void addResult(String orderItemId, String serviceId) {
        LambdaQueryWrapper<DeliveryResult> qw = new LambdaQueryWrapper<>();
        qw.eq(DeliveryResult::getOrderItemId, orderItemId);
        long count = this.count(qw);
        if (count > 0) {
            return;
        }
        DeliveryResult deliveryResult = new DeliveryResult();
        deliveryResult.setResult(DeliveryResultEnum.RUNNING.getValue());
        deliveryResult.setOrderItemId(orderItemId);
        deliveryResult.setCustomerId(serviceId);
        this.save(deliveryResult);
    }
}
