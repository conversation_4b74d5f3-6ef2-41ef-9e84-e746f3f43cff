package com.pxb7.mall.trade.ofs.delivery.infra.repository.db;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.delivery.infra.model.CardContentTypeConfigReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.CardContentTypeConfig;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.CardContentTypeConfigMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 卡片关联内容外链配置(CardContentTypeConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-06 10:37:41
 */
@Slf4j
@Repository
public class CardContentTypeConfigDbRepository extends ServiceImpl<CardContentTypeConfigMapper, CardContentTypeConfig> implements CardContentTypeConfigRepository {


    @Override
    public CardContentTypeConfig findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<CardContentTypeConfig> list(CardContentTypeConfigReqPO.SearchPO param) {
        LambdaQueryWrapper<CardContentTypeConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(param.getCardConfigId())) {
            queryWrapper.eq(CardContentTypeConfig::getCardConfigId, param.getCardConfigId());
        }
        if (Objects.nonNull(param.getContentTypeId())) {
            queryWrapper.eq(CardContentTypeConfig::getContentTypeId, param.getContentTypeId());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(CardContentTypeConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(CardContentTypeConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }
}
