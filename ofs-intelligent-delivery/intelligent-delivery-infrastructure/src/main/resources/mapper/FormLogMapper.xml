<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.FormLogMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FormLog">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="form_config_id" jdbcType="BIGINT" property="formConfigId"/>
        <result column="form_submit" jdbcType="VARCHAR" property="formSubmit"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="process_ins_id" jdbcType="VARCHAR" property="processInsId"/>
        <result column="task_ins_id" jdbcType="VARCHAR" property="taskInsId"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        form_log(form_config_id,form_submit,create_user_id,update_user_id,create_time,update_time,is_deleted,process_ins_id,task_ins_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.formConfigId},#{entity.formSubmit},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted},#{entity.processInsId},#{entity.taskInsId})
        </foreach>
    </insert>
</mapper>

