<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.ProcessConfigMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.ProcessConfig">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="game_makers" jdbcType="INTEGER" property="gameMakers"/>
        <result column="scene_code" jdbcType="VARCHAR" property="sceneCode"/>
        <result column="allow_customer" jdbcType="VARCHAR" property="allowCustomer"/>
        <result column="open_process" jdbcType="INTEGER" property="openProcess"/>
        <result column="open_robot" jdbcType="INTEGER" property="openRobot"/>
        <result column="process_def_id" jdbcType="VARCHAR" property="processDefId"/>
        <result column="process_version" jdbcType="INTEGER" property="processVersion"/>
        <result column="process_status" jdbcType="INTEGER" property="processStatus"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        process_config(game_id,allow_customer,game_name,game_makers,scene_code,open_process,open_robot,process_def_id,process_version,process_status,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gameId},#{entity.allowCustomer},#{entity.gameName},#{entity.gameMakers},#{entity.sceneCode},#{entity.openProcess},#{entity.openRobot},#{entity.processDefId},#{entity.processVersion},#{entity.processStatus},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

