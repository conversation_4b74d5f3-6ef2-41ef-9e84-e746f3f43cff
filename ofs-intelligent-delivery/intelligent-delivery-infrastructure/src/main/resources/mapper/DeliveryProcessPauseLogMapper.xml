<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.DeliveryProcessPauseLogMapper">

    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessPauseLog">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="process_ins_id" jdbcType="VARCHAR" property="processInsId"/>
        <result column="pause_time" jdbcType="TIMESTAMP" property="pauseTime"/>
        <result column="exp_time" jdbcType="TIMESTAMP" property="expTime"/>
        <result column="restart_time" jdbcType="TIMESTAMP" property="restartTime"/>
        <result column="notify_user_type" jdbcType="VARCHAR" property="notifyUserType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        delivery_process_pause_log(order_item_id,process_ins_id,pause_time,exp_time,restart_time,notify_user_type,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderItemId},#{entity.processInsId},#{entity.pauseTime},#{entity.expTime},#{entity.restartTime},#{entity.notifyUserType},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

