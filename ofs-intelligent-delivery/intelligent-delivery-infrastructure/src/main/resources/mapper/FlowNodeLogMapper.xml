<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.FlowNodeLogMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FlowNodeLog">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="flow_id" jdbcType="VARCHAR" property="flowId"/>
        <result column="flow_node" jdbcType="VARCHAR" property="flowNode"/>
        <result column="node_value" jdbcType="VARCHAR" property="nodeValue"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="pid" jdbcType="VARBINARY" property="pid"/>
        <result column="is_parent" jdbcType="BOOLEAN" property="parent"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
    </resultMap>


</mapper>

