<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.mapper.CardDelayTimeConfigMapper">

    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.CardDelayTimeConfig">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="card_config_id" jdbcType="INTEGER" property="cardConfigId"/>
        <result column="delay_type" jdbcType="INTEGER" property="delayType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        card_delay_time_config(card_config_id,delay_type,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.cardConfigId},#{entity.delayType},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

