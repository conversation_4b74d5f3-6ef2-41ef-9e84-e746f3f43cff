package com.pxb7.mall.trade.ofs.delivery.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 流程节点任务类型
 */
@Getter
@ToString
public enum ExecTaskTypeEnum {

    CARD_TASK(Constants.CARD_TASK, "卡片任务"),
    SYSTEM_TASK(Constants.SYSTEM_TASK, "系统任务"),
    NORMAL_MESSAGE(Constants.NORMAL_MESSAGE, "普通消息任务");

    private final String code;
    private final String desc;

    ExecTaskTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static class Constants {
        public static final String CARD_TASK = "card_type";
        public static final String SYSTEM_TASK = "system_task";
        public static final String NORMAL_MESSAGE = "normal_message";
    }

}
