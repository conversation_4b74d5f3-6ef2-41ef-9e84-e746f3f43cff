package com.pxb7.mall.trade.ofs.delivery.client.enums;

import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 组件类型定义
 */
@Getter
@ToString
public enum ComponentTypeEnum {

    // 通用的文本组件
    COMMON_TEXT_COMPONENT(Constants.COMMON_COMPONENT_1, "通用组件", Constants.C_TEXT_2, "文本", true, true),
    // 通用的图片组件
    COMMON_PIC_COMPONENT(Constants.COMMON_COMPONENT_1, "通用组件", Constants.C_PIC_2, "图片", true, true),

    //业务组件
    BUSINESS_GAME_NAME(Constants.BUSINESS_COMPONENT_1,  "业务组件", Constants.B_GAME_NAME_2, "游戏名称", true, false),
    BUSINESS_ACCOUNT_INFO(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_PRODUCE_INFO_2, "商品信息", true, true),
    BUSINESS_ACCOUNT_SOURCE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_ACCOUNT_SOURCE_2,"账号来源", true, true),
    BUSINESS_GAME_LOGIN_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_GAME_LOGIN_PWD_2,"游戏登录账密", true, true),

    BUSINESS_TAP_LOGIN_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_TAP_LOGIN_ACC_2,"tap登录账密", true, false),
    BUSINESS_IS_BIND_TAP(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_IS_BIND_TAP_2, "是否绑定tap", true, false),
    BUSINESS_TAP_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_TAP_ACCOUNT_2,"tap账号", true, false),
    BUSINESS_TAP_BIND_SITUATION(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_TAP_BIND_SITUATION_2,"tap绑定情况", false, true),

    BUSINESS_PSN_LOGIN_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_PSN_LOGIN_PWD_2,"psn登录账密", true, false),
    BUSINESS_IS_BIND_PSN(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_IS_BIND_PSN_2, "是否绑定psn", true, false),
    BUSINESS_PSN_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_PSN_ACCOUNT_2,"psn账号", true, false),
    BUSINESS_PSN_BIND_SITUATION(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_PSN_BIND_SITUATION_2,"psn绑定情况", false, true),

    BUSINESS_BIND_OR_SALE_EMAIL(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_BIND_OR_SALE_EMAIL_2,"邮箱出售情况", true, true),
    BUSINESS_SALE_EMAIL_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_SALE_EMAIL_PWD_2, "邮箱账密", true, false),
    BUSINESS_EMAIL_TYPE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_EMAIL_TYPE_2,"邮箱类型", true, false),

    BUSINESS_18_BIT_REBIND_CODE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_18_BIT_REBIND_CODE_2,"18位换绑码", true, false),
    BUSINESS_CAN_PROVIDER_REBIND_CODE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_CAN_PROVIDER_REBIND_CODE_2, "是否提供换绑码", true, false),
    BUSINESS_PROVIDER_REBIND_CODE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_REBIND_CODE_SITUATION_2, "提供换绑码情况", false, true),

    BUSINESS_REBIND_PHONE_NUMBER(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_REBIND_PHONE_NUMBER_2, "换绑手机号", true, false),
    BUSINESS_REBIND_PHONE_NUMBER_DESENSITIZATION_2(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_REBIND_PHONE_NUMBER_DESENSITIZATION_2, "换绑手机号(脱敏)", true, false),


    BUSINESS_UID(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_GAME_UID_2,"UID(游戏唯一标识)", true, true),
    BUSINESS_WEIXIN_PAY_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEIXIN_PAY_PWD_2,"微信支付密码", true, true),
    BUSINESS_GAME_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_GAME_ACCOUNT_2,"游戏账号", true, true),


    BUSINESS_TENCENT_ACCOUNT_TYPE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_TENCENT_ACCOUNT_TYPE_2,"腾讯账号类型", false, true),
    BUSINESS_MIHOYO_ACCOUNT_TYPE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_MIHOYO_ACCOUNT_TYPE_2,"米哈游账号类型", true, true),
    BUSINESS_LOGINED_WEIXIN_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_LOGINED_WEIXIN_ACCOUNT_2,"登录后微信号", true, true),


    BUSINESS_NETEASE_ACCOUNT_TYPE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_NETEASE_ACCOUNT_TYPE_2,"网易账号类型", true, false),

    BUSINESS_GENERAL_ACCOUNT_TYPE(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_GENERAL_ACCOUNT_TYPE_2,"通用账号类型", true, false),

    BUSINESS_WEGAME_LOGIN_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEGAME_PWD_2,"WeGame登录账密", true, false),
    BUSINESS_IS_BIND_WEGAME(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_IS_BIND_WEGAME_2, "是否绑定WeGame", true, false),
    BUSINESS_WEGAME_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEGAME_ACCOUNT_2,"WeGame账号", true, false),
    BUSINESS_WEGAME_BIND_SITUATION(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEGAME_BIND_SITUATION_2,"WeGame绑定情况", false, true),

    BUSINESS_BIND_OR_SALE_INIT_EMAIL(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_BIND_OR_SALE_INIT_EMAIL_2,"初邮出售情况", true, true),

    // 微信
    BUSINESS_WEIXIN_BIND(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEIXIN_BIND_2,"微信绑定情况", true, true),
    BUSINESS_WEIXIN_LOGIN_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEIXIN_LOGIN_ACCOUNT_2,"微信登录账号", true, false),
    BUSINESS_WEIXIN_LOGIN_ACCOUNT_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_WEIXIN_LOGIN_PWD_2,"微信登录密码", true, true),

    BUSINESS_QQ_BIND(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_QQ_BIND_2, "QQ绑定情况", true, true),
    BUSINESS_QQ_LOGIN_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_QQ_LOGIN_ACCOUNT_2, "QQ登陆账号", true, false),
    BUSINESS_QQ_LOGIN_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_QQ_LOGIN_PWD_2, "QQ登陆密码", true, false),

    // Apple相关组件
    BUSINESS_APPLE_BIND(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_APPLE_BIND_2,"Apple绑定情况", true, true),
    BUSINESS_APPLE_LOGIN_ACCOUNT(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_APPLE_LOGIN_ACCOUNT_2,"Apple登录账号", true, false),
    BUSINESS_APPLE_LOGIN_PWD(Constants.BUSINESS_COMPONENT_1, "业务组件", Constants.B_APPLE_LOGIN_PWD_2,"Apple登录密码", true, false),

    //OCR组件
    OCR_SECONDARY_REAL_NAME(Constants.OCR_COMPONENT_1, "OCR组件", Constants.OCR_SECOND_REAL_NAME_2,"二次实名情况", true, true),
    OCR_GAME_UID_SCREENSHOT(Constants.OCR_COMPONENT_1, "OCR组件", Constants.OCR_GAME_UID_SCREENSHOT_2,"游戏UID截图", false, true),
    OCR_LOGOUT_NETEASE_PAY_SCREENSHOT(Constants.OCR_COMPONENT_1, "OCR组件", Constants.OCR_LOGOUT_NETEASE_PAY_SCREENSHOT_2,"注销网易支付截图", true, true),

    ;


    private final String componentType;
    private final String componentTypeDesc;

    private final String scenarioType;
    private final String scenarioTypeDesc;

    private final Boolean useCaseCard;
    private final Boolean useCaseFrom;


    static final Map<String, ComponentTypeEnum> map = new HashMap<>();

    static {
        for (ComponentTypeEnum componentTypeEnum : ComponentTypeEnum.values()) {
            map.put(componentTypeEnum.getComponentType() + Constants.SEPARATOR + componentTypeEnum.getScenarioType(), componentTypeEnum);
        }
    }


    ComponentTypeEnum(String componentType, String componentTypeDesc,
                      String scenarioType, String scenarioTypeDesc,
                      Boolean useCaseCard, Boolean userCaseFrom) {
        this.componentType = componentType;
        this.componentTypeDesc = componentTypeDesc;
        this.scenarioType = scenarioType;
        this.scenarioTypeDesc = scenarioTypeDesc;
        this.useCaseCard = useCaseCard;
        this.useCaseFrom = userCaseFrom;
    }

    public static ComponentTypeEnum convert(String key) {
        return map.get(key);
    }


    public static class Constants {

        public static final String SEPARATOR = "_";

        /**
         * 组件一级分类常量
         */
        // 通用组件
        public static final String COMMON_COMPONENT_1 = "1";
        // 业务组件
        public static final String BUSINESS_COMPONENT_1 = "2";
        // OCR组件
        public static final String OCR_COMPONENT_1 = "3";

        /**
         * 组件二级分类常量
         */
        // -----------------------------------通用组件--------------------------------------
        // 文本
        public static final String C_TEXT_2 = "1";
        // 图片
        public static final String C_PIC_2 = "2";

        // -----------------------------------业务组件--------------------------------------
        // 游戏名称
        public static final String B_GAME_NAME_2 = "1";
        //tap登录账密
        public static final String B_TAP_LOGIN_ACC_2 = "2";

        public static final String B_PRODUCE_INFO_2 = "3";

        public static final String B_ACCOUNT_SOURCE_2 = "4";
        public static final String B_GAME_LOGIN_PWD_2 = "5";
        public static final String B_IS_BIND_TAP_2 = "6";
        public static final String B_PSN_LOGIN_PWD_2 = "7";
        public static final String B_IS_BIND_PSN_2 = "8";
        public static final String B_BIND_OR_SALE_EMAIL_2 = "9";
        public static final String B_SALE_EMAIL_PWD_2 = "10";
        public static final String B_18_BIT_REBIND_CODE_2 = "11";
        public static final String B_CAN_PROVIDER_REBIND_CODE_2 = "12";
        public static final String B_REBIND_PHONE_NUMBER_2 = "13";
        public static final String B_GAME_UID_2 = "14";
        public static final String B_WEIXIN_PAY_PWD_2 = "15";

        public static final String B_GAME_ACCOUNT_2 = "16";

        public static final String B_TAP_ACCOUNT_2 = "17";

        public static final String B_PSN_ACCOUNT_2 = "18";

        public static final String B_EMAIL_TYPE_2 = "19";

        public static final String B_TENCENT_ACCOUNT_TYPE_2 = "20";

        public static final String B_TAP_BIND_SITUATION_2 = "21";

        public static final String B_PSN_BIND_SITUATION_2 = "22";

        public static final String B_REBIND_CODE_SITUATION_2 = "23";

        public static final String B_REBIND_PHONE_NUMBER_DESENSITIZATION_2 = "24";

        public static final String B_MIHOYO_ACCOUNT_TYPE_2 = "25";

        public static final String B_LOGINED_WEIXIN_ACCOUNT_2 = "26";

        public static final String B_NETEASE_ACCOUNT_TYPE_2 = "27";

        public static final String B_GENERAL_ACCOUNT_TYPE_2 = "28";

        public static final String B_IS_BIND_WEGAME_2 = "29";

        public static final String B_WEGAME_ACCOUNT_2 = "30";

        public static final String B_WEGAME_PWD_2 = "31";

        public static final String B_WEGAME_BIND_SITUATION_2 = "32";

        public static final String B_BIND_OR_SALE_INIT_EMAIL_2 = "33";

        //QQ绑定情况
        public static final String B_QQ_BIND_2 = "34";
        public static final String B_QQ_LOGIN_ACCOUNT_2 = "35";
        public static final String B_QQ_LOGIN_PWD_2 = "36";

        //微信相关组件
        public static final String B_WEIXIN_BIND_2 = "37";
        public static final String B_WEIXIN_LOGIN_ACCOUNT_2 = "38";
        public static final String B_WEIXIN_LOGIN_PWD_2 = "39";

        //Apple相关组件
        public static final String B_APPLE_BIND_2 = "40";
        public static final String B_APPLE_LOGIN_ACCOUNT_2 = "41";
        public static final String B_APPLE_LOGIN_PWD_2 = "42";


        // -----------------------------------OCR组件--------------------------------------
        // 二次实名组件
        public static final String OCR_SECOND_REAL_NAME_2 = "1";
        //UID截图组件
        public static final String OCR_GAME_UID_SCREENSHOT_2 = "2";
        //网易支付注销截图组件
        public static final String OCR_LOGOUT_NETEASE_PAY_SCREENSHOT_2 = "3";

    }

}
