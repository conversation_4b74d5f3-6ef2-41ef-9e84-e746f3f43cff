package com.pxb7.mall.trade.ofs.delivery.client.api;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryInfoDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.DeliveryInfoReqDTO;

public interface DeliveryInfoServiceI {


    SingleResponse<Void> saveDeliveryInfo(DeliveryInfoReqDTO reqDTO);

    SingleResponse<DeliveryInfoDTO> getDeliveryInfo(String orderItemId);

    /**
     * 获取开启了 智能faq的游戏
     *
     * @return
     */
    MultiResponse<String> getDeliveryFAQGameIds();
}
