package com.pxb7.mall.trade.ofs.delivery.client.dto.response;

import lombok.Data;

@Data
public class DeliveryNodeAppDTO {

    /**
     * 节点任务实例ID
     */
    private Long nodeTaskInsId;

    /**
     * 节点配置Id
     */
    private Long nodeConfigId;

    /**
     * 节点Id
     */
    private Long nodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点状态 0:待执行 1:执行中 2:已完成 3:失败
     */
    private Integer status;






}
