package com.pxb7.mall.trade.ofs.delivery.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 用户交易类型类型
 * <AUTHOR>
 */
@Getter
@ToString
public enum TradeRoleTypeEnum {

    /**
     * 1：买家，2：卖家, 3: 买家&卖家
     */

    BUYER(1, "买家"),
    SELLER(2, "卖家"),
    ALL(3, "买家&卖家");

    private final Integer code;
    private final String desc;

    TradeRoleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
