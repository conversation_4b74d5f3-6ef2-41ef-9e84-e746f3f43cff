package com.pxb7.mall.trade.ofs.delivery.client.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * 流程节点任务类型
 */
@Getter
@ToString
public enum FlowTaskTypeEnum {

    NO(0, "无"),
    SEND_CARD(1, "发操作卡片"),
    BLACK_ACCOUNT_CHECK(2, "验黑号");

    private final Integer code;
    private final String desc;

    FlowTaskTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static FlowTaskTypeEnum getByCode(Integer value) {
        for (FlowTaskTypeEnum taskTypeEnum : values()) {
            if (taskTypeEnum.getCode().equals(value)) {
                return taskTypeEnum;
            }
        }
        return null;
    }


}
