package com.pxb7.mall.trade.ofs.delivery.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeliveryTypeEnum {


    ORDINARY(1,"普通交付"),

    AUTOMATION(2,"自动化交付"),

    AUTOMATION_V3(3,"自动化交付3.0")

    ;

    private final Integer code;

    private final String desc;
    
    
    public static DeliveryTypeEnum getByCode(Integer code) {
        for (DeliveryTypeEnum value : DeliveryTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }


}
