package com.pxb7.mall.trade.ofs.delivery.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 数据字典定义
 */
@Getter
@AllArgsConstructor
public enum DeliveryDataCodeEnum {

    buyerUserId("buyerUserId", TypeEnum.STRING, "买家id"),
    sellerUserId("sellerUserId", TypeEnum.STRING, "卖家id"),
    gameName("gameName", TypeEnum.STRING, "游戏名称"),
    productUniqueNo("productUniqueNo", TypeEnum.STRING, "商品编码"),
    orderItemId("orderItemId",  TypeEnum.STRING,"订单编号"),

    productName("productName", TypeEnum.STRING, "商品标题"),
    productThumbnail("productThumbnail", TypeEnum.STRING, "商品主图"),
    price("price", TypeEnum.STRING, "商品原价"),
    finalPrice("finalPrice", TypeEnum.NUMBER, "预估到手价"),
    formTitle("formTitle", TypeEnum.STRING, "表单标题"),
    cardTitle("cardTitle", TypeEnum.STRING, "卡片标题"),
    productSource("productSource", TypeEnum.ENUM, "账号来源"),
    gameAccount("gameAccount", TypeEnum.STRING, "游戏登录账号"),
    gameLoginPwd("gameLoginPwd", TypeEnum.STRING, "游戏登录密码"),
    uid("uid", TypeEnum.STRING, "通行证账号(UID)"),
    buyerUnconfirmedAutoPayout("buyerUnconfirmedAutoPayout", TypeEnum.STRING, "买家未确认自动放款倒计时"),
    waitingBindingChangeReviewTime("waitingBindingChangeReviewTime", TypeEnum.STRING, "等待换绑审核时间倒计时"),
    ipWaitingTime("ipWaitingTime", TypeEnum.STRING, "挂IP等待时间"),
    transactionIntention("transactionIntention", TypeEnum.ENUM, "交易意图"),
    bindingChangeCode18("bindingChangeCode18", TypeEnum.STRING, "18位换绑码"),
    bindingChangeMobile("bindingChangeMobile", TypeEnum.STRING, "换绑手机号"),
    wechatPayPassword("wechatPayPassword", TypeEnum.STRING, "微信支付密码"),
    tapLoginAccount("tapLoginAccount", TypeEnum.STRING, "tap登录账号"),
    tapLoginPassword("tapLoginPassword", TypeEnum.STRING, "tap登录密码"),
    psnLoginAccount("psnLoginAccount", TypeEnum.STRING, "psn登录账号"),
    psnLoginPassword("psnLoginPassword", TypeEnum.STRING, "psn登录密码"),
    emailSaleAccount("emailSaleAccount", TypeEnum.STRING, "出售邮箱账号"),
    emailSalePwd("emailSalePwd", TypeEnum.STRING, "出售邮箱密码"),
    secondRealNameImageUrl("secondRealNameImageUrl", TypeEnum.STRING, "二次实名截图"),
    neteasePayRealName("neteasePayRealName", TypeEnum.ENUM, "网易支付实名情况"),
    bindCDType("bindCDType", TypeEnum.ENUM, "换绑CD情况"),

    uidImageUrl("uidImageUrl", TypeEnum.STRING, "UID截图"),
    txProductType("txProductType", TypeEnum.ENUM, "腾讯账号类型"),
    tapBind("tapBind", TypeEnum.ENUM, "tap绑定情况"),
    psnBind("psnBind", TypeEnum.ENUM, "psn绑定情况"),
    tapRadioStyle("tapRadioStyle", TypeEnum.ENUM, "tap单选样式"),
    psnRadioStyle("psnRadioStyle", TypeEnum.ENUM, "psn单选样式 "),


    emailSale("emailSale", TypeEnum.ENUM, "邮箱出售情况"),
    emailType("emailType", TypeEnum.ENUM, "邮箱情况"),
    provideBindingCode("provideBindingCode", TypeEnum.ENUM, "是否提供换绑码"),
    secondRealName("secondRealName", TypeEnum.ENUM, "二次实名情况"),
    gameId("gameId", TypeEnum.STRING, "游戏ID"),
    accountReleaseType("accountReleaseType", TypeEnum.ENUM, "账号发布类型"),
    riskAccountVerify("riskAccountVerify", TypeEnum.ENUM, "黑号风控校验"),
    //网易支付注销（实名）情况
    netEaseRealNameImageUrl("netEaseRealNameImageUrl", TypeEnum.STRING, "网易支付截图"),
    gameMakers("gameMakers", TypeEnum.ENUM, "游戏厂商"),
    sellerPhone("sellerPhone", TypeEnum.STRING, "卖家联系手机号"),
    bindingChangeMobileSensitive("bindingChangeMobileSensitive", TypeEnum.STRING, "换绑手机号(脱敏)"),
    waitBindTimeIsEnable("waitBindTimeIsEnable", TypeEnum.NUMBER, "等待换绑时长是否可用"),
    autoQuestionTimeIsEnable("autoQuestionTimeIsEnable", TypeEnum.NUMBER, "买家未确认自动换绑时长是否可用"),
    attachIpIsEnable("attachIpIsEnable", TypeEnum.NUMBER, "挂ip是否可用"),
    sellerAccountType("sellerAccountType", TypeEnum.ENUM, "卖家账号类型"),
    hasBindCD("hasBindCD", TypeEnum.ENUM, "是否有换绑CD"),
    loginWechatAccount("loginWechatAccount", TypeEnum.STRING, "登录后微信号"),
    mihoyoProductType("mihoyoProductType", TypeEnum.ENUM, "米哈游账号类型"),

    ntesAccountType("ntesAccountType", TypeEnum.ENUM, "网易账号类型"),
    commonAccountType("commonAccountType", TypeEnum.ENUM, "通用账号类型"),

    repackage("repackage", TypeEnum.ENUM, "是否续包"),


    weGameBind("weGameBind", TypeEnum.ENUM, "weGame绑定情况"),
    weGameLoginAccount("weGameLoginAccount", TypeEnum.STRING, "weGame账号"),
    weGameLoginPassword("weGameLoginPassword", TypeEnum.STRING, "weGame密码"),
    weGameRadioStyle("weGameRadioStyle", TypeEnum.ENUM, "weGame单选样式"),

    initEmailSale("initEmailSale", TypeEnum.ENUM, "初邮出售情况"),

    loginAccountType("loginAccountType", TypeEnum.ENUM, "登录账号类型"),

    qqBind("qqBind", TypeEnum.ENUM, "QQ绑定情况"),
    qqLoginAccount("qqLoginAccount", TypeEnum.STRING, "QQ账号"),
    qqLoginPwd("qqLoginPwd", TypeEnum.STRING, "QQ密码"),

    wechatBind("wechatBind", TypeEnum.ENUM, "微信绑定情况"),
    wechatLoginAccount("wechatLoginAccount", TypeEnum.STRING, "微信账号"),
    weixinLoginPwd("weixinLoginPwd", TypeEnum.STRING, "微信密码"),

    appleBind("appleBind", TypeEnum.ENUM, "apple绑定情况"),
    appleLoginAccount("appleLoginAccount", TypeEnum.STRING, "apple账号"),
    appleLoginPwd("appleLoginPwd", TypeEnum.STRING, "apple密码"),

    ;

    private final String code;
    private final TypeEnum type;
    private final String desc;

    public static DeliveryDataCodeEnum getByCode(String code) {
        for (DeliveryDataCodeEnum deliveryDataCodeEnum : DeliveryDataCodeEnum.values()) {
            if (deliveryDataCodeEnum.getCode().equals(code)) {
                return deliveryDataCodeEnum;
            }
        }
        return null;
    }

    @Getter
    @AllArgsConstructor
    public enum TypeEnum{
        STRING(0, "String"),
        NUMBER(1, "Number"),
        ENUM(2, "Enum"),
        OBJ(4, "Obj"),

        ;
        private final int type;
        private final String desc;

        public static TypeEnum getEnumByType(int type){
            for (TypeEnum e : TypeEnum.values()){
                if (e.getType() == type){
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 内嵌字典值
     * 只在初始化时赋值的字典项,不允许覆盖
     */
    public static Set<DeliveryDataCodeEnum> BUILTIN_DATA_CODES =
        new HashSet<>(Arrays.asList(buyerUserId, sellerUserId, gameName, productUniqueNo, orderItemId));

}
