-- Apple绑定相关配置
-- Apple绑定情况枚举值配置
INSERT INTO delivery_data_config_value (data_code, enum_value, enum_desc, sort_order, create_time, update_time) VALUES
('appleBind', 'apple_binded', 'Apple已绑定', 1, NOW(), NOW()),
('appleBind', 'apple_unbind', 'Apple未绑定', 2, NOW(), NOW()),
('appleBind', 'apple_binded_no_sale', 'Apple已绑定不出售', 3, NOW(), NOW());

-- Apple单选样式配置
INSERT INTO delivery_data_config_value (data_code, enum_value, enum_desc, sort_order, create_time, update_time) VALUES
('appleRadioStyle', 'apple_radio_1', 'Apple单选样式1', 1, NOW(), NOW()),
('appleRadioStyle', 'apple_radio_2', 'Apple单选样式2', 2, NOW(), NOW());

-- Apple相关数据配置
INSERT INTO delivery_data_config (data_code, data_type, data_desc, is_required, max_length, min_length, create_time, update_time) VALUES
('appleBind', 'ENUM', 'Apple绑定情况', 1, NULL, NULL, NOW(), NOW()),
('appleLoginAccount', 'STRING', 'Apple登录账号', 1, 100, 5, NOW(), NOW()),
('appleLoginPwd', 'STRING', 'Apple登录密码', 1, 32, 8, NOW(), NOW()),
('appleRadioStyle', 'ENUM', 'Apple单选样式', 0, NULL, NULL, NOW(), NOW());
