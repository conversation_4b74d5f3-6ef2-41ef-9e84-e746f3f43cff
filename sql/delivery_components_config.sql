INSERT INTO delivery_components_config (component_name,component_type_level1,component_type_level2,component_desc,card_style_json,form_style_json,data_config,component_use_case,sort_value,create_user_id,update_user_id,create_time,update_time,is_deleted) VALUES
	 ('游戏名称',2,1,'游戏名称','[{"key": "gameName", "c_id": "1b07216fdc0047739ace9364952f47c5", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "游戏名称", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 1], "componentTypeLevel1": 1}]',NULL,'["gameName"]',1,1,'***************','***************','2025-03-25 11:50:15.096','2025-04-15 20:06:11.511',0),
	 ('账号来源',2,4,'账号来源','[{"key": "productSource", "c_id": "5a4f88638c6a401dba24f63a428250c8", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "账号来源", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 4], "componentTypeLevel1": 1}]',NULL,'["productSource"]',1,1,'***************','***************','2025-03-25 13:56:09.879','2025-04-15 20:06:11.511',0),
	 ('账号来源',2,4,'账号来源',NULL,'[{"key": "productSource", "c_id": "dec32fa29a75487babe1eef0398e8f63", "type": "radio", "label": {"color": "rgba(51, 51, 51, 1)", "title": "账号来源", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "from_self", "label": "自己注册", "value": "from_self", "tooltip": ""}, {"key": "from_px", "label": "螃蟹平台购买", "value": "from_px", "tooltip": ""}, {"key": "other", "label": "其他", "value": "other", "tooltip": ""}]}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择账号来源", "componentTypeLevel": [2, 4], "componentTypeLevel1": 1}]','["productSource"]',2,1,'***************','***************','2025-03-25 14:07:56.886','2025-04-18 10:39:50.165',0),
	 ('游戏登录帐密',2,5,'游戏登录帐密','[{"key": "gameAccount", "c_id": "a32c0503bdbc4dd08fc58f7f4e5f1a23", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "游戏账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 5], "componentTypeLevel1": 1}, {"key": "gameLoginPwd", "c_id": "c95718d6e27f4811abcf079904640cac", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "游戏密码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 5], "componentTypeLevel1": 1}]',NULL,'["gameAccount", "gameLoginPwd"]',1,1,'***************','***************','2025-03-25 14:12:07.769','2025-04-15 20:06:11.511',0),
	 ('游戏账号',2,16,'游戏账号','[{"key": "gameAccount", "c_id": "b0666d245a494c9cb55722e5b0c0631e", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "游戏账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": true, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 16], "componentTypeLevel1": 1}]',NULL,'["gameAccount"]',1,1,'***************','***************','2025-03-25 14:20:29.214','2025-04-15 20:06:11.511',0),
	 ('是否绑定tap',2,6,'是否绑定tap','[{"key": "tapBind", "c_id": "8bf4dae30ddf44ac9b97dcd6e968e963", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "是否绑定tap", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 6], "componentTypeLevel1": 1}]',NULL,'["tapBind"]',1,1,'***************','***************','2025-03-25 14:22:13.204','2025-04-15 20:06:11.511',0),
	 ('tap登录账密',2,2,'tap登录账密
','[{"key": "tapLoginAccount", "c_id": "130b17eee1184deea8d35acabe1d8d05", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "tap账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 2], "componentTypeLevel1": 1}, {"key": "tapLoginPassword", "c_id": "abf030e16e5145a1aba8e9f566ad250e", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "tap密码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 2], "componentTypeLevel1": 1}]',NULL,'["tapLoginAccount", "tapLoginPassword"]',1,1,'***************','***************','2025-03-25 14:29:09.969','2025-04-15 20:06:11.511',0),
	 ('tap账号',2,17,'tap账号','[{"key": "tapLoginAccount", "c_id": "9f06db92c3204e72b96262bfb1687fa9", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "tap账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 17], "componentTypeLevel1": 1}]',NULL,'["tapLoginAccount"]',1,1,'***************','***************','2025-03-25 14:29:52.112','2025-04-15 20:06:11.511',0),
	 ('是否绑定psn',2,8,'是否绑定psn','[{"key": "psnBind", "c_id": "e647e13b5401480ea7be575122775e7a", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "是否绑定psn", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 8], "componentTypeLevel1": 1}]',NULL,'["psnBind"]',1,1,'***************','***************','2025-03-25 14:34:07.449','2025-04-15 20:06:11.511',0),
	 ('psn登录账密',2,7,'psn登录账密','[{"key": "psnLoginAccount", "c_id": "b67be451e95e45dd837141b25bdfc0a7", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "psn账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": true, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 7], "componentTypeLevel1": 1}, {"key": "psnLoginPassword", "c_id": "d4197cc5cda1439f9cb4b7fca044e957", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "psn密码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": true, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 7], "componentTypeLevel1": 1}]',NULL,'["psnLoginAccount", "psnLoginPassword"]',1,1,'***************','***************','2025-03-25 14:45:21.164','2025-04-15 20:06:11.511',0);
INSERT INTO delivery_components_config (component_name,component_type_level1,component_type_level2,component_desc,card_style_json,form_style_json,data_config,component_use_case,sort_value,create_user_id,update_user_id,create_time,update_time,is_deleted) VALUES
	 ('psn账号',2,18,'psn账号','[{"key": "psnLoginAccount", "c_id": "96b864b9854841f383a916d050112d87", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "psn账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 18], "componentTypeLevel1": 1}]',NULL,'["psnLoginAccount"]',1,1,'***************','***************','2025-03-25 14:46:13.732','2025-04-15 20:06:11.511',0),
	 ('tap绑定情况',2,21,'tap绑定情况',NULL,'[{"c_id": "db55c60736a749bc86cdcd060ad542b1", "type": "text", "label": {"color": "rgba(51, 51, 51, 1)", "title": "tap绑定情况", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "为保障交易，平台约定已绑定tap必须出售", "disabled": {"defaultValue": false}, "required": true, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 21], "componentTypeLevel1": 1}, {"key": "tapBind", "c_id": "1a315a6d7fd246448f76653ff9e4ed0a", "type": "radio", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "tap_unbind", "label": "tap未绑定", "value": "tap_unbind", "tooltip": ""}, {"key": "tap_binded", "label": "tap已绑定", "value": "tap_binded", "tooltip": ""}]}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "tapRadioStyle", "rightKey": "bind", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "绑定", "rightFormKey": "bind"}]}, "defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择tap绑定情况", "componentTypeLevel": [2, 21], "componentTypeLevel1": 1}, {"key": "tapLoginAccount", "c_id": "1255ab7c50b3424794df69aa1354fa34", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入tap账号", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "tapBind", "rightKey": "tap_binded", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "tap已绑定", "rightFormKey": "tap_binded"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "tap账号长度限制5-50位", "mode": "regexp", "regexp": "^.{5,50}$"}, "requiredMessage": "请输入tap账号", "componentTypeLevel": [2, 21], "componentTypeLevel1": 1}, {"key": "tapLoginPassword", "c_id": "f4e00dd923c04180bd37373f677715f5", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "password", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入tap登录密码", "showPassword": true, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "tapBind", "rightKey": "tap_binded", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "tap已绑定", "rightFormKey": "tap_binded"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "tap登录密码长度限制5-50位", "mode": "regexp", "regexp": "^.{5,50}$"}, "requiredMessage": "请输入tap登录密码", "componentTypeLevel": [2, 21], "componentTypeLevel1": 1}]','["tapBind", "tapRadioStyle", "bind", "tapLoginAccount", "tap_binded", "tapLoginPassword"]',2,1,'***************','***************','2025-03-25 14:49:29.292','2025-04-23 15:13:41.249',0),
	 ('邮箱出售情况',2,9,'邮箱出售情况','[{"key": "emailSale", "c_id": "57c7c4eecb58439a945ae43d89278c4f", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "邮箱出售情况", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 9], "componentTypeLevel1": 1}]',NULL,'["emailSale"]',1,1,'***************','***************','2025-03-25 14:50:01.860','2025-04-15 20:06:11.511',0),
	 ('邮箱类型',2,19,'邮箱类型','[{"key": "emailType", "c_id": "d377ed79653940a08ee11b0b6aa7c3b5", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "邮箱类型", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 19], "componentTypeLevel1": 1}]',NULL,'["emailType"]',1,1,'***************','***************','2025-03-25 14:51:52.138','2025-04-15 20:06:11.511',0),
	 ('psn绑定情况',2,22,'psn绑定情况',NULL,'[{"c_id": "6c0029ee89c9420aa31bae150be21291", "type": "text", "label": {"color": "rgba(51, 51, 51, 1)", "title": "psn绑定情况", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"text": "", "color": "rgba(153, 153, 153, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "为保障交易，平台约定已绑定psn必须出售", "disabled": {"defaultValue": false}, "required": true, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 22], "componentTypeLevel1": 1}, {"key": "psnBind", "c_id": "da280519c65647aa89ee571c068fbd9b", "type": "radio", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "psn_unbind", "label": "psn未绑定", "value": "psn_unbind", "tooltip": ""}, {"key": "psn_binded", "label": "psn已绑定", "value": "psn_binded", "tooltip": ""}]}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "psnRadioStyle", "rightKey": "bind", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "绑定", "rightFormKey": "bind"}]}, "defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择psn绑定情况", "componentTypeLevel": [2, 22], "componentTypeLevel1": 1}, {"key": "psnLoginAccount", "c_id": "b03c865976d948d5b8faee165b19b8de", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入psn账号", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "psnBind", "rightKey": "psn_binded", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "psn已绑定", "rightFormKey": "psn_binded"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "psn账号长度限制1-50位", "mode": "regexp", "length": 50, "regexp": "^.{1,50}$"}, "requiredMessage": "请输入psn账号", "componentTypeLevel": [2, 22], "componentTypeLevel1": 1}, {"key": "psnLoginPassword", "c_id": "b3f90a7cb55342fa83ca5a4bd4a000f2", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "password", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入psn登录密码", "showPassword": true, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "psnBind", "rightKey": "psn_binded", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "psn已绑定", "rightFormKey": "psn_binded"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "psn登录密码长度限制1-50位", "mode": "regexp", "regexp": "^.{1,50}$"}, "requiredMessage": "请输入psn登录密码", "componentTypeLevel": [2, 22], "componentTypeLevel1": 1}]','["psnBind", "psnRadioStyle", "bind", "psnLoginAccount", "psn_binded", "psnLoginPassword"]',2,1,'***************','***************','2025-03-25 14:53:41.934','2025-04-21 19:47:26.555',0),
	 ('邮箱账密',2,10,'邮箱账密','[{"key": "emailSaleAccount", "c_id": "9f0cbddc9e9a4a9bb14f5cdfef23a6fa", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "邮箱账号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 10], "componentTypeLevel1": 1}, {"key": "emailSalePwd", "c_id": "985ec3415e6042a7a2388d970bbb30de", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "邮箱密码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 10], "componentTypeLevel1": 1}]',NULL,'["emailSaleAccount", "emailSalePwd"]',1,1,'***************','***************','2025-03-25 14:56:09.980','2025-04-15 20:06:11.511',0),
	 ('18位换绑码',2,11,'18位换绑码','[{"key": "bindingChangeCode18", "c_id": "66e6104b6d684296a5d8a6787fc2a48f", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "18位换绑码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": true, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 11], "componentTypeLevel1": 1}]',NULL,'["bindingChangeCode18"]',1,1,'***************','***************','2025-03-25 14:56:54.165','2025-04-15 20:06:11.511',0),
	 ('邮箱出售情况',2,9,'邮箱出售情况',NULL,'[{"key": "emailSale", "c_id": "8354c6e1c6cf4104a650a33a5c5ad5fe", "type": "radio", "label": {"color": "rgba(51, 51, 51, 1)", "title": "邮箱出售情况", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "sale", "label": "出售邮箱", "value": "sale", "tooltip": ""}, {"key": "unsale", "label": "不出售邮箱", "value": "unsale", "tooltip": ""}, {"key": "unbind", "label": "邮箱未绑定", "value": "unbind", "tooltip": ""}]}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择邮箱出售情况", "componentTypeLevel": [2, 9], "componentTypeLevel1": 1}, {"key": "emailSaleAccount", "c_id": "17fcbadbcb69443294106b82ce23647e", "type": "input", "label": {"color": "rgba(51, 51, 51, 1)", "title": "请输入邮箱密码", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": ["@qq.com", "@vip.qq.com", "@163.com", "@VIP.163.com", "@126.com", "@VIP.126.com", "@yeah.net", "@188.com", "@139.com", "@aliyun.com", "@hotmail.com", "@outlook.com", "@foxmail.com", "@sina.com", "@Yahoo.com", "@gmail.com", "@aol.com", "@sohu.com"], "options": [], "autoSize": false, "clearable": false, "displayMail": true, "placeholder": "请输入邮箱账号", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "emailSale", "rightKey": "sale", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "出售邮箱", "rightFormKey": "sale"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请输入正确的邮箱账号", "mode": "regexp", "regexp": "^(?=.{5,50}$)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$"}, "requiredMessage": "请输入邮箱账号", "componentTypeLevel": [2, 9], "componentTypeLevel1": 1}, {"key": "emailSalePwd", "c_id": "d1b91c00828b40ee89fdfaabb9f44bb9", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "password", "mails": [], "options": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入密码", "showPassword": true, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "emailSale", "rightKey": "sale", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "出售邮箱", "rightFormKey": "sale"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "邮箱密码长度限制5-50位", "mode": "regexp", "regexp": "^.{5,50}$"}, "requiredMessage": "请输入邮箱密码", "componentTypeLevel": [2, 9], "componentTypeLevel1": 1}]','["emailSale", "emailSaleAccount", "sale", "emailSalePwd"]',2,1,'***************','***************','2025-03-25 14:57:00.050','2025-04-21 19:47:12.336',0),
	 ('游戏账号',2,5,'游戏账号',NULL,'[{"key": "gameAccount", "c_id": "c708d2b7857c478a873ddf0606e1d0a5", "type": "input", "label": {"color": "rgba(0,0,0,1)", "title": "游戏账号", "fontSize": 14, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": "rgba(255,255,255,1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "输入框", "validate": {"msg": ""}, "componentTypeLevel": [2, 5], "componentTypeLevel1": 1}, {"key": "gameLoginPwd", "c_id": "5c78cca21d1646edaa73eab31273b539", "type": "input", "label": {"color": "rgba(0,0,0,1)", "title": "账号密码", "fontSize": 14, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": "rgba(255,255,255,1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "输入框", "validate": {"msg": ""}, "componentTypeLevel": [2, 5], "componentTypeLevel1": 1}]','["gameAccount", "gameLoginPwd"]',2,1,'','***************','2025-03-25 15:01:36.594','2025-04-15 20:06:11.511',0),
	 ('是否提供换绑码',2,12,'是否提供换绑码','[{"key": "provideBindingCode", "c_id": "41c03fc959e34d0aa770519709b1a0d3", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "是否提供换绑码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 12], "componentTypeLevel1": 1}]',NULL,'["provideBindingCode"]',1,1,'***************','***************','2025-03-25 15:05:29.485','2025-04-15 20:06:11.511',0);
INSERT INTO delivery_components_config (component_name,component_type_level1,component_type_level2,component_desc,card_style_json,form_style_json,data_config,component_use_case,sort_value,create_user_id,update_user_id,create_time,update_time,is_deleted) VALUES
	 ('换绑手机号',2,13,'换绑手机号','[{"key": "bindingChangeMobile", "c_id": "125c5faa248d4034b0431401d50d1731", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "换绑手机号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 13], "componentTypeLevel1": 1}]',NULL,'["bindingChangeMobile"]',1,1,'***************','***************','2025-03-25 15:06:46.984','2025-04-15 20:06:11.511',0),
	 ('换绑码提供情况',2,23,'换绑码提供情况',NULL,'[{"key": "provideBindingCode", "c_id": "0439259a9e1b4bf68afbf2f02b9e4a75", "type": "radio", "label": {"color": "rgba(51, 51, 51, 1)", "title": "换绑码提供情况", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "provide", "label": "提供换绑码", "value": "provide", "tooltip": ""}, {"key": "no_provide", "label": "不提供换绑码", "value": "no_provide", "tooltip": ""}]}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "提供18位换绑码，平均交易提效30%", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择换绑码提供情况", "componentTypeLevel": [2, 23], "componentTypeLevel1": 1}, {"key": "bindingChangeCode18", "c_id": "810bb6b058874629b77f44efa8538bc0", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "options": [], "autoSize": false, "clearable": true, "displayMail": false, "placeholder": "请输入18位换绑码(认证的身份证号)", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "provideBindingCode", "rightKey": "provide", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "提供换绑码", "rightFormKey": "provide"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请填写正确的18位换绑码(认证的身份证号)", "mode": "idcard"}, "requiredMessage": "请输入18位换绑码(认证的身份证号)", "componentTypeLevel": [2, 23], "componentTypeLevel1": 1}]','["provideBindingCode", "bindingChangeCode18", "provide"]',2,1,'***************','***************','2025-03-25 15:13:59.946','2025-04-18 10:38:32.944',0),
	 ('换绑手机号',2,13,'换绑手机号',NULL,'[{"key": "bindingChangeMobile", "c_id": "a3a53164e8e7475d9a0d1eb5c7a5c152", "type": "input", "label": {"color": "rgba(51, 51, 51, 1)", "title": "换绑手机号", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入手机号", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": "", "pr": "", "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请输入正确的手机号码", "mode": "phone"}, "requiredMessage": "请输入手机号", "componentTypeLevel": [2, 13], "componentTypeLevel1": 1}]','["bindingChangeMobile"]',2,1,'***************','***************','2025-03-25 15:15:18.006','2025-04-18 10:42:35.367',0),
	 ('换绑手机号（脱敏）',2,24,'换绑手机号（脱敏）','[{"key": "bindingChangeMobileSensitive", "c_id": "61b4a0a6380c4f80aeb63985782e673f", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "换绑手机号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "options": [], "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 24], "componentTypeLevel1": 1}]',NULL,'["bindingChangeMobileSensitive"]',1,1,'***************','***************','2025-03-25 15:15:58.849','2025-04-21 20:12:48.739',0),
	 ('UID(游戏唯一标识)',2,14,'UID(游戏唯一标识)',NULL,'[{"key": "uid", "c_id": "d2518c3b3b2c40d99fcceb532149de65", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "UID(游戏唯一标识)", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入UID", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "UID长度限制1-50位", "mode": "regexp", "length": null, "regexp": "^.{1,50}$"}, "requiredMessage": "请输入UID", "componentTypeLevel": [2, 14], "componentTypeLevel1": 1}]','["uid"]',2,1,'***************','***************','2025-03-25 15:16:44.216','2025-04-25 10:43:55.885',0),
	 ('UID',2,14,'UID','[{"key": "uid", "c_id": "f79ae480fc434b77acf726bfe5af3ba4", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "UID", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 14], "componentTypeLevel1": 1}]',NULL,'["uid"]',1,1,'***************','***************','2025-03-25 15:18:27.335','2025-04-15 20:06:11.511',0),
	 ('微信支付密码',2,15,'微信支付密码',NULL,'[{"key": "wechatPayPassword", "c_id": "d3f1903b3a0f4e439c26d9791a7118c4", "type": "input", "label": {"color": "rgba(51, 51, 51, 1)", "title": "微信支付密码", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "number", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入6位的数字密码", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请输入6位微信支付密码", "mode": "regexp", "length": 6, "regexp": "^\\\\d{6}$"}, "requiredMessage": "请输入微信支付密码", "componentTypeLevel": [2, 15], "componentTypeLevel1": 1}]','["wechatPayPassword"]',2,1,'***************','***************','2025-03-25 15:21:05.625','2025-04-23 16:24:32.025',0),
	 ('微信支付密码',2,15,'微信支付密码','[{"key": "wechatPayPassword", "c_id": "b79de63926eb4b02b0b5f0dad524368d", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "微信支付密码", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 15], "componentTypeLevel1": 1}]',NULL,'["wechatPayPassword"]',1,1,'***************','***************','2025-03-25 15:21:05.635','2025-04-15 20:06:11.511',0),
	 ('游戏账号密码',2,5,'游戏账号密码',NULL,'[{"key": "gameAccount", "c_id": "659e7cc98d724e86b684a9c02042a8f2", "type": "input", "label": {"color": "rgba(51, 51, 51, 1)", "title": "游戏账号密码", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": ["@qq.com", "@vip.qq.com", "@163.com", "@VIP.163.com", "@126.com", "@VIP.126.com", "@yeah.net", "@188.com", "@139.com", "@aliyun.com", "@hotmail.com", "@outlook.com", "@foxmail.com", "@sina.com", "@Yahoo.com", "@gmail.com", "@aol.com", "@sohu.com"], "autoSize": false, "clearable": false, "displayMail": true, "placeholder": "请输入游戏账号", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请检查游戏账号是否输入有误", "mode": "regexp", "regexp": "^.{5,50}$"}, "requiredMessage": "请输入游戏账号", "componentTypeLevel": [2, 5], "componentTypeLevel1": 1}, {"key": "gameLoginPwd", "c_id": "d45ff818d5bd4fbaa46b38c7e01457f8", "type": "input", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "password", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "非密码登录，该项任意输入", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请检查游戏密码是否输入有误", "mode": "regexp", "regexp": "^.{5,50}$"}, "requiredMessage": "请输入游戏账号密码", "componentTypeLevel": [2, 5], "componentTypeLevel1": 1}]','["gameAccount", "gameLoginPwd"]',2,1,'***************','***************','2025-03-25 15:28:51.836','2025-04-21 19:59:21.645',0),
	 ('OCR二次实名情况',3,1,'二次实名情况',NULL,'[{"key": "secondRealName", "c_id": "df6c0a7667df44de95b61a31f1a7ffa9", "type": "radio", "label": {"color": "rgba(51, 51, 51, 1)", "title": "二次实名情况", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "support_second", "label": "可二次实名", "value": "support_second", "tooltip": ""}, {"key": "no_second", "label": "不可二次实名", "value": "no_second", "tooltip": ""}]}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择二次实名情况", "componentTypeLevel": [3, 1], "componentTypeLevel1": 1}, {"key": "secondRealNameImageUrl", "c_id": "13754a8f06b94f49a0fd27974fb5032f", "type": "upload", "label": {"color": "rgba(51, 51, 51, 1)", "title": "请上传二次实名截图（5分钟内）", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"limit": 1, "examplePicKey": "ocr_secondary_real_name"}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "secondRealName", "rightKey": "support_second", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "可二次实名", "rightFormKey": "support_second"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "上传", "validate": {"msg": ""}, "requiredMessage": "请上传二次实名截图", "componentTypeLevel": [3, 1], "componentTypeLevel1": 1}]','["secondRealName", "secondRealNameImageUrl", "support_second"]',2,1,'***************','***************','2025-03-25 15:37:16.851','2025-04-18 10:45:14.760',0);
INSERT INTO delivery_components_config (component_name,component_type_level1,component_type_level2,component_desc,card_style_json,form_style_json,data_config,component_use_case,sort_value,create_user_id,update_user_id,create_time,update_time,is_deleted) VALUES
	 ('OCR注销网易支付截图',3,3,'注销网易支付截图',NULL,'[{"key": "netEaseRealNameImageUrl", "c_id": "6b320c674fc644a89182d5bbeab3ceee", "type": "upload", "label": {"color": "rgba(51, 51, 51, 1)", "title": "注销网易支付截图", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"limit": 1, "options": [], "examplePicKey": "ocr_logout_netease_pay_screenshot"}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "上传", "validate": {"msg": ""}, "requiredMessage": "请上传注销网易支付截图", "componentTypeLevel": [3, 3], "componentTypeLevel1": 1}]','["netEaseRealNameImageUrl"]',2,1,'***************','***************','2025-03-25 15:44:09.014','2025-04-18 10:45:38.570',0),
	 ('OCR游戏uid截图',3,2,'OCR游戏uid截图',NULL,'[{"key": "uidImageUrl", "c_id": "57143c8ad2184d58a11b3a3cfa7b3eb7", "type": "upload", "label": {"color": "rgba(51, 51, 51, 1)", "title": "游戏UID截图", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"limit": 1, "examplePicKey": "ocr_game_uid_screenshot"}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "上传", "validate": {"msg": ""}, "requiredMessage": "请上传游戏UID截图", "componentTypeLevel": [3, 2], "componentTypeLevel1": 1}, {"key": "uid", "c_id": "97ac2981a52848a081096407fd841b32", "type": "input", "label": {"color": "rgba(51, 51, 51, 1)", "title": "UID（游戏唯一标识）", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "OR", "conditions": [{"type": "enum", "leftKey": "imIdentity", "rightKey": "custom_service", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "客服", "rightFormKey": "custom_service"}, {"logic": "AND", "conditions": [{"type": "string", "leftKey": "uid", "condition": "notEmpty", "leftKeyType": "variable"}, {"type": "string", "leftKey": "uidImageUrl", "condition": "notEmpty", "leftKeyType": "variable"}]}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "imIdentity", "rightKey": "custom_service", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "客服", "rightFormKey": "custom_service"}]}, "defaultValue": true}, "required": true, "typeName": "输入框", "validate": {"msg": ""}, "requiredMessage": "请填写UID", "componentTypeLevel": [3, 2], "componentTypeLevel1": 1}]','["uidImageUrl", "uid", "imIdentity", "custom_service"]',2,1,'***************','***************','2025-03-25 15:47:06.041','2025-04-18 10:46:01.197',0),
	 ('二次实名',3,1,'组合组件二次实名','[{"key": "secondRealName", "c_id": "1a04db94b5df4819b75eb26711383e5a", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "二次实名", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [3, 1], "componentTypeLevel1": 1}, {"key": "secondRealNameImageUrl", "c_id": "aa40d9b291c44e96bf19cc93a427533f", "type": "image", "label": {"color": "rgba(153, 153, 153, 1)", "title": " ", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"src": "", "initialIndex": 0, "previewSrcList": []}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"rules": {"logic": "AND", "conditions": [{"type": "enum", "leftKey": "secondRealName", "rightKey": "support_second", "variable": "variable", "condition": "==", "leftKeyType": "variable", "rightCardKey": "可二次实名", "rightFormKey": "support_second"}]}, "defaultValue": true}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "图片", "validate": {"msg": ""}, "componentTypeLevel": [3, 1], "componentTypeLevel1": 1}]',NULL,'["secondRealName", "secondRealNameImageUrl", "support_second"]',1,1,'***************','***************','2025-03-25 16:37:03.426','2025-04-21 20:12:22.419',0),
	 ('注销网易支付截图',3,3,'注销网易支付截图','[{"key": "netEaseRealNameImageUrl", "c_id": "9219da1243a94e4b9254bf0885ddd110", "type": "image", "label": {"color": "rgba(153, 153, 153, 1)", "title": "注销网易支付截图", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"src": "", "initialIndex": 0, "previewSrcList": []}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "图片", "validate": {"msg": ""}, "componentTypeLevel": [3, 3], "componentTypeLevel1": 1}]',NULL,'["netEaseRealNameImageUrl"]',1,1,'***************','***************','2025-03-25 16:40:33.597','2025-04-21 21:05:11.316',0),
	 ('商品信息',2,3,'商品信息','[{"c_id": "bab2cbd8a57c44ec98403eb2c9f907e6", "type": "product", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"img": {"key": "productThumbnail"}, "price": {"key": "price", "color": "rgba(153, 153, 153, 1)", "fontSize": 12, "fontWeight": 400}, "title": {"key": "productName", "color": "rgba(51, 51, 51, 1)", "fontSize": 13, "fontWeight": 500}, "jumpUrl": {}, "receivedPrice": {"key": "finalPrice", "color": "rgba(246, 160, 70, 1)", "fontSize": 12, "fontWeight": 400, "priceFontSize": 16, "priceFontWeight": 500}}, "style": {"mb": 6, "ml": 12, "mr": 12, "mt": 2, "pb": 8, "pl": 12, "pr": 12, "pt": 8, "borderRadius": [8, 8, 8, 8], "backgroundColor": "rgba(244, 245, 246, 1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "商品", "validate": {"msg": ""}, "componentTypeLevel": [2, 3], "componentTypeLevel1": 1}]','[{"c_id": "bab2cbd8a57c44ec98403eb2c9f907e6", "type": "product", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"img": {"key": "productThumbnail"}, "price": {"key": "price", "color": "rgba(153, 153, 153, 1)", "fontSize": 12, "fontWeight": 400}, "title": {"key": "productName", "color": "rgba(51, 51, 51, 1)", "fontSize": 13, "fontWeight": 500}, "jumpUrl": {}, "receivedPrice": {"key": "finalPrice", "color": "rgba(246, 160, 70, 1)", "fontSize": 12, "fontWeight": 400, "priceFontSize": 16, "priceFontWeight": 500}}, "style": {"mb": 6, "ml": 12, "mr": 12, "mt": 2, "pb": 8, "pl": 12, "pr": 12, "pt": 8, "borderRadius": [8, 8, 8, 8], "backgroundColor": "rgba(244, 245, 246, 1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "商品", "validate": {"msg": ""}, "componentTypeLevel": [2, 3], "componentTypeLevel1": 1}]','["productThumbnail", "price", "productName", "finalPrice"]',1,1,'***************','***************','2025-03-27 11:34:30.005','2025-04-25 14:50:55.916',0),
	 ('通用文本',1,1,'通用文本-卡片','[{"c_id": "df3c9b38128d462a8d031b106c88e4aa", "type": "html", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"text": "<p>富文本内容</p>", "innerText": "<p>富文本内容</p>"}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "HTML", "validate": {"msg": ""}, "componentTypeLevel": [1, 1], "componentTypeLevel1": 1}]','[{"c_id": "df3c9b38128d462a8d031b106c88e4aa", "type": "html", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"text": "<p>富文本内容</p>", "innerText": "<p>富文本内容</p>"}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": "rgba(255,255,255,1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "HTML", "validate": {"msg": ""}, "componentTypeLevel": [1, 1], "componentTypeLevel1": 1}]','[]',1,1,'***************','***************','2025-03-31 10:52:20.158','2025-04-15 20:06:11.511',0),
	 ('通用图片',1,2,'通用图片-卡片','[{"c_id": "69b55f3d7d2943838e6e374aeafeacef", "type": "image", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"src": "", "initialIndex": 0, "previewSrcList": []}, "style": {"mb": 4, "ml": 12, "mr": 12, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "图片", "validate": {"msg": ""}, "componentTypeLevel": [1, 2], "componentTypeLevel1": 1}]','[{"c_id": "69b55f3d7d2943838e6e374aeafeacef", "type": "image", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"src": "", "initialIndex": 0, "previewSrcList": []}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 0, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": "rgba(255,255,255,1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "图片", "validate": {"msg": ""}, "componentTypeLevel": [1, 2], "componentTypeLevel1": 1}]','[]',1,1,'***************','***************','2025-03-31 10:56:23.335','2025-04-21 20:11:08.906',0),
	 ('通用文本',1,1,'通用文本-表单',NULL,'[{"c_id": "5026c2deb0b846ef9a73ff7a418e7448", "type": "html", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"text": "<p>富文本内容</p>", "innerText": "<p>富文本内容</p>"}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "HTML", "validate": {"msg": ""}, "componentTypeLevel": [1, 1], "componentTypeLevel1": 1}]','[]',2,1,'***************','***************','2025-04-02 10:51:01.426','2025-04-15 20:06:11.511',0),
	 ('通用图片',1,2,'通用图片-表单',NULL,'[{"c_id": "c71802ca16ae4dafb0bc1fe58258dbb1", "type": "image", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"src": "", "initialIndex": 0, "previewSrcList": []}, "style": {"mb": 8, "ml": 16, "mr": 16, "mt": 4, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "图片", "validate": {"msg": ""}, "componentTypeLevel": [1, 2], "componentTypeLevel1": 1}]','[]',2,1,'***************','***************','2025-04-02 10:58:03.697','2025-04-15 20:06:11.511',0),
	 ('腾讯账号类型',2,20,'腾讯账号类型',NULL,'[{"key": "txProductType", "c_id": "6e75526bbf174619a52ce32683189d55", "type": "radio", "label": {"color": "rgba(51, 51, 51, 1)", "title": "腾讯账号类型", "fontSize": 14, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "qq", "label": "qq登录", "value": "qq", "tooltip": ""}, {"key": "wechat", "label": "微信登录", "value": "wechat", "tooltip": ""}, {"key": "other", "label": "其他", "value": "other", "tooltip": ""}]}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择腾讯账号类型", "componentTypeLevel": [2, 20], "componentTypeLevel1": 1}]','["txProductType"]',2,1,'***************','***************','2025-04-03 11:09:32.855','2025-04-18 10:46:32.799',0);
INSERT INTO delivery_components_config (component_name,component_type_level1,component_type_level2,component_desc,card_style_json,form_style_json,data_config,component_use_case,sort_value,create_user_id,update_user_id,create_time,update_time,is_deleted) VALUES
	 ('商品信息',2,3,'商品信息-表单',NULL,'[{"c_id": "20ff894c51ff44b298b55293973b753f", "type": "product", "label": {"color": "rgba(153, 153, 153, 1)", "title": "", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"img": {"key": "productThumbnail"}, "price": {"key": "price", "color": "rgba(153, 153, 153, 1)", "fontSize": 12, "fontWeight": 400}, "title": {"key": "productName", "color": "rgba(51, 51, 51, 1)", "fontSize": 13, "fontWeight": 500}, "jumpUrl": {}, "receivedPrice": {"key": "finalPrice", "color": "rgba(246, 160, 70, 1)", "fontSize": 12, "fontWeight": 400, "priceFontSize": 16, "priceFontWeight": 500}}, "style": {"mb": 6, "ml": 16, "mr": 16, "mt": 2, "pb": 8, "pl": 12, "pr": 12, "pt": 8, "borderRadius": [8, 8, 8, 8], "backgroundColor": "rgba(244, 245, 246, 1)"}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "商品", "validate": {"msg": ""}, "componentTypeLevel": [2, 3], "componentTypeLevel1": 1}]','["productThumbnail", "price", "productName", "finalPrice"]',2,1,'***************','***************','2025-04-25 14:41:55.108','2025-04-25 14:44:38.446',0),
	 ('腾讯账号类型',2,20,'腾讯账号类型','[{"key": "txProductType", "c_id": "17886667d63d41e2ae4110df7f516d15", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "腾讯账号类型", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "options": [{"key": "qq", "label": "QQ登录", "value": "qq", "tooltip": ""}, {"key": "wechat", "label": "微信登录", "value": "wechat", "tooltip": ""}, {"key": "other", "label": "其他", "value": "other", "tooltip": ""}], "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 20], "componentTypeLevel1": 1}]',NULL,'["txProductType"]',1,1,'***************','***************','2025-04-28 17:45:51.936','2025-04-29 14:56:59.466',0),
	 ('登录后微信号',2,26,'登录后微信号','[{"key": "loginWechatAccount", "c_id": "5df6cf3e9eec48b08477731a84ce7e52", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "登录后微信号", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "options": [], "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 26], "componentTypeLevel1": 1}]',NULL,'["loginWechatAccount"]',1,1,'***************','***************','2025-04-28 17:58:13.292','2025-05-12 11:03:09.674',0),
	 ('米哈游账号类型',2,25,'米哈游账号类型','[{"key": "mihoyoProductType", "c_id": "51de9757422946379977df4838a1e441", "type": "text", "label": {"color": "rgba(153, 153, 153, 1)", "title": "米哈游账号类型", "fontSize": 13, "position": "auto", "fontWeight": 400}, "props": {"text": "", "color": "rgba(51, 51, 51, 1)", "options": [{"key": "mihoyo", "label": "米哈游官服", "value": "mihoyo", "tooltip": ""}, {"key": "bilibili", "label": "B服", "value": "bilibili", "tooltip": ""}, {"key": "international", "label": "国际服", "value": "international", "tooltip": ""}, {"key": "xiaomi", "label": "小米服", "value": "xiaomi", "tooltip": ""}, {"key": "other", "label": "其他", "value": "other", "tooltip": ""}], "copyable": false, "fontSize": 13, "fontWeight": 400}, "style": {"mb": 2, "ml": 12, "mr": 12, "mt": 2, "pb": 0, "pl": 0, "pr": 0, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "isTitle": false, "tooltip": "", "disabled": {"defaultValue": false}, "required": false, "typeName": "文本", "validate": {"msg": ""}, "componentTypeLevel": [2, 25], "componentTypeLevel1": 1}]',NULL,'["mihoyoProductType"]',1,1,'***************','***************','2025-04-28 17:59:19.898','2025-04-29 14:56:39.507',0),
	 ('米哈游账号类型',2,25,'米哈游账号类型',NULL,'[{"key": "mihoyoProductType", "c_id": "3370810b246c4d2a9fa0bb2b2123eb73", "type": "radio", "label": {"color": "rgba(51, 51, 51, 1)", "title": "米哈游账号类型", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"options": [{"key": "mihoyo", "label": "米哈游官服", "value": "mihoyo", "tooltip": ""}, {"key": "bilibili", "label": "B服", "value": "bilibili", "tooltip": ""}, {"key": "international", "label": "国际服", "value": "international", "tooltip": ""}, {"key": "xiaomi", "label": "小米服", "value": "xiaomi", "tooltip": ""}, {"key": "other", "label": "其他", "value": "other", "tooltip": ""}]}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "单选框", "validate": {"msg": ""}, "requiredMessage": "请选择米哈游账号类型", "componentTypeLevel": [2, 25], "componentTypeLevel1": 1}]','["mihoyoProductType"]',2,1,'***************','***************','2025-04-28 18:00:15.796','2025-04-29 11:41:45.550',0),
	 ('登陆后微信号',2,26,'登陆后微信号',NULL,'[{"key": "loginWechatAccount", "c_id": "4740fbde79c34b0fa693b1ef75a3673f", "type": "input", "label": {"color": "rgba(51, 51, 51, 1)", "title": "登陆后微信号", "fontSize": 13, "position": "top", "fontWeight": 400}, "props": {"rows": 1, "type": "text", "mails": [], "options": [], "autoSize": false, "clearable": false, "displayMail": false, "placeholder": "请输入", "showPassword": false, "showWordLimit": false}, "style": {"mb": 8, "ml": 0, "mr": 0, "mt": 4, "pb": 0, "pl": 16, "pr": 16, "pt": 0, "borderRadius": [0, 0, 0, 0], "backgroundColor": null}, "hidden": {"defaultValue": false}, "prefix": "", "suffix": "", "tooltip": "", "disabled": {"defaultValue": false}, "required": true, "typeName": "输入框", "validate": {"msg": "请填写正确的微信号", "mode": "regexp", "length": 20, "regexp": "^[A-Za-z][A-Za-z0-9_-]{5,19}$"}, "requiredMessage": "请输入微信号", "componentTypeLevel": [2, 26], "componentTypeLevel1": 1}]','["loginWechatAccount"]',2,1,'***************','***************','2025-04-28 18:05:14.586','2025-05-06 13:55:34.595',0);
