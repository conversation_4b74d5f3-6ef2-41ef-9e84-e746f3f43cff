<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.pxb7.mall.trade</groupId>
    <artifactId>ofs</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>ofs</name>
    <distributionManagement>
        <repository>
            <id>pxb7-releases</id>
            <url>http://nexus.pxb7.internal/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <snapshotRepository>
            <id>pxb7-snapshots</id>
            <url>http://nexus.pxb7.internal/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </snapshotRepository>
    </distributionManagement>
    <properties>
        <revision>1.0.9</revision>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>${maven.compiler.source}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${project.build.sourceEncoding}</project.reporting.outputEncoding>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <skipSpotless>false</skipSpotless>
        <maven.deploy.skip>false</maven.deploy.skip>
        <cola.components.version>4.3.2</cola.components.version>
        <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
        <spring-cloud-starter-bootstrap-version>4.1.2</spring-cloud-starter-bootstrap-version>
        <spring-boot.version>3.2.12</spring-boot.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mybatis-plus-starter.version>3.5.6</mybatis-plus-starter.version>
        <spring-cloud.version>2023.0.1</spring-cloud.version>
        <redission-spring-boot-version>3.29.0</redission-spring-boot-version>
        <dubbo.boot.version>3.2.12</dubbo.boot.version>
        <rocketmq.boot.version>2.3.0</rocketmq.boot.version>
        <spring-context-support-version>1.0.11</spring-context-support-version>
        <hutool.version>5.8.27</hutool.version>
        <lombok.version>1.18.32</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <fastjson.version>2.0.50</fastjson.version>
        <okhttp.version>4.12.0</okhttp.version>
        <retrofit.version>2.11.0</retrofit.version>
        <converter-jackson-version>2.11.0</converter-jackson-version>
        <jakarta.validation-api-version>3.0.2</jakarta.validation-api-version>
        <jakarta.el-api-version>6.0.0</jakarta.el-api-version>
        <powerjob.version>4.3.9</powerjob.version>
        <skywalking.version>9.2.0</skywalking.version>
        <shardingsphere-jdbc-version>5.5.0</shardingsphere-jdbc-version>
        <maven-resources-plugin-version>3.3.1</maven-resources-plugin-version>
        <maven-clean-plugin-version>3.3.2</maven-clean-plugin-version>
        <maven.compiler-plugin.version>3.13.0</maven.compiler-plugin.version>
        <maven-deploy-plugin.version>3.1.2</maven-deploy-plugin.version>
        <sonar-maven-plugin-version>3.11.0.3922</sonar-maven-plugin-version>
        <spotless-maven-plugin-version>2.43.0</spotless-maven-plugin-version>
        <dynamic-tp-spring-cloud-starter-nacos-version>1.1.7-3.x</dynamic-tp-spring-cloud-starter-nacos-version>
        <idgen-spring-boot-starter-version>1.1.4</idgen-spring-boot-starter-version>
        <logback-level-change-version>0.0.1</logback-level-change-version>
        <auth-satoken.version>1.0.13</auth-satoken.version>
        <cola-statemachine.version>4.2.1</cola-statemachine.version>
        <web-scoket-version>1.1</web-scoket-version>
        <org-springframework-retry-version>1.2.4.RELEASE</org-springframework-retry-version>
        <px-elasticsearch-starter.version>1.0.0</px-elasticsearch-starter.version>
        <px-cache-redisson.version>0.0.1-SNAPSHOT</px-cache-redisson.version>
        <dubbo-instance-register.version>1.0.8</dubbo-instance-register.version>
        <exception-spring-boot.version>1.11.9</exception-spring-boot.version>
        <rocketmqext.version>1.0.2</rocketmqext.version>
        <sentryext-starter-version>1.0.3</sentryext-starter-version>
        <sentinel.version>1.0.1</sentinel.version>
        <!-- dubbo client -->
        <user-c-client-version>1.0.7</user-c-client-version>
        <ass-client-version>1.0.7</ass-client-version>
        <order-client-version>1.0.26-SNAPSHOT</order-client-version>
        <im-c-client-version>1.0.17</im-c-client-version>
        <merchant-admin-client-version>1.0.2</merchant-admin-client-version>
        <product-c-client-version>1.0.26</product-c-client-version>
        <product-admin-client-version>1.0.1</product-admin-client-version>
        <tradeadmin-client-version>1.0.5</tradeadmin-client-version>
        <delivery-admin-client-version>1.0.5</delivery-admin-client-version>
        <common-support-client-version>1.0.18</common-support-client-version>
        <!-- dubbo client -->
        <app-config-starter.version>1.0.15</app-config-starter.version>
        <pdfbox-version>2.0.4</pdfbox-version>
        <cola-component-extension-starter.version>5.0.0</cola-component-extension-starter.version>
        <camunda-client.version>1.0.0</camunda-client.version>
        <risk-engine-client-version>1.0.0</risk-engine-client-version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>


            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>camunda-client</artifactId>
                <version>${camunda-client.version}</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/com.alibaba.cola/cola-component-extension-starter -->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-extension-starter</artifactId>
                <version>${cola-component-extension-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>app-config-starter</artifactId>
                <version>${app-config-starter.version}</version>
            </dependency>

            <!--    dubbo-client-->
            <dependency>
                <groupId>com.pxb7.mall.product</groupId>
                <artifactId>product-c-client</artifactId>
                <version>${product-c-client-version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>delivery-admin-client</artifactId>
                <version>${delivery-admin-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>tradeadmin-client</artifactId>
                <version>${tradeadmin-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.common</groupId>
                <artifactId>common-support-client</artifactId>
                <version>${common-support-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.common</groupId>
                <artifactId>risk-engine-client</artifactId>
                <version>${risk-engine-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.user</groupId>
                <artifactId>user-c-client</artifactId>
                <version>${user-c-client-version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>order-client</artifactId>
                <version>${order-client-version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.im</groupId>
                <artifactId>im-c-client</artifactId>
                <version>${im-c-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.merchant</groupId>
                <artifactId>merchant-admin-client</artifactId>
                <version>${merchant-admin-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>ass-client</artifactId>
                <version>${ass-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.product</groupId>
                <artifactId>product-admin-client</artifactId>
                <version>${product-admin-client-version}</version>
            </dependency>
            <!--    dubbo-client end-->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${spring-cloud-starter-bootstrap-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${org-springframework-retry-version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <!--Project modules-->
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>ofs-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>ofs-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>ofs-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>contract-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>contract-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>contract-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>intelligent-delivery-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>intelligent-delivery-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.trade</groupId>
                <artifactId>intelligent-delivery-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules End-->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-components-bom</artifactId>
                <version>${cola.components.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>idgen-spring-boot-starter</artifactId>
                <version>${idgen-spring-boot-starter-version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-nacos</artifactId>
                <version>${dynamic-tp-spring-cloud-starter-nacos-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>logback-springboot3-starter</artifactId>
                <version>${logback-level-change-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-test-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-parser-sql-oracle</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-parser-sql-sqlserver</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.shardingsphere</groupId>
                        <artifactId>shardingsphere-parser-sql-opengauss</artifactId>
                    </exclusion>
                </exclusions>
                <version>${shardingsphere-jdbc-version}</version>
            </dependency>
            <!--rocketmq-->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
                <version>${rocketmq.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>rocketmqext-spring-boot-starter</artifactId>
                <version>${rocketmqext.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-common</artifactId>
                <version>${dubbo.boot.version}</version>
                <scope>compile</scope>
            </dependency>
            <!-- Misc -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.el</groupId>
                <artifactId>jakarta.el-api</artifactId>
                <version>${jakarta.el-api-version}</version>
            </dependency>
            <!-- Misc End -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>${converter-jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redission-spring-boot-version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-exception</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <!-- powerJob -->
            <dependency>
                <groupId>tech.powerjob</groupId>
                <artifactId>powerjob-worker-spring-boot-starter</artifactId>
                <version>${powerjob.version}</version>
            </dependency>
            <!-- skywalking链路追踪 -->
            <!-- skywalking logback日志插件 -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>auth-satoken-c-starter</artifactId>
                <version>${auth-satoken.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-statemachine</artifactId>
                <version>${cola-statemachine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>px-elasticsearch-starter</artifactId>
                <version>${px-elasticsearch-starter.version}</version>
            </dependency>

            <!-- dubbo应用级别注册 -->
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>dubbo-instance-register-starter</artifactId>
                <version>${dubbo-instance-register.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>exception-spring-boot-starter</artifactId>
                <version>${exception-spring-boot.version}</version>
            </dependency>

            <!-- 缓存工具类 -->
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>px-cache-redisson-util-starter</artifactId>
                <version>${px-cache-redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>sentryext-spring-boot-starter</artifactId>
                <version>${sentryext-starter-version}</version>
            </dependency>
            <!-- pdfbox tools-->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox-version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox-tools</artifactId>
                <version>${pdfbox-version}</version>
            </dependency>
            <dependency>
                <groupId>com.pxb7.mall.components</groupId>
                <artifactId>sentinel-component-starter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <repositories>
        <repository>
            <id>pxb7-nexus</id>
            <url>http://nexus.pxb7.internal/repository/maven-public/</url>
            <releases>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <defaultGoal>compile</defaultGoal>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                    <configuration>
                        <skip>${maven.deploy.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin-version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <includeEmptyDirs>true</includeEmptyDirs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.source}</target>
                    <compilerArgument>-parameters</compilerArgument>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <!-- Lombok 在编译时插件 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin-version}</version>
                <configuration>
                    <filesets>
                        <fileset>
                            <!--要清理的目录位置-->
                            <directory>${user.home}/data/weblog/${name}</directory>
                            <!--是否跟随符号链接,默认false-->
                            <followSymlinks>false</followSymlinks>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>ofs-adapter</module>
        <module>ofs-app</module>
        <module>start</module>
        <module>ofs-common</module>
        <module>ofs-contract/contract-client</module>
        <module>ofs-contract/contract-domain</module>
        <module>ofs-contract/contract-infrastructure</module>
        <module>ofs-intelligent-delivery/intelligent-delivery-client</module>
        <module>ofs-intelligent-delivery/intelligent-delivery-domain</module>
        <module>ofs-intelligent-delivery/intelligent-delivery-infrastructure</module>
    </modules>
</project>
