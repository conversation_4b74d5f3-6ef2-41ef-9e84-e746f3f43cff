package com.pxb7.mall.trade.ofs.contract.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: ContractSourceEnum.java
 * @description: 合同来源
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/2/27 20:07
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Getter
@AllArgsConstructor
public enum ContractSourceEnum {
    IM(1, "IM"),
    ORDER_CENTER(2, "订单中心"),
    BACKEND(3, "后台");
    private final Integer code;
    private final String desc;
}
