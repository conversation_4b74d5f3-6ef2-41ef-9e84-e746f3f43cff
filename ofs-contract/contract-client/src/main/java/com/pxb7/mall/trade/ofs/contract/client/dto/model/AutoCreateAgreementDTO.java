package com.pxb7.mall.trade.ofs.contract.client.dto.model;

import java.io.Serial;
import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AutoCreateAgreementDTO.java
 * @description: 自动创建协议DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/3 14:54
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Builder
public class AutoCreateAgreementDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 2324705860517576124L;
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 操作人ID
     */
    private String operatorId;
}
