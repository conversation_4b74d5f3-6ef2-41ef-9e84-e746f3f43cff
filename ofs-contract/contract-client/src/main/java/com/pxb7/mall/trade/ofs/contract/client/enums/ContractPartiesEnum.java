package com.pxb7.mall.trade.ofs.contract.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: ContractPartiesEnum.java
 * @description: 合同签署方
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/2/20 11:23
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@AllArgsConstructor
@Getter
public enum ContractPartiesEnum {
    PARTIES(1, "parties", "双方"),
    PURCHASER(2, "purchaser", "甲方(买家)"),
    VENDOR(3, "vendor", "乙方(卖家)"),
    AGENT(4, "agent", "丙方(居间方)"),
    AFFILIATE(5, "affiliate", "关联方"),
    PRINCIPAL(6, "principal", "主要方");
    private final Integer code;
    private final String label;
    private final String partyName;
}
