package com.pxb7.mall.trade.ofs.contract.client.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: UserContractReqDTO.java
 * @description: 用户合同信息
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/10/11 19:29
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class UserContractReqDTO implements Serializable {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 游戏账号
     */
    private String gameAccount;
}
