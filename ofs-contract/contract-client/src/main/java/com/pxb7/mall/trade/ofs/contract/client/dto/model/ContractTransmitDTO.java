package com.pxb7.mall.trade.ofs.contract.client.dto.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 合同发卡片数据传参
 *
 * <AUTHOR>
 * @since: 2024-09-02 16:57
 **/
@Data
@Accessors(chain = true)
public class ContractTransmitDTO implements Serializable {
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 订单来源 1:主站；2:主站IM；3:闲鱼；4:支付宝
     */

    private Integer orderSource;
    /**
     * 资料类型1 买家 2 卖家
     */
    private Integer identityType;
    private String buyerUserId;
    private String sellerUserId;
    private String customerId;
    private String deliveryRoomId;
    /**
     * 账号来源  0自己注册 1螃蟹平台 2其他平台
     */
    private Integer accountSource;
    /**
     * 资料提交方手机号
     */
    private String phone;

    /**
     * (10000, 99999)之间随机数 ，同一个资料填写链接，限制只能提交一次
     */
    private Integer rand;

}
