<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pxb7.mall.trade</groupId>
        <artifactId>ofs</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>contract-domain</artifactId>
    <packaging>jar</packaging>
    <name>contract-domain</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>contract-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>contract-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>intelligent-delivery-infrastructure</artifactId>
        </dependency>

        <!-- COLA components -->
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-domain-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-exception</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- COLA components End-->
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-catchlog-starter</artifactId>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-statemachine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.user</groupId>
            <artifactId>user-c-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.merchant</groupId>
            <artifactId>merchant-admin-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>intelligent-delivery-domain</artifactId>
        </dependency>

    </dependencies>


</project>
