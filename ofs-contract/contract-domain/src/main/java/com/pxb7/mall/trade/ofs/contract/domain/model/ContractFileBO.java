package com.pxb7.mall.trade.ofs.contract.domain.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractFileBO.java
 * @description: 合同文件BO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/6 11:50
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ContractFileBO implements Serializable {

    /**
     * 合同文件id
     */
    private String contractFileId;
    /**
     * 合同id
     */
    private String contractDataId;
    /**
     * 订单行id
     */
    private String orderItemId;
    /**
     * 合同类型1包赔证明 2买家 3卖家 4双方(2、3、4为交易合同)
     */
    private Integer contractType;
    /**
     * 合同外部名称
     */
    private String externalName;
    /**
     * 合同内部名称
     */
    private String internalName;
    /**
     * 合同下载地址（contract_type=1取的是ossUrl,交易合同取的是download_url）
     */
    private String documentDownloadUrl;

    /**
     * 合同查看地址
     */
    private String documentViewUrl;
    /**
     * 合同文件上传时间
     */
    private LocalDateTime documentUploadTime;

    /**
     * 归档时间（调法大大接口）
     */
    private LocalDateTime archivingTime;
    /**
     * 归档状态 0否1是
     */
    private Boolean archive;

    /**
     * 文件状态0待签署 1已完成 2已归档
     */
    private Integer fileStatus;

}
