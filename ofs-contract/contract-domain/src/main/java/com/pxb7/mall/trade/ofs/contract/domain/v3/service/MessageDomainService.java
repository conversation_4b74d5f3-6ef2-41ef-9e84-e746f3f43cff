package com.pxb7.mall.trade.ofs.contract.domain.v3.service;

import java.util.function.Supplier;

import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.trade.ofs.common.config.constants.AgreementConstants;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.AutomatedContractReqBO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.ImMessageGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FeiShuGateway;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: MessageDomainService.java
 * @description: 发送消息
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/3/19 17:12
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class MessageDomainService {

    @Resource
    private ImMessageGateway imMessageGateway;
    @Resource
    private FeiShuGateway feiShuGateway;

    /**
     * 发送文本消息
     *
     * @param reqBO
     */
    public void sendImTextInfo(AutomatedContractReqBO reqBO) {
        Supplier<SendTextMsgReqDTO> supplierText = () -> {
            SendTextMsgReqDTO sendTextMsgReqDTO = new SendTextMsgReqDTO();
            sendTextMsgReqDTO.setTargetId(reqBO.getRoomId());
            sendTextMsgReqDTO.setFromUserId(reqBO.getCustomerId());
            sendTextMsgReqDTO.setContent(AgreementConstants.CREATE_BUYER_INDEMNITY_FAIL_TIPS);
            return sendTextMsgReqDTO;
        };
        imMessageGateway.sendTextMessage(supplierText);
    }

    /**
     * 发送催一催消息
     *
     * @param reqBO
     */
    public void sendReminderInfo(AutomatedContractReqBO reqBO) {
        Supplier<SendReminderMsgReqDTO> supplierReminder = () -> {
            SendReminderMsgReqDTO sendReminderMsgReqDTO = new SendReminderMsgReqDTO();
            sendReminderMsgReqDTO.setGroupId(reqBO.getRoomId());
            sendReminderMsgReqDTO.setUserId(reqBO.getBuyerId());
            return sendReminderMsgReqDTO;
        };
        imMessageGateway.sendReminderMsg(supplierReminder);
    }

    /**
     * 发送飞书消息
     *
     * @param reqBO
     * @param errMsg
     * @return
     */
    public String sendFeiShuMsg(AutomatedContractReqBO reqBO, String errMsg) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(AgreementConstants.CREATE_BUYER_INDEMNITY_FAIL_TIPS + ": ")
            .append("\n")
            .append("失败原因：")
            .append(errMsg)
            .append("\n")
            .append("订单号：")
            .append(reqBO.getOrderItemId())
            .append("\n")
            .append("商品编号：")
            .append(reqBO.getProductUniqueNo())
            .append("\n")
            .append("游戏名称：")
            .append(reqBO.getGameName())
            .append("\n")
            .append("通行证账号：")
            .append(reqBO.getPassPermitAccount())
            .append("\n");
        return feiShuGateway.sendFeiShuMsg(sbd.toString());
    }

    public void sendImText(String roomId, String fromUserId, String content) {
        Supplier<SendTextMsgReqDTO> supplierText = () -> {
            SendTextMsgReqDTO sendTextMsgReqDTO = new SendTextMsgReqDTO();
            sendTextMsgReqDTO.setTargetId(roomId);
            sendTextMsgReqDTO.setFromUserId(fromUserId);
            sendTextMsgReqDTO.setContent(content);
            sendTextMsgReqDTO.setTargetUserIds(Lists.newArrayList(fromUserId));
            return sendTextMsgReqDTO;
        };
        imMessageGateway.sendTextMessage(supplierText);
    }

}
