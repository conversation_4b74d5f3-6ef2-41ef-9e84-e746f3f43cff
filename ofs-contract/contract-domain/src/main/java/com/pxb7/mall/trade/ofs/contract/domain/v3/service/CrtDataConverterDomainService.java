package com.pxb7.mall.trade.ofs.contract.domain.v3.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ofs.common.config.constants.AgreementConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.UserConstants;
import com.pxb7.mall.trade.ofs.common.config.util.IdGenUtil;
import com.pxb7.mall.trade.ofs.contract.client.enums.BusScenarioEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractFileStatus;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractPartiesEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractStatusEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractTypeEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractUserType;
import com.pxb7.mall.trade.ofs.contract.client.enums.MainContractDataSource;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractIndemnityBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.DeliveryInfoBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.ContractQuickSignBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.FddTemplateBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.ManualCreateAgreementBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.AgreementRiskReqBO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractRoute;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignInfo;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderItemAmountRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.TradeIdentityRespDTO;
import com.pxb7.mall.trade.order.client.enums.order.MainOrderSourceEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderServiceModeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网 数据组装类
 *
 * <AUTHOR> 2025/3/7 下午5:52
 */
@Service
@Slf4j
public class CrtDataConverterDomainService {
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;

    /**
     * 构建风控参数
     *
     * @param contractData 合同数据
     * @return AgreementRiskReqBO
     */
    public AgreementRiskReqBO buildAgreementRiskReqBO(ContractData contractData) {
        return AgreementRiskReqBO.builder()
            .orderItemId(contractData.getOrderItemId())
            .contractDataId(contractData.getContractDataId())
            .gameId(contractData.getGameId())
            .gameName(contractData.getGameName())
            .productUniqueNo(contractData.getProductUniqueNo())
            .passesNo(contractData.getPassesNo())
            .buyerId(contractData.getBuyerId())
            .sellerId(contractData.getSellerId())
            .buyerIdentity(contractData.getBuyerUserType())
            .sellerIdentity(contractData.getSellerUserType())
            .buyerMerchantId(contractData.getBuyerMerchantId())
            .sellerMerchantId(contractData.getSellerMerchantId())
            .productType(contractData.getBusinessType())
            .build();
    }

    /**
     * 构建合同主数据
     *
     * @param contractId               合同id
     * @param orderInfo                订单信息
     * @param orderItemIndemnity       订单关联的包赔
     * @param contractRoute            路由
     * @param tradeIdentityData        订单买卖家身份信息
     * @param needSignContract         卖家是否需要签署合同
     * @param createUserId             创建人id
     * @return {@link ContractData}
     */
    public ContractData buildContractData(String contractId,
                                          OrderInfoDubboRespDTO orderInfo,
                                          List<OrderItemIndemnityDubboDTO> orderItemIndemnity,
                                          ContractRoute contractRoute,
                                          TradeIdentityRespDTO tradeIdentityData,
                                          Boolean needSignContract,
                                          String createUserId,
                                          DeliveryInfoBO deliveryInfo) {
        ContractData contractData = new ContractData();
        contractData.setContractDataId(contractId);
        contractData.setOrderItemId(orderInfo.getOrderItemId());
        contractData.setRouteId(contractRoute.getRouteId());
        contractData.setSignChannelId(contractRoute.getSignChannelId());
        contractData.setGameId(orderInfo.getGameId());
        contractData.setGameName(orderInfo.getGameName());
        contractData.setProductId(orderInfo.getProductId());
        contractData.setProductUniqueNo(orderInfo.getProductUniqueNo());
        contractData.setBusinessType(orderInfo.getProductType());
        contractData.setOrderType(orderInfo.getServiceMode());
        contractData.setContractNo(StrUtil.addPrefixIfNot(String.valueOf(System.currentTimeMillis()), AgreementConstants.CONTRACT_NO_PREFIX));
        contractData.setBuyerId(orderInfo.getBuyerId());
        contractData.setSellerId(orderInfo.getSellerId());
        List<String> indemnityIds = orderItemIndemnity.stream().map(OrderItemIndemnityDubboDTO::getIndemnityId).toList();
        contractData.setIndemnityIds(indemnityIds);
        contractData.setStartTime(LocalDateTime.now());
        contractData.setDataSource(StrUtil.isNotBlank(createUserId) && !Objects.equals("0", createUserId)
            ? MainContractDataSource.IM_MANUAL_CREATE.getSourceType()
            : MainContractDataSource.IM_AUTO_CREATE.getSourceType());
        contractData.setBusScenario(BusScenarioEnum.ORDER_TRADE.getLabel());
        contractData.setSourceType(MainOrderSourceEnum.MAIN_ONLINE.getValue());
        contractData.setFreeze(Boolean.FALSE);
        contractData.setCreateUserId(StrUtil.isNotBlank(createUserId) && !Objects.equals("0", createUserId)
            ? createUserId : UserConstants.ADMIN_ID);
        contractData.setUpdateUserId(StrUtil.isNotBlank(createUserId) && !Objects.equals("0", createUserId)
            ? createUserId : UserConstants.ADMIN_ID);
        // 操作人为空或者0 获取交付客服信息
        contractData.setCustomerId(StrUtil.isNotBlank(createUserId) && !Objects.equals("0", createUserId) ? createUserId
            : orderInfo.getDeliveryCustomerId());

        if (ObjectUtil.isNotEmpty(tradeIdentityData)) {
            contractData.setBuyerPhone(tradeIdentityData.getBuyerPhone());
            contractData.setSellerPhone(tradeIdentityData.getSellerPhone());
            contractData.setBuyerUserType(tradeIdentityData.getBuyerIdentity());
            contractData.setSellerUserType(tradeIdentityData.getSellerIdentity());
            contractData.setBuyerMerchantId(tradeIdentityData.getBuyerMerchantId());
            contractData.setSellerMerchantId(tradeIdentityData.getSellerMerchantId());
        }
        //号价
        OrderItemAmountRespDTO orderItemAmountRespDTO = orderItemDubboGateway.getDealPrice(orderInfo.getOrderItemId());
        if (ObjectUtil.isNotEmpty(orderItemAmountRespDTO)) {
            contractData.setPreDiscountPrice(orderItemAmountRespDTO.getProductAmount());
            contractData.setDiscountPrice(orderItemAmountRespDTO.getProductActualAmount());
        }
        //合同状态(判断卖家是否需要签署合同)
        if (needSignContract) {
            contractData.setDataCollection(ContractPartiesEnum.VENDOR.getCode());
            contractData.setStatus(ContractStatusEnum.SIGNING.getCode());
        } else {
            contractData.setStatus(ContractStatusEnum.FINISHED.getCode());
            contractData.setFinishTime(LocalDateTime.now());
        }
        log.info("buildContractData,orderItemId:{},contractId:{},deliveryInfo:{}", orderInfo.getOrderItemId(),contractId, JSON.toJSONString(deliveryInfo));
        // 交付信息判断
        if (Objects.nonNull(deliveryInfo)) {
            contractData.setAccountSource(deliveryInfo.getAccountSource());
            contractData.setPassesNo(StrUtil.isNotBlank(deliveryInfo.getAccountUid())
                ? StrUtil.trim(deliveryInfo.getAccountUid()) : (StrUtil.isNotBlank(deliveryInfo.getGameAccount())
                    ? StrUtil.trim(deliveryInfo.getGameAccount()) : StrUtil.trim(orderInfo.getGameAccount())));
        } else {
            contractData.setPassesNo(StrUtil.trim(orderInfo.getGameAccount()));
        }
        return contractData;
    }

    public ContractData buildContractData(String agreementId,
                                          ContractRoute contractRoute,
                                          TradeIdentityRespDTO tradeIdentityData,
                                          Boolean needSignContract,
                                          ManualCreateAgreementBO manualCreateAgreementBO) {
        ContractData contractData = new ContractData();
        contractData.setContractDataId(agreementId);
        contractData.setRouteId(contractRoute.getRouteId());
        contractData.setSignChannelId(contractRoute.getSignChannelId());
        contractData.setBuyerId(manualCreateAgreementBO.getBuyerId());
        contractData.setSellerId(manualCreateAgreementBO.getSellerId());
        contractData.setOrderItemId(manualCreateAgreementBO.getOrderItemId());
        contractData.setGameId(manualCreateAgreementBO.getGameId());
        contractData.setProductId(manualCreateAgreementBO.getProductId());
        contractData.setProductUniqueNo(manualCreateAgreementBO.getProductUniqueNo());
        contractData.setGameName(manualCreateAgreementBO.getGameName());
        contractData.setBusinessType(manualCreateAgreementBO.getProductType());
        contractData.setOrderType(OrderServiceModeEnum.AGENT_SALE.getValue());
        contractData.setContractNo(StrUtil.addPrefixIfNot(String.valueOf(System.currentTimeMillis()), AgreementConstants.CONTRACT_NO_PREFIX));

        //包赔信息
        List<String> indemnityIds = new ArrayList<>();
        indemnityIds.add(manualCreateAgreementBO.getNormalIndemnityBO().getIndemnityId());
        indemnityIds.addAll(manualCreateAgreementBO.getEnhancedIndemnityBO().stream().map(ContractIndemnityBO::getIndemnityId).toList());
        contractData.setIndemnityIds(indemnityIds);
        contractData.setStartTime(LocalDateTime.now());
        contractData.setDataSource(MainContractDataSource.IM_MANUAL_CREATE.getSourceType());
        contractData.setBusScenario(BusScenarioEnum.ORDER_TRADE.getLabel());
        contractData.setSourceType(MainOrderSourceEnum.MAIN_ONLINE.getValue());
        contractData.setFreeze(Boolean.FALSE);
        contractData.setCreateUserId(manualCreateAgreementBO.getCreateUserId());
        contractData.setUpdateUserId(manualCreateAgreementBO.getUpdateUserId());
        contractData.setCustomerId(manualCreateAgreementBO.getCreateUserId());

        contractData.setBuyerUserType(manualCreateAgreementBO.getBuyerIdentity());
        contractData.setSellerUserType(manualCreateAgreementBO.getSellerIdentity());
        contractData.setBuyerMerchantId(manualCreateAgreementBO.getBuyerMerchantId());
        contractData.setSellerMerchantId(manualCreateAgreementBO.getSellerMerchantId());

        //号价
        contractData.setPreDiscountPrice(manualCreateAgreementBO.getPreDiscountPrice());
        contractData.setDiscountPrice(manualCreateAgreementBO.getDiscountPrice());

        if (ObjectUtil.isNotEmpty(tradeIdentityData)) {
            contractData.setBuyerPhone(tradeIdentityData.getBuyerPhone());
            contractData.setSellerPhone(tradeIdentityData.getSellerPhone());
        }
        contractData.setAccountSource(manualCreateAgreementBO.getAccountSource());
        contractData.setPassesNo(StrUtil.trim(manualCreateAgreementBO.getPassesNo()));
        //合同状态(判断卖家是否需要签署合同)
        if (needSignContract) {
            contractData.setDataCollection(ContractPartiesEnum.VENDOR.getCode());
            contractData.setStatus(ContractStatusEnum.SIGNING.getCode());
        } else {
            contractData.setStatus(ContractStatusEnum.FINISHED.getCode());
            contractData.setFinishTime(LocalDateTime.now());
        }
        return contractData;
    }

    /**
     * 构建买家包赔文件
     *
     * @param contractData          合同主数据
     * @param filledFddIndemnityTpl 填充后的包赔模板
     * @param createUserId          创建人id
     * @return {@link ContractFile}
     */
    public ContractFile buildBuyerIndemnityFile(ContractData contractData,
                                                FddTemplateBO filledFddIndemnityTpl,
                                                String createUserId) {
        ContractFile contractFile = new ContractFile();
        contractFile.setContractDataId(contractData.getContractDataId());
        contractFile.setContractFileId(filledFddIndemnityTpl.getContractFileId());
        contractFile.setFileStatus(ContractFileStatus.FINISHED.getCode());
        contractFile.setArchive(Boolean.FALSE);
        contractFile.setOrderItemId(contractData.getOrderItemId());
        contractFile.setContractType(ContractTypeEnum.INDEMNITY_PROVE.getType());
        contractFile.setExternalName(filledFddIndemnityTpl.getExternalName());
        contractFile.setInternalName(filledFddIndemnityTpl.getInternalName());
        contractFile.setDocumentDownloadUrl(filledFddIndemnityTpl.getDownloadUrl());
        contractFile.setDocumentViewUrl(filledFddIndemnityTpl.getViewPdfUrl());
        contractFile.setDocumentUploadTime(LocalDateTime.now());
        contractFile.setCreateUserId(StrUtil.isNotBlank(createUserId) ? createUserId : UserConstants.ADMIN_ID);
        contractFile.setUpdateUserId(StrUtil.isNotBlank(createUserId) ? createUserId : UserConstants.ADMIN_ID);

        return contractFile;
    }

    /**
     * 构建卖家交易合同文件信息
     *
     * @param contractQuickSignBO 快捷签业务模型
     * @param fddTradeTemplateBO  法大大交易合同模板
     * @return {@link ContractFile}
     */
    public ContractFile buildSellerTradeFile(ContractQuickSignBO contractQuickSignBO,
                                             FddTemplateBO fddTradeTemplateBO) {
        ContractFile sellerContractFile = new ContractFile();
        sellerContractFile.setContractDataId(contractQuickSignBO.getContractDataId());
        sellerContractFile.setContractFileId(fddTradeTemplateBO.getContractFileId());
        sellerContractFile.setFileStatus(ContractFileStatus.WAIT_SIGN.getCode());
        sellerContractFile.setArchive(Boolean.FALSE);
        sellerContractFile.setOrderItemId(contractQuickSignBO.getOrderItemId());
        sellerContractFile.setContractType(ContractTypeEnum.SELLER_CONTRACT.getType());
        sellerContractFile.setExternalName(fddTradeTemplateBO.getExternalName());
        sellerContractFile.setInternalName(fddTradeTemplateBO.getInternalName());
        sellerContractFile.setDocumentDownloadUrl(fddTradeTemplateBO.getDownloadUrl());
        sellerContractFile.setDocumentViewUrl(fddTradeTemplateBO.getViewPdfUrl());
        sellerContractFile.setDocumentUploadTime(LocalDateTime.now());
        sellerContractFile.setCreateUserId(contractQuickSignBO.getSellerId());
        sellerContractFile.setUpdateUserId(contractQuickSignBO.getSellerId());
        return sellerContractFile;
    }

    /**
     * 构建合同签署信息
     *
     * @param contractFileId      合同文件id
     * @param contractQuickSignBO 快捷签业务模型
     * @return {@link ContractSignInfo}
     */
    public ContractSignInfo buildContractSignInfo(String contractFileId, ContractQuickSignBO contractQuickSignBO) {
        String contractSignId = IdGenUtil.getId();
        ContractSignInfo contractSignInfo = new ContractSignInfo();
        contractSignInfo.setContractSignId(contractSignId);
        contractSignInfo.setContractFileId(contractFileId);
        contractSignInfo.setContractDataId(contractQuickSignBO.getContractDataId());
        contractSignInfo.setOrderItemId(contractQuickSignBO.getOrderItemId());
        contractSignInfo.setUserId(contractQuickSignBO.getSellerId());
        contractSignInfo.setSignatoryUserId(contractQuickSignBO.getSignatoryId());
        contractSignInfo.setSignatoryType(ContractUserType.SELLER.getCode());
        contractSignInfo.setCustomerId(contractQuickSignBO.getCustomerId());
        contractSignInfo.setSignedStatus(ContractFileStatus.WAIT_SIGN.getCode());
        contractSignInfo.setPeerSignStatus(Boolean.TRUE);
        contractSignInfo.setTransactionId(contractSignId);
        contractSignInfo.setCreateUserId(contractQuickSignBO.getSellerId());
        contractSignInfo.setUpdateUserId(contractQuickSignBO.getSellerId());
        return contractSignInfo;
    }
}
