package com.pxb7.mall.trade.ofs.contract.domain.v3.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantInfoRespDTO;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.AgreementConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.IdGenUtil;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractFileOpEntry;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractFileOpType;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractStatusEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractTypeEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.GuaranteeTypeEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.SysUserType;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractDeleteCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractSignCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractCardDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.ContractQuickSignBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.ContractSignJudgmentBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.FddTemplateBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.FddTemplateVariableBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.AgreementVendorInfoReqBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.OrderItemReqBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.resp.AgreementSignRespBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.resp.AgreementVendorInfoRespBO;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractType;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.IndemnityDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.MerchantGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.UserGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.ExtSignReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.PersonVerifySignReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FddGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractDataRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractFileOperationRecordRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractFileRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractRouteRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractSignInfoRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractSignetRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.TradeDictItemRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFileOperationRecord;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.order.OrderInfoGateway;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.indemnity.ContractIndemnityInfoDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.OrderDeliveryInfoRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.TradeIdentityRespDTO;
import com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeEnum;
import com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeLev2Enum;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import io.vavr.Tuple4;
import io.vavr.Tuple6;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: CrtSignProcessDomainService.java
 * @description: 新版本卖家签署流程服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/2/18 18:03
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class CrtSignProcessDomainService {
    @Resource
    private DataSourceTransactionManager transactionManager;
    @Resource
    private CrtDataConverterDomainService crtDataConverterDomainService;
    @Resource
    private CrtTemplateDomainService crtTemplateDomainService;
    @Resource
    private ContractCardDomainService contractCardDomainService;
    @Resource
    private DeliveryInfoDbRepository deliveryInfoDbRepository;
    @Resource
    private TradeDictItemRepository tradeDictItemRepository;
    @Resource
    private ContractDataRepository contractDataRepository;
    @Resource
    private ContractFileRepository contractFileRepository;
    @Resource
    private ContractFileOperationRecordRepository contractFileOperationRecordRepository;
    @Resource
    private ContractSignInfoRepository contractSignInfoRepository;
    @Resource
    private ContractRouteRepository contractRouteRepository;
    @Resource
    private ContractSignetRepository contractSignetRepository;
    @Resource
    private MerchantGateway merchantGateway;
    @Resource
    private IndemnityDubboGateway indemnityDubboGateway;
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;
    @Resource
    private FddGateway fddGateway;
    @Resource
    private UserGateway userGateway;
    @Resource
    private OrderInfoGateway orderInfoGateway;

    @Value("${fdd.customerId}")
    private String customerId;

    /**
     * 判断卖家是否需要签署合同
     *
     * @param contractSignJudgmentBO 合同是否签署判断业务模型
     * @return {@link Map<String, Boolean> Map<orderItemId, isNeedSignContract> true:需要签署合同，false:不需要签署合同}
     */
    public Map<String, Boolean> isNeedSignContract(ContractSignJudgmentBO contractSignJudgmentBO) {
        Map<String, Boolean> resultMap = new HashMap<>();
        Tuple4<String, Integer, Integer, List<String>> tuple = Tuple.of(contractSignJudgmentBO.getSellerId(), contractSignJudgmentBO.getProductType(), contractSignJudgmentBO.getSellerIdentity(), contractSignJudgmentBO.getOrderItemIdList());
        SysUserType sysUserType = SysUserType.fromCode(tuple._3);
        switch (sysUserType) {
            case MERCHANT -> {
                MerchantInfoRespDTO merchantInfoRespDTO = merchantGateway.getMerchantInfo(tuple._1, tuple._2);
                if (merchantInfoRespDTO.isBusinessFlag() && merchantInfoRespDTO.isContractValidFlag()) {// 卖家是有效期内的商家
                    tuple._4.forEach(orderItemId -> resultMap.put(orderItemId, Boolean.FALSE));
                    return resultMap;
                }
            }
        }
        List<DeliveryInfo> deliveryInfoList = deliveryInfoDbRepository.listDeliveryInfo(tuple._4);
        Map<String, Boolean> orderItemId2RepackageMap = deliveryInfoList.stream()
            .collect(Collectors.groupingBy(DeliveryInfo::getOrderItemId, Collectors.mapping(DeliveryInfo::isRepackage, Collectors.toList())))
            .entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(0)));
        tuple._4.forEach(orderItemId -> resultMap.put(orderItemId, !orderItemId2RepackageMap.getOrDefault(orderItemId, Boolean.FALSE)));
        return resultMap;
    }

    /**
     * 获取合同卖家信息
     *
     * @param agreementVendorInfoReqBO 合同卖家信息
     * @return {@link List<AgreementVendorInfoRespBO>}
     */
    public List<AgreementVendorInfoRespBO> getAgreementVendorInfo(AgreementVendorInfoReqBO agreementVendorInfoReqBO) {
        List<AgreementVendorInfoRespBO> agreementVendorRespList = new ArrayList<>();
        List<String> needToSignOrderItemIdList = new ArrayList<>();
        //判断卖家是否需要签署合同
        ContractSignJudgmentBO contractSignJudgmentBO = ContractSignJudgmentBO.builder()
            .sellerId(agreementVendorInfoReqBO.getSellerId())
            .sellerIdentity(agreementVendorInfoReqBO.getSellerIdentity())
            .build();
        List<String> orderItemIdList = agreementVendorInfoReqBO.getOrderItemReqList()
            .stream()
            .map(OrderItemReqBO::getOrderItemId)
            .toList();
        contractSignJudgmentBO.setOrderItemIdList(orderItemIdList);
        contractSignJudgmentBO.setProductType(agreementVendorInfoReqBO.getOrderItemReqList().get(0).getProductType());
        Map<String, Boolean> signContractTagMap = isNeedSignContract(contractSignJudgmentBO);
        signContractTagMap.forEach((orderItemId, signTag) -> {
            if (signTag) {
                needToSignOrderItemIdList.add(orderItemId);
            } else {
                //手动发起合同会修改合同类型
                String signCheckCacheKey = String.format(RedisConstants.SIGN_CONTRACT_CHECK_KEY, orderItemId);
                Boolean needSignContract = RedissonUtils.getCacheObject(signCheckCacheKey);
                if (ObjectUtil.isNotEmpty(needSignContract) && needSignContract) {
                    needToSignOrderItemIdList.add(orderItemId);
                } else {
                    AgreementVendorInfoRespBO agreementVendorInfoRespBO = new AgreementVendorInfoRespBO();
                    agreementVendorInfoRespBO.setIsNeedSignContract(Boolean.FALSE);
                    agreementVendorInfoRespBO.setOrderItemId(orderItemId);
                    agreementVendorInfoRespBO.setProductUniqueNo("");
                    agreementVendorInfoRespBO.setContractDataId("");
                    agreementVendorInfoRespBO.setProductType(0);
                    agreementVendorRespList.add(agreementVendorInfoRespBO);
                }
            }
        });
        if (CollUtil.isNotEmpty(needToSignOrderItemIdList)) {
            //查询合同数据是否已经生成
            List<ContractData> contractDataList = contractDataRepository.findByOrderItemIds(needToSignOrderItemIdList);
            Map<String, ContractData> contractDataMap = contractDataList.stream()
                .collect(Collectors.toMap(ContractData::getOrderItemId, contractData -> contractData));
            for (String orderItemId : needToSignOrderItemIdList) {
                ContractData contractData = contractDataMap.getOrDefault(orderItemId, null);
                AgreementVendorInfoRespBO agreementVendorInfoRespBO = new AgreementVendorInfoRespBO();

                agreementVendorInfoRespBO.setOrderItemId(orderItemId);
                Boolean contractRecordExist;
                if (ObjectUtil.isNotEmpty(contractData)) {
                    ContractStatusEnum contractStatusEnum = ContractStatusEnum.getStatusEnumByCode(contractData.getStatus());
                    switch (contractStatusEnum) {
                        case FINISHED, CANCELED -> contractRecordExist = Boolean.FALSE;
                        default -> contractRecordExist = Boolean.TRUE;
                    }
                    agreementVendorInfoRespBO.setContractDataId(contractData.getContractDataId());
                    agreementVendorInfoRespBO.setProductType(contractData.getBusinessType());
                    agreementVendorInfoRespBO.setProductUniqueNo(contractData.getProductUniqueNo());
                } else {
                    agreementVendorInfoRespBO.setProductUniqueNo("");
                    agreementVendorInfoRespBO.setContractDataId("");
                    agreementVendorInfoRespBO.setProductType(0);
                    contractRecordExist = Boolean.FALSE;
                }
                agreementVendorInfoRespBO.setIsNeedSignContract(contractRecordExist);
                agreementVendorRespList.add(agreementVendorInfoRespBO);
            }
        }
        return agreementVendorRespList;
    }

    /**
     * 卖家签署合同
     *
     * @param contractQuickSignBO 快捷签合同业务模型
     * @param contractFileOpEntry 合同文件操作入口
     * @return {@link AgreementSignRespBO}签署链接
     */
    public AgreementSignRespBO handleSellerSignProcess(ContractQuickSignBO contractQuickSignBO,
                                                       ContractFileOpEntry contractFileOpEntry) {
        List<ContractIndemnityInfoDTO> indemnityConfigList = indemnityDubboGateway.getIndemnityConfigList(contractQuickSignBO.getIndemnityIds());
        Integer indemnityTypeLev2 = indemnityConfigList.stream()
            .filter(item -> ObjectUtil.equals(item.getIndemnityTypeLev1(), IndemnityTypeEnum.NORMAL_INDEMNITY))
            .map(ContractIndemnityInfoDTO::getIndemnityTypeLev2)
            .findFirst()
            .orElse(IndemnityTypeLev2Enum.FREE_INDEMNITY.getCode());

        List<FddTemplateBO> fddTemplateBOList = crtTemplateDomainService.pullContractTemplate(contractQuickSignBO.getContractDataId(), contractQuickSignBO.getGameId(), indemnityTypeLev2, contractQuickSignBO.getSignChannelId());

        //填充卖家交易合同模板
        FddTemplateBO unfilledFddSellerTradeTpl = fddTemplateBOList.stream()
            .filter(fddTemplateBO -> fddTemplateBO.getTemplateType().equals(GuaranteeTypeEnum.TRADE_CONTRACT.getCode()))
            .findFirst()
            .orElseThrow(() -> new BusinessException(ErrorCode.TRANSACTION_TEMPLATE_NOT_EXIST));
        FddTemplateBO filledFddSellerContractTpl = crtTemplateDomainService.fillFddTemplateVariable(unfilledFddSellerTradeTpl, initFddSellerTradeTplVariable(contractQuickSignBO));

        ContractSignInfo contractSignInfo = crtDataConverterDomainService.buildContractSignInfo(filledFddSellerContractTpl.getContractFileId(), contractQuickSignBO);
        ContractSignInfo hisLatestVendorSignInfo = contractSignInfoRepository.getLatestVendorSignInfo(contractSignInfo);
        String signUrl = generateSellerSignUrl(contractSignInfo.getContractSignId(), contractQuickSignBO, filledFddSellerContractTpl);
        contractSignInfo.setSignUrl(signUrl);
        //平台签或者代签都走自动签署
        extSignAuto(contractQuickSignBO, filledFddSellerContractTpl);

        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(6);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
            ContractFile contractFile = crtDataConverterDomainService.buildSellerTradeFile(contractQuickSignBO, filledFddSellerContractTpl);
            ContractFileOperationRecord contractFileOperationRecord = buildCrtFileOperationRecord(contractQuickSignBO, contractFile, contractSignInfo, contractFileOpEntry);
            String opType;
            if (ObjectUtil.isNotEmpty(hisLatestVendorSignInfo)) {
                contractFileRepository.updateVendorAgreementFile(contractFile);
                contractSignInfoRepository.updateVendorSignedInfo(contractSignInfo);
                opType = ContractFileOpType.UPDATE.getLabel();
            } else {
                contractFileRepository.save(contractFile);
                contractSignInfoRepository.save(contractSignInfo);
                opType = ContractFileOpType.CREATE.getLabel();
            }
            contractFileOperationRecord.setOpType(opType);
            contractFileOperationRecordRepository.save(contractFileOperationRecord);
            transactionManager.commit(status);
        } catch (Throwable e) {
            log.error("[卖家签署合同 本地事务失败], orderItemId: {},contractId:{}", contractQuickSignBO.getOrderItemId(), contractQuickSignBO.getContractDataId(), e);
            transactionManager.rollback(status);
            throw new BusinessException(ErrorCode.CREATE_SELLER_SIGN_INFO_ERROR);
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }

        AgreementSignRespBO signRespBO = new AgreementSignRespBO().setSignUrl(signUrl)
            .setSignatoryUserId(contractQuickSignBO.getSignatoryId())
            .setContractId(contractQuickSignBO.getContractDataId())
            .setContractFileId(filledFddSellerContractTpl.getContractFileId())
            .setOrderItemId(contractQuickSignBO.getOrderItemId())
            .setLastTimeSignInfoId(ObjectUtil.isNotNull(hisLatestVendorSignInfo)
                ? hisLatestVendorSignInfo.getContractSignId()
                : "")
            .setLastTimeImMsgId(ObjectUtil.isNotNull(hisLatestVendorSignInfo)
                ? hisLatestVendorSignInfo.getMsgUid()
                : "")
            .setThisTimeSignInfoId(contractSignInfo.getContractSignId())
            .setSellerId(contractQuickSignBO.getSellerId())
            .setAgreementSource(contractQuickSignBO.getAgreementSource());
        OrderDeliveryInfoRespDTO orderDeliveryInfo = orderItemDubboGateway.getOrderDeliveryInfo(contractQuickSignBO.getOrderItemId());
        if (ObjectUtil.isNotEmpty(orderDeliveryInfo)) {
            signRespBO.setRoomId(orderDeliveryInfo.getDeliveryRoomId())
                .setDeliveryCustomer(orderDeliveryInfo.getDeliveryCustomer())
                .setDeliveryCustomerId(orderDeliveryInfo.getDeliveryCustomerId());
        }
        return signRespBO;
    }

    /**
     * 处理卖家签署卡片
     *
     * @param agreementSignRespBO 协议签署信息
     */
    public void handleVendorSignCard(AgreementSignRespBO agreementSignRespBO) {
        if (ObjectUtil.isEmpty(agreementSignRespBO)) {
            return;
        }
        Tuple6<String, String, String, String, String, String> tuple = Tuple.of(agreementSignRespBO.getSignUrl(), agreementSignRespBO.getThisTimeSignInfoId(), agreementSignRespBO.getRoomId(), agreementSignRespBO.getDeliveryCustomerId(), agreementSignRespBO.getLastTimeImMsgId(), agreementSignRespBO.getSellerId());

        if (StrUtil.isNotBlank(tuple._5)) {
            contractCardDomainService.deleteContractCard(buildBasicDeleteCardBO(tuple._5, tuple._3, tuple._4));
        }
        String msgUid = contractCardDomainService.sendSignContractCard(buildBasicSignCardBO(tuple._1, tuple._3, tuple._4, tuple._6));
        contractSignInfoRepository.lambdaUpdate()
            .eq(ContractSignInfo::getContractSignId, tuple._2)
            .set(ContractSignInfo::getMsgUid, msgUid)
            .update();
    }

    /**
     * 标记历史签署卡片
     *
     * @param agreementSignRespBO 协议签署信息
     */
    public void markHistorySignCard(AgreementSignRespBO agreementSignRespBO) {
        if (ObjectUtil.isEmpty(agreementSignRespBO)) {
            return;
        }
        Tuple3<String, String, String> tuple = Tuple.of(agreementSignRespBO.getLastTimeImMsgId(), agreementSignRespBO.getRoomId(), agreementSignRespBO.getDeliveryCustomerId());
        contractCardDomainService.deleteContractCard(buildBasicDeleteCardBO(tuple._1, tuple._2, tuple._3));
    }

    /**
     * 是否需要发起合同 true 不需要发起合同||已经发起合同  false 需要发起合同却未发起
     * todo 重新完善规则
     *
     * @param orderItemId 订单id
     * @return {@link Boolean true:签署完成，false:签署未完成}
     */
    public Boolean checkAgreementSigned(String orderItemId) {
        OrderItemIndemnityDubboDTO orderIndemnity = orderInfoGateway.getOrderIndemnityInfo(orderItemId);
        OrderInfoDubboRespDTO orderInfo = orderInfoGateway.getOrderInfo(orderItemId);
        TradeIdentityRespDTO tradeIdentityData = orderItemDubboGateway.getTradeIdentity(orderInfo.getOrderItemId());
        ContractData contractData = contractDataRepository.getContractDataWithOrder(orderItemId);
        ContractSignJudgmentBO contractSignJudgmentBO = ContractSignJudgmentBO.builder()
            .sellerId(orderInfo.getSellerId())
            .productType(orderInfo.getProductType())
            .sellerIdentity(ObjectUtil.isNotEmpty(tradeIdentityData)
                ? tradeIdentityData.getSellerIdentity()
                : SysUserType.RETAIL.getCode())
            .orderItemIdList(CollUtil.newArrayList(orderItemId))
            .build();
        Map<String, Boolean> signContractTagMap = isNeedSignContract(contractSignJudgmentBO);
        // 卖家是否需要签署合同 true:需要签署合同，false:不需要签署合同
        Boolean signBool = signContractTagMap.getOrDefault(orderInfo.getOrderItemId(), Boolean.TRUE);

        if (ObjectUtil.isEmpty(orderIndemnity) && !signBool) {
            // 不需要签署合同
            return Boolean.TRUE;
        } else {
            // 需要签署合同
            if (ObjectUtil.isNotEmpty(contractData)) {
                // 已发起合同
                return Boolean.TRUE;
            }
            // 需要签署但未发起合同
            return Boolean.FALSE;
        }
    }

    private ContractFileOperationRecord buildCrtFileOperationRecord(ContractQuickSignBO contractQuickSignBO,
                                                                    ContractFile contractFile,
                                                                    ContractSignInfo contractSignInfo,
                                                                    ContractFileOpEntry contractFileOpEntry) {
        ContractFileOperationRecord contractFileOperationRecord = new ContractFileOperationRecord();
        contractFileOperationRecord.setFddContractId(contractFile.getContractFileId());
        contractFileOperationRecord.setTransactionId(contractSignInfo.getTransactionId());
        contractFileOperationRecord.setFileOperationLogId(IdGenUtil.getId());
        contractFileOperationRecord.setClientType(contractQuickSignBO.getClientType());
        contractFileOperationRecord.setOpIp(contractQuickSignBO.getRemoteIp());
        contractFileOperationRecord.setOpEntry(contractFileOpEntry.getCode());
        contractFileOperationRecord.setDownloadUrl(contractFile.getDocumentDownloadUrl());
        contractFileOperationRecord.setViewUrl(contractFile.getDocumentViewUrl());
        contractFileOperationRecord.setOperatorId(contractQuickSignBO.getSellerId());
        contractFileOperationRecord.setSignUrl(contractSignInfo.getSignUrl());
        return contractFileOperationRecord;
    }

    /**
     * 初始化卖家交易合同模板变量
     *
     * @param contractQuickSignBO 快捷签业务模型
     * @return {@link FddTemplateVariableBO}
     */
    private FddTemplateVariableBO initFddSellerTradeTplVariable(ContractQuickSignBO contractQuickSignBO) {
        LocalDate now = LocalDate.now();
        FddTemplateVariableBO fddTemplateVariableBO = FddTemplateVariableBO.builder()
            .orderItemId(contractQuickSignBO.getOrderItemId())
            .sellerName(contractQuickSignBO.getSignatoryName())
            .agentName(tradeDictItemRepository.getCustomerName(contractQuickSignBO.getBusChannel()))
            .year(now.getYear())
            .month(now.getMonthValue())
            .day(now.getDayOfMonth())
            .build();
        SysUserType principalIdentityType = SysUserType.fromCode(contractQuickSignBO.getPrincipalIdentity());
        switch (principalIdentityType) {
            case RETAIL -> {
                UserCertInfoRespDTO userCertInfo = userGateway.getPersonalUserInfo(contractQuickSignBO.getPrincipalId());
                fddTemplateVariableBO.setBuyerName(ObjectUtil.isNotEmpty(userCertInfo)
                    ? userCertInfo.getCertName()
                    : "");
            }
            case MERCHANT -> {
                MerchantInfoRespDTO merchantInfoRespDTO = merchantGateway.getMerchantInfo(contractQuickSignBO.getPrincipalId(), contractQuickSignBO.getProductType());
                fddTemplateVariableBO.setBuyerName(ObjectUtil.isNotEmpty(merchantInfoRespDTO)
                    ? merchantInfoRespDTO.getContactName()
                    : "");
            }
        }
        return fddTemplateVariableBO;
    }

    /**
     * 生成卖家交易合同签署链接
     *
     * @param contractSignId      签署信息id
     * @param contractQuickSignBO 快捷签业务模型
     * @param fddTradeTemplateBO  法大大交易合同模板
     * @return {@link String 法大大签署链接}
     */
    private String generateSellerSignUrl(String contractSignId,
                                         ContractQuickSignBO contractQuickSignBO,
                                         FddTemplateBO fddTradeTemplateBO) {
        PersonVerifySignReqPO verSignReqPO = new PersonVerifySignReqPO();
        verSignReqPO.setAppId(fddTradeTemplateBO.getAppId());
        verSignReqPO.setAppSecret(fddTradeTemplateBO.getAppSecret());
        verSignReqPO.setContractId(fddTradeTemplateBO.getContractFileId());

        verSignReqPO.setCustomerId(contractQuickSignBO.getCustomerId());
        verSignReqPO.setTransactionId(contractSignId);
        // 允许用户修改认证页面信息 1允许 2不允许
        verSignReqPO.setPageModify("2");
        // 实名认证套餐类型 0：三要素标准方案 1：三要素补充方案
        verSignReqPO.setVerifiedWay("1");
        verSignReqPO.setIdPhotoOptional("2");
        verSignReqPO.setSignKeyword(contractQuickSignBO.getSignatoryName());
        verSignReqPO.setCertType("0");
        verSignReqPO.setCustomerName(contractQuickSignBO.getSignatoryName());
        verSignReqPO.setCustomerIdentNo(contractQuickSignBO.getSignatoryIdentNo());
        verSignReqPO.setMobile(contractQuickSignBO.getSignatoryPhone());
        return fddGateway.personVerifySign(verSignReqPO);
    }

    /**
     * 自动签署
     *
     * @param contractQuickSignBO 快捷签业务模型
     * @param fddTradeTemplateBO  法大大交易合同模板
     */
    private void extSignAuto(ContractQuickSignBO contractQuickSignBO, FddTemplateBO fddTradeTemplateBO) {
        String transactionId = StrUtil.addPrefixIfNot(IdGenUtil.getId(), AgreementConstants.EXT_SIGN_PREFIX);
        log.info("exec extSignAuto, contractId: {}, transactionId: {}", fddTradeTemplateBO.getContractFileId(), transactionId);
        Integer busChannel = contractQuickSignBO.getBusChannel();
        String agentName = tradeDictItemRepository.getCustomerName(busChannel);
        ExtSignReqPO extSignReqPO = new ExtSignReqPO();
        extSignReqPO.setAppId(fddTradeTemplateBO.getAppId());
        extSignReqPO.setAppSecret(fddTradeTemplateBO.getAppSecret());
        extSignReqPO.setCustomerId(customerId);
        extSignReqPO.setSignKeyword(agentName);
        extSignReqPO.setTransactionId(transactionId);
        extSignReqPO.setContractId(fddTradeTemplateBO.getContractFileId());
        extSignReqPO.setDocTitle(fddTradeTemplateBO.getExternalName());
        extSignReqPO.setSignatureId(getSignetNo(contractQuickSignBO.getSignChannelId(), busChannel));
        JSONObject jsonObject = fddGateway.extSignAuto(extSignReqPO).getData();
        if (ObjectUtil.isEmpty(jsonObject)) {
            return;
        }
        String code = jsonObject.getString("code");
        if (!StrUtil.equals(code, "1000")) {
            String msg = jsonObject.getString("msg");
            log.error("extSignAuto failed, contractId:{}, transactionId:{}, errCode:{}, errMsg:{}", fddTradeTemplateBO.getContractFileId(), transactionId, code, jsonObject.getString("msg"));
            throw new BizException(ErrorCode.FDD_AUTO_EXT_SIGN_FAIL.getErrCode(), String.format(ErrorCode.FDD_AUTO_EXT_SIGN_FAIL.getErrDesc(), fddTradeTemplateBO.getContractFileId(), msg));
        }
    }

    /**
     * 构建卖家交易合同签署卡片
     *
     * @param signUrl            签署链接
     * @param deliveryRoomId     交付房间
     * @param deliveryCustomerId 交付客服
     * @param sellerId           卖家id
     * @return {@link ContractSignCardBO}
     */
    private ContractSignCardBO buildBasicSignCardBO(String signUrl,
                                                    String deliveryRoomId,
                                                    String deliveryCustomerId,
                                                    String sellerId) {

        ContractSignCardBO cardBO = new ContractSignCardBO();
        cardBO.setSellerSignUrl(signUrl);
        cardBO.setContractType(ContractType.CLAIM_SELLER);
        cardBO.setSellerUserId(sellerId);
        cardBO.setRoomId(deliveryRoomId);
        cardBO.setCustomerUserId(deliveryCustomerId);
        return cardBO;
    }

    /**
     * 构建删除签署卡片
     *
     * @param msgUid             消息id
     * @param deliveryRoomId     交付房间id
     * @param deliveryCustomerId 交付客服id
     * @return {@link ContractDeleteCardBO}
     */
    private ContractDeleteCardBO buildBasicDeleteCardBO(String msgUid,
                                                        String deliveryRoomId,
                                                        String deliveryCustomerId) {
        ContractDeleteCardBO contractDeleteCardBO = new ContractDeleteCardBO();
        contractDeleteCardBO.setDeliveryRoomId(deliveryRoomId);
        contractDeleteCardBO.setCustomerUserId(deliveryCustomerId);
        contractDeleteCardBO.setContractType(ContractTypeEnum.SELLER_CONTRACT.getType());
        contractDeleteCardBO.setMsgUID(msgUid);
        return contractDeleteCardBO;
    }

    /**
     * 获取印章编号
     *
     * @param signChannelId 电子签渠道id
     * @param busChannel    业务渠道1:主站2:支付宝
     * @return 印章编号
     */
    private String getSignetNo(String signChannelId, Integer busChannel) {
        String signetId = contractRouteRepository.getSignetIdByChannelIdAndSourceType(signChannelId, busChannel);
        if (StrUtil.isBlank(signetId)) {
            throw new BusinessException(ErrorCode.CONTRACT_SIGNET_NOT_CONFIGURED);
        }
        return contractSignetRepository.getOptById(signetId)
            .orElseThrow(() -> new BusinessException(ErrorCode.CONTRACT_SIGNET_NOT_CONFIGURED))
            .getSignetNo();
    }
}
