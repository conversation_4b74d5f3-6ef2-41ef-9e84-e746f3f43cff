package com.pxb7.mall.trade.ofs.contract.domain.v3.model.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AgreementVendorInfoRespBO.java
 * @description: 合同卖家信息返回模型
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/2/24 17:07
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class AgreementVendorInfoRespBO implements Serializable {
    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 是否需要签署合同
     */
    private Boolean isNeedSignContract;

    /**
     * 合同id
     */
    private String contractDataId;

    /**
     * 商品类型 1:账号 2:充值 3:金币 4:装备 5:初始号 20:诚心卖
     *
     * @see com.pxb7.mall.trade.order.client.enums.order.ProductTypeEnum
     */
    private Integer productType;

    /**
     * 商品编码
     */
    private String productUniqueNo;

}
