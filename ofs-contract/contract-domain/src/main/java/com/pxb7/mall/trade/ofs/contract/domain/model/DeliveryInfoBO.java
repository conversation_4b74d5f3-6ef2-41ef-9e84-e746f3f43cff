package com.pxb7.mall.trade.ofs.contract.domain.model;

/**
 * <AUTHOR>
 * @date 2025/06/27 15:12
 **/

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/06/27 15:06
 **/
@Data
public class DeliveryInfoBO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9047148116559740124L;

    private Long  id;
    /**
     * 游戏账号
     */
    private String gameAccount;
    /**
     * 账号来源
     */
    private Integer accountSource;
    /**
     * 账号通行证
     */
    private String accountUid;

    /**
     * 是否续包
     */
    private boolean repackage;
}
