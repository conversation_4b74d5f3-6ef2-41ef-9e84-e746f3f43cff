package com.pxb7.mall.trade.ofs.contract.domain.v3.model.req;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: VendorInformationReqBO.java
 * @description: 卖家资料快照参数
 * @author: guo<PERSON>qiang
 * @email: <EMAIL>
 * @date: 2025/2/27 14:37
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class VendorInfoSnapshotReqBO implements Serializable {

    /**
     * 订单id
     */
    private String orderItemId;
    /**
     * 卖家(乙方)id
     */
    private String sellerId;
    /**
     * 卖家(乙方)姓名
     */
    private String sellerName;
    /**
     * 证件类型 1:身份证 2:港澳通行证 3:台湾通行证 4:护照
     */
    private Integer certType;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证正面照片
     */
    private String certFrontImg;
    /**
     * 身份证反面照片
     */
    private String certBackImg;
    /**
     * 合同来源 1:IM 2:订单中心
     */
    private Integer agreementSource;
    /**
     * 合同id
     */
    private String agreementId;

    /**
     * (10000, 99999)之间随机数 ，同一个资料填写链接，限制只能提交一次
     */
    private Integer rand;

    /**
     * IM(融云)卡片消息ID
     */
    private String msgUid;
}
