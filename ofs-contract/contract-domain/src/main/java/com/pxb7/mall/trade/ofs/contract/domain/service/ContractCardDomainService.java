package com.pxb7.mall.trade.ofs.contract.domain.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonConUrl;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientCommon;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientTypeBuild;
import com.pxb7.mall.im.client.dto.request.card.button.styletype.Primary;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeBlank;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeWebUrl;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYQX;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYSX;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYWC;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.util.DubboResultAssert;
import com.pxb7.mall.trade.ofs.contract.client.dto.model.ContractTransmitDTO;
import com.pxb7.mall.trade.ofs.contract.domain.mapping.ContractDataDomainMapping;
import com.pxb7.mall.trade.ofs.contract.domain.model.CollectMaterialCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractDeleteCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractFinishCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractFinishCommitBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractFreezeCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractInitiateReqBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractSignCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractUrgeCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractUrgeMsgBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.InternalMsgParamsBO;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractButtonMsg;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractCardTag;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractCardTitle;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractMessage;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractNoticeMessage;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractUserType;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractMaterialSnapshotRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractMaterialSnapshot;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.ContractCollectUrlRespPO;
import com.pxb7.mall.trade.order.client.dto.response.order.OrderDeliveryInfoRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 合同卡片
 *
 * <AUTHOR>
 * @since: 2024-09-03 10:13
 **/
@Service
@Slf4j
public class ContractCardDomainService {

    @Resource
    private InternalMessageDomainService internalMessageDomainService;
    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;

    @Value("${contract.notice.limitTime}")
    private Integer noticeLimitTime;

    @Resource
    private ContractMaterialSnapshotRepository contractMaterialSnapshotRepository;

    /**
     * 发送买家资料收集卡片
     *
     * @param collectMaterialCardBO
     * @param urlList
     * @return
     */
    public String sendBuyerInfoCollectCard(CollectMaterialCardBO collectMaterialCardBO,
                                           List<ContractCollectUrlRespPO> urlList) {
        String roomId = collectMaterialCardBO.getDeliveryRoomId();
        String buyerUserId = collectMaterialCardBO.getBuyerUserId();
        ContractMaterialSnapshot buyerMaterialSnapshot = contractMaterialSnapshotRepository.queryContractMaterialSnapshot(roomId, collectMaterialCardBO.getOrderItemId(), buyerUserId, ContractUserType.BUYER.getCode());
        if (ObjectUtil.isNotEmpty(buyerMaterialSnapshot)) {
            //上一次的卡片做失效处理
            SetExpansionDTO expansionDTO = new SetExpansionDTO().setTargetId(buyerMaterialSnapshot.getDeliveryRoomId())
                .setOperation(ContractCardTag.COLLECT_BUYER_INFORMATION.getTag())
                .setFormUserId(buyerMaterialSnapshot.getCustomerId())
                .setMsgUID(buyerMaterialSnapshot.getMsgUid())
                .setWaterMark(new WaterMarkYSX());
            sendMsgServiceI.setExpansion(expansionDTO).getData();
        }

        // 封装按钮信息
        List<ButtonConUrl> buttonConUrlList = urlList.stream()
            .map(item -> new ButtonConUrl().setUrl(item.getUrl())
                .setClientType(ClientTypeBuild.build(item.getClientType()))
                .setTargetType(new TargetTypeBlank()))
            .toList();
        // 页面传参构建
        Integer rand = RandomUtil.randomInt(100000, 499999);
        ContractTransmitDTO transmitDTO = ContractDataDomainMapping.INSTANCE.toContractTransmitDTO(collectMaterialCardBO);
        transmitDTO.setIdentityType(ContractUserType.BUYER.getCode()).setRand(rand);
        ButtonCon buttonContent = new ButtonCon().setLabel(ContractButtonMsg.CONFIRM.getMsg())
            .setDisabled(Boolean.FALSE)
            .setButtonUrl(buttonConUrlList)
            .setType(new Primary())
            .setParam(transmitDTO);
        // 封装消息体
        SendRichTextMsgReqDTO reqDTO = new SendRichTextMsgReqDTO();
        reqDTO.setTargetId(roomId);
        reqDTO.setFromUserId(collectMaterialCardBO.getCustomerUserId());
        reqDTO.setTitle(ContractCardTitle.BUYER_INFO.getTitle());
        reqDTO.setMentionedIds(Collections.singletonList(buyerUserId));
        String noticeContent = String.format(ContractNoticeMessage.BUYER_INFO.getMessage(), noticeLimitTime);
        reqDTO.setContent(List.of(new RichTextContent(ContractMessage.BUYER_BASE_INFO.getMessage()), new RichTextContent(noticeContent)));
        reqDTO.setButtons(Collections.singletonList(buttonContent));
        reqDTO.setOperation(ContractCardTag.COLLECT_BUYER_INFORMATION.getTag());
        // 谁能操作按钮
        reqDTO.setOperatorUserIds(Collections.singletonList(buyerUserId));

        SingleResponse<String> response = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(reqDTO), ErrorCode.SEND_COLLECT_BUYER_INFO_CARD_FAIL);
        String msgId = response.getData();

        ContractMaterialSnapshot contractMaterialSnapshot = ContractDataDomainMapping.INSTANCE.toContractMaterialSnapshot(collectMaterialCardBO);
        contractMaterialSnapshot.setRand(rand);
        contractMaterialSnapshot.setUserId(buyerUserId);
        contractMaterialSnapshot.setMsgUid(msgId);
        contractMaterialSnapshot.setUserType(ContractUserType.BUYER.getCode());
        contractMaterialSnapshotRepository.save(contractMaterialSnapshot);
        CompletableFuture.runAsync(() -> {
            InternalMsgParamsBO internalMsgParamsBO = ContractDataDomainMapping.INSTANCE.toInternalMsgParamsBO(transmitDTO);
            internalMsgParamsBO.setMsgId(msgId);
            internalMsgParamsBO.setMsgUid(msgId);
            internalMsgParamsBO.setRoomId(roomId);
            internalMessageDomainService.sendCollectBuyerMessage(internalMsgParamsBO);
        });
        return msgId;

    }

    /**
     * 发送卖家资料收集卡片
     *
     * @param collectMaterialCardBO
     * @param urlList
     * @return
     */
    public String sendSellerInfoCollectCard(CollectMaterialCardBO collectMaterialCardBO,
                                            List<ContractCollectUrlRespPO> urlList) {
        String roomId = collectMaterialCardBO.getDeliveryRoomId();
        String sellerUserId = collectMaterialCardBO.getSellerUserId();
        ContractMaterialSnapshot sellerMaterialSnapshot = contractMaterialSnapshotRepository.queryContractMaterialSnapshot(roomId, collectMaterialCardBO.getOrderItemId(), sellerUserId, ContractUserType.SELLER.getCode());
        if (ObjectUtil.isNotEmpty(sellerMaterialSnapshot)) {
            //上一次的卡片做失效处理
            SetExpansionDTO expansionDTO = new SetExpansionDTO().setTargetId(sellerMaterialSnapshot.getDeliveryRoomId())
                .setOperation(ContractCardTag.COLLECT_SELLER_INFORMATION.getTag())
                .setFormUserId(sellerMaterialSnapshot.getCustomerId())
                .setMsgUID(sellerMaterialSnapshot.getMsgUid())
                .setWaterMark(new WaterMarkYQX());
            log.info("sendSellerInfoCollectCard.expansionDTO:{}", JSON.toJSONString(expansionDTO));
            sendMsgServiceI.setExpansion(expansionDTO).getData();
        }

        // 封装按钮信息
        List<ButtonConUrl> buttonConUrlList = urlList.stream()
            .map(item -> new ButtonConUrl().setClientType(ClientTypeBuild.build(item.getClientType()))
                .setUrl(item.getUrl())
                .setTargetType(new TargetTypeBlank()))
            .toList();
        // 页面传参构建
        Integer rand = RandomUtil.randomInt(100000, 499999);
        ContractTransmitDTO transmitDTO = ContractDataDomainMapping.INSTANCE.toContractTransmitDTO(collectMaterialCardBO);
        transmitDTO.setIdentityType(ContractUserType.SELLER.getCode()).setRand(rand);
        ButtonCon buttonContent = new ButtonCon().setLabel(ContractButtonMsg.CONFIRM.getMsg())
            .setDisabled(Boolean.FALSE)
            .setButtonUrl(buttonConUrlList)
            .setType(new Primary())
            .setParam(transmitDTO);
        // 封装消息体
        SendRichTextMsgReqDTO reqDTO = new SendRichTextMsgReqDTO();
        reqDTO.setTargetId(roomId);
        reqDTO.setFromUserId(collectMaterialCardBO.getCustomerUserId());
        reqDTO.setTitle(ContractCardTitle.SELLER_INFO.getTitle());
        // @用户列表
        reqDTO.setMentionedIds(Collections.singletonList(sellerUserId));
        reqDTO.setContent(List.of(new RichTextContent(ContractMessage.SELLER_BASE_INFO.getMessage()), new RichTextContent(ContractNoticeMessage.SELLER_INFO.getMessage())));
        reqDTO.setButtons(Collections.singletonList(buttonContent));
        reqDTO.setOperation(ContractCardTag.COLLECT_SELLER_INFORMATION.getTag());
        // 谁能操作按钮
        reqDTO.setOperatorUserIds(Collections.singletonList(sellerUserId));
        SingleResponse<String> response = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(reqDTO), ErrorCode.SEND_COLLECT_SELLER_INFO_CARD_FAIL);
        String msgId = response.getData();

        ContractMaterialSnapshot contractMaterialSnapshot = ContractDataDomainMapping.INSTANCE.toContractMaterialSnapshot(collectMaterialCardBO);
        contractMaterialSnapshot.setRand(rand);
        contractMaterialSnapshot.setUserId(sellerUserId);
        contractMaterialSnapshot.setMsgUid(msgId);
        contractMaterialSnapshot.setUserType(ContractUserType.SELLER.getCode());
        contractMaterialSnapshotRepository.save(contractMaterialSnapshot);
        CompletableFuture.runAsync(() -> {
            InternalMsgParamsBO internalMsgParamsBO = ContractDataDomainMapping.INSTANCE.toInternalMsgParamsBO(transmitDTO);
            internalMsgParamsBO.setMsgId(msgId);
            internalMsgParamsBO.setMsgUid(msgId);
            internalMsgParamsBO.setRoomId(roomId);
            internalMessageDomainService.sendCollectSellerMessage(internalMsgParamsBO);
        });
        return msgId;
    }

    /**
     * 资料提交完成发卡
     */
    public Boolean sendCollectFinishCard(ContractFinishCommitBO commitBO) {
        String userId = commitBO.getFromUserId();
        //修改旧消息的信息
        SetExpansionDTO expansionDTO = new SetExpansionDTO().setTargetId(commitBO.getDeliveryRoomId())
            .setFormUserId(userId)
            .setMsgUID(commitBO.getMsgId())
            .setWaterMark(new WaterMarkYWC());
        //新发一条新的消息通知
        SendTextMsgReqDTO reqDTO = new SendTextMsgReqDTO().setFromUserId(userId)
            .setTargetId(commitBO.getDeliveryRoomId())
            .setMentionedInfo(Collections.singletonList(commitBO.getCustomerId()));
        //根据不同的用户类型设置不同的按钮文本和卡片操作类型
        switch (commitBO.getUserType()) {
            case BUYER -> {
                reqDTO.setContent(ContractMessage.BUYER_COLLECT.getMessage());
                expansionDTO.setOperation(ContractCardTag.COLLECT_BUYER_INFORMATION.getTag());
            }
            case SELLER -> {
                reqDTO.setContent(ContractMessage.SELLER_COLLECT.getMessage());
                expansionDTO.setOperation(ContractCardTag.COLLECT_SELLER_INFORMATION.getTag());
            }
        }
        sendMsgServiceI.setExpansion(expansionDTO);
        return sendMsgServiceI.sendTextMsg(reqDTO).getData();
    }

    /**
     * 发起合同签署卡片
     */
    public String sendSignContractCard(ContractSignCardBO cardBO) {
        return switch (cardBO.getContractType()) {
            //包赔证明
            case CLAIM -> sendClaimCard(cardBO);
            //买家包赔证明 + 卖家合同
            case CLAIM_SELLER -> sendClaimSellerCard(cardBO);
            //买家合同
            case BUYER_CONTRACT -> sendBuyerContractCard(cardBO);
            //双方合同
            case TOGETHER -> sendTogetherCard(cardBO);
        };

    }

    /**
     * 风控-合同被冻结时发送卡片,并且需要用户（买家）催一催群主
     */
    public Boolean sendFreezeCard(ContractFreezeCardBO freezeCardBO) {
        SendTextMsgReqDTO textMsg = new SendTextMsgReqDTO().setFromUserId(freezeCardBO.getCustomerUserId())
            .setTargetId(freezeCardBO.getDeliveryRoomId())
            .setContent(ContractMessage.FREEZE.getMessage());
        SingleResponse<Boolean> freezeMsgResp = DubboResultAssert.wrapException(() -> sendMsgServiceI.sendTextMsg(textMsg), ErrorCode.SEND_FREEZE_CARD_FAIL);

        //买家发送催一催提醒消息
        SendReminderMsgReqDTO sendReminderMsgReqDTO = new SendReminderMsgReqDTO();
        sendReminderMsgReqDTO.setGroupId(freezeCardBO.getDeliveryRoomId());
        sendReminderMsgReqDTO.setUserId(freezeCardBO.getBuyerUserId());
        DubboResultAssert.wrapException(() -> sendMsgServiceI.sendRemindMsg(sendReminderMsgReqDTO), ErrorCode.SEND_CONTRACT_FREEZE_REMIND_MSG_ERROR);
        return freezeMsgResp.getData();
    }

    /**
     * 合同签署完成发消息 原签署卡片取消按钮显示, 打上已完成的水印,需要用户（买家）催一催群主
     */
    public boolean finishContractCard(ContractFinishCardBO cardBO) {
        //合同签署卡片加已完成水印
        SetExpansionDTO expansionDTO = new SetExpansionDTO().setTargetId(cardBO.getDeliveryRoomId())
            .setOperation(analysisContractType(cardBO.getContractType()))
            .setFormUserId(cardBO.getCustomerUserId())
            .setWaterMark(new WaterMarkYWC());
        if (StrUtil.isNotBlank(cardBO.getMsgUID())) {
            expansionDTO.setMsgUID(cardBO.getMsgUID());
        }
        sendMsgServiceI.setExpansion(expansionDTO).getData();
        //发普通文本消息
        SendTextMsgReqDTO msg = new SendTextMsgReqDTO().setContent(ContractMessage.FINISH_CONTRACT.getMessage())
            .setTargetId(cardBO.getDeliveryRoomId())
            .setFromUserId(cardBO.getCustomerUserId());
        //发送催一催消息
        SendReminderMsgReqDTO sendReminderMsgReqDTO = new SendReminderMsgReqDTO();
        sendReminderMsgReqDTO.setGroupId(cardBO.getDeliveryRoomId());
        sendReminderMsgReqDTO.setUserId(cardBO.getBuyerUserId());
        DubboResultAssert.wrapException(() -> sendMsgServiceI.sendRemindMsg(sendReminderMsgReqDTO), ErrorCode.SEND_CONTRACT_SIGN_FINISHED_REMIND_MSG_ERROR);
        return sendMsgServiceI.sendTextMsg(msg).getData();
    }

    /**
     * 发送催一催消息
     *
     * @param urgeMsgBO
     * @return
     */
    public boolean sendUrgeMsg(ContractUrgeMsgBO urgeMsgBO) {
        //发送催一催消息
        SendReminderMsgReqDTO sendReminderMsgReqDTO = new SendReminderMsgReqDTO();
        sendReminderMsgReqDTO.setGroupId(urgeMsgBO.getRoomId());
        sendReminderMsgReqDTO.setUserId(urgeMsgBO.getBuyerId());
        SingleResponse<Boolean> urgeResp = DubboResultAssert.wrapException(() -> sendMsgServiceI.sendRemindMsg(sendReminderMsgReqDTO), ErrorCode.SEND_CONTRACT_SIGN_FINISHED_REMIND_MSG_ERROR);
        return urgeResp.getData();
    }

    /**
     * 删除合同-作废签署的合同卡片 原签署卡片取消按钮显示, 打上已取消的水印
     */
    public boolean deleteContractCard(ContractDeleteCardBO cardBO) {
        SetExpansionDTO expansionDTO = new SetExpansionDTO().setOperation(analysisContractType(cardBO.getContractType()))
            .setFormUserId(cardBO.getCustomerUserId())
            .setTargetId(cardBO.getDeliveryRoomId())
            .setWaterMark(new WaterMarkYQX());
        if (StrUtil.isNotBlank(cardBO.getMsgUID())) {
            expansionDTO.setMsgUID(cardBO.getMsgUID());
        }
        return sendMsgServiceI.setExpansion(expansionDTO).getData();
    }

    /**
     * 催促另一方签署 发送普通消息并@被催促方 + 双方重新发送合同签署卡片(不需要@)
     */
    public boolean sendUrgeCard(ContractUrgeCardBO cardBO) {
        //普通催一催消息
        SendTextMsgReqDTO msg = new SendTextMsgReqDTO().setFromUserId(cardBO.getCustomerUserId())
            .setTargetId(cardBO.getDeliveryRoomId());
        //合同签署卡片
        SendRichTextMsgReqDTO card = new SendRichTextMsgReqDTO().setTargetId(cardBO.getDeliveryRoomId())
            .setFromUserId(cardBO.getCustomerUserId())
            .setTitle(ContractCardTitle.TOGETHER_SIGN.getTitle())
            .setOperation(ContractCardTag.URGE_MESSAGE.getTag())
            .setContent(Arrays.asList(new RichTextContent(ContractMessage.TOGETHER_SIGN.getMessage()), new RichTextContent(ContractMessage.NOTICE.getMessage())));

        switch (cardBO.getUserType()) {
            //卖家已签完,催促买家
            case BUYER -> {
                String buyerId = cardBO.getBuyerUserId();
                new ButtonCon();
                msg.setContent(ContractMessage.BUYER_URGE.getMessage())
                    .setMentionedInfo(Collections.singletonList(buyerId));

                ButtonCon buyerButtonCon = new ButtonCon().setLabel(ContractButtonMsg.BUYER_SIGN.getMsg())
                    .setButtonUrl(List.of(new ButtonConUrl().setUrl(cardBO.getBuyerSignUrl())
                        .setClientType(new ClientCommon())
                        .setTargetType(new TargetTypeWebUrl())))
                    .setOperationUserIds(buyerId);
                card.setOperatorUserIds(Collections.singletonList(buyerId))
                    .setButtons(Collections.singletonList(buyerButtonCon));
            }
            //买家已签完,催促卖家
            case SELLER -> {
                String sellerId = cardBO.getSellerUserId();
                msg.setContent(ContractMessage.SELLER_URGE.getMessage())
                    .setMentionedInfo(Collections.singletonList(sellerId));

                ButtonCon sellerButtonCon = new ButtonCon().setLabel(ContractButtonMsg.SELLER_SIGN.getMsg())
                    .setButtonUrl(List.of(new ButtonConUrl().setUrl(cardBO.getSellerSignUrl())
                        .setClientType(new ClientCommon())
                        .setTargetType(new TargetTypeWebUrl())))
                    .setOperationUserIds(sellerId);
                card.setOperatorUserIds(Collections.singletonList(sellerId))
                    .setButtons(Collections.singletonList(sellerButtonCon));
            }
        }
        //发送普通催一催消息
        DubboResultAssert.wrapException(() -> sendMsgServiceI.sendTextMsg(msg), ErrorCode.SEND_URGE_CARD_FAIL);

        //发送合同签署卡片
        SingleResponse<String> cardResp = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(card), ErrorCode.SEND_SELLER_URGE_CARD_FAIL);
        return ObjectUtil.isNotEmpty(cardResp.getData());
    }

    /**
     * 构建基础签署卡片参数
     *
     * @param contractInitiateReqBO
     * @return
     */
    public ContractSignCardBO buildBasicSignCardBO(ContractInitiateReqBO contractInitiateReqBO) {
        var buyerId = contractInitiateReqBO.getBuyerId();
        var sellerId = contractInitiateReqBO.getSellerId();
        ContractSignCardBO contractSignCardBO = new ContractSignCardBO();
        contractSignCardBO.setBuyerUserId(buyerId);
        contractSignCardBO.setSellerUserId(sellerId);
        contractSignCardBO.setRoomId(ObjectUtil.isNotEmpty(contractInitiateReqBO.getRoomId())
            ? contractInitiateReqBO.getRoomId()
            : null);
        contractSignCardBO.setCustomerUserId(ObjectUtil.isNotEmpty(contractInitiateReqBO.getCustomerUserId())
            ? contractInitiateReqBO.getCustomerUserId()
            : null);
        return contractSignCardBO;
    }

    /**
     * 删除合同发送卡片，原卡片打上已取消的水印
     *
     * @param orderDeliveryInfo 交付信息
     * @param contractType      合同类型 2买家合同 3卖家合同 4双方合同
     * @param msgUid            融云消息id
     */
    public void sendDeleteContractCard(OrderDeliveryInfoRespDTO orderDeliveryInfo,
                                       Integer contractType,
                                       String msgUid) {
        ContractDeleteCardBO deleteCardReqDTO = new ContractDeleteCardBO();
        deleteCardReqDTO.setCustomerUserId(orderDeliveryInfo.getDeliveryCustomerId());
        deleteCardReqDTO.setDeliveryRoomId(orderDeliveryInfo.getDeliveryRoomId());
        deleteCardReqDTO.setContractType(contractType);
        deleteCardReqDTO.setMsgUID(msgUid);
        this.deleteContractCard(deleteCardReqDTO);
    }

    /**
     * 场景一: 买家包赔证明 发送给买家、卖家和客服(需要@买家)
     */
    private String sendClaimCard(ContractSignCardBO cardBO) {
        SendRichTextMsgReqDTO card = new SendRichTextMsgReqDTO().setTargetId(cardBO.getRoomId())
            .setFromUserId(cardBO.getCustomerUserId())
            .setTitle(ContractCardTitle.BUYER_CLAIM.getTitle())
            .setOperation(ContractCardTag.GENERATE_BUYER_INDEMNITY_PROVE.getTag())
            .setMentionedIds(Collections.singletonList(cardBO.getBuyerUserId()))
            .setContent(Collections.singletonList(new RichTextContent(ContractMessage.BUYER_CLAIM.getMessage())));
        SingleResponse<String> cardResp = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(card), ErrorCode.SEND_BUYER_CLAIM_CARD_FAIL);

        return cardResp.getData();
    }

    /**
     * 场景二: 买家包赔证明 + 卖家合同 发送卖家合同卡片,发送给买卖双方+客服(需要@卖家),且卖家有签署按钮的操作权限
     */
    private String sendClaimSellerCard(ContractSignCardBO cardBO) {
        //发送卖家签署卡片，发送给买卖双方+客服 @卖家
        ButtonCon sellerCon = new ButtonCon();
        sellerCon.setLabel(ContractButtonMsg.SELLER_SIGN.getMsg())
            .setButtonUrl(List.of(new ButtonConUrl().setUrl(cardBO.getSellerSignUrl())
                .setClientType(new ClientCommon())
                .setTargetType(new TargetTypeWebUrl())));
        SendRichTextMsgReqDTO card = new SendRichTextMsgReqDTO().setTargetId(cardBO.getRoomId())
            .setFromUserId(cardBO.getCustomerUserId())
            .setTitle(ContractCardTitle.SELLER_SIGN.getTitle())
            .setMentionedIds(Collections.singletonList(cardBO.getSellerUserId()))
            .setContent(Arrays.asList(new RichTextContent(ContractMessage.SELLER_SIGN.getMessage()), new RichTextContent(ContractMessage.NOTICE.getMessage())))
            .setOperatorUserIds(Collections.singletonList(cardBO.getSellerUserId()))
            .setButtons(Collections.singletonList(sellerCon))
            .setOperation(ContractCardTag.SIGN_SELLER_TRADE_CONTRACT.getTag());
        SingleResponse<String> sendCardResp = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(card), ErrorCode.SEND_SELLER_CONTRACT_SIGN_CARD_FAIL);
        return sendCardResp.getData();
    }

    /**
     * 场景三: 买家合同 发一个卡片,发给买卖双方 + 客服,需要@买家,且买家有按钮
     */
    private String sendBuyerContractCard(ContractSignCardBO cardBO) {
        ButtonCon buyerButtonCon = new ButtonCon();
        buyerButtonCon.setLabel(ContractButtonMsg.BUYER_SIGN.getMsg())
            .setButtonUrl(List.of(new ButtonConUrl().setUrl(cardBO.getBuyerSignUrl())
                .setClientType(new ClientCommon())
                .setTargetType(new TargetTypeWebUrl())));
        //定义卡片
        SendRichTextMsgReqDTO card = new SendRichTextMsgReqDTO().setTargetId(cardBO.getRoomId())
            .setFromUserId(cardBO.getCustomerUserId())
            .setTitle(ContractCardTitle.BUYER_SIGN.getTitle())
            .setMentionedIds(Collections.singletonList(cardBO.getBuyerUserId()))
            .setContent(Arrays.asList(new RichTextContent(ContractMessage.BUYER_SIGN.getMessage()), new RichTextContent(ContractMessage.NOTICE.getMessage())))
            .setOperatorUserIds(Collections.singletonList(cardBO.getBuyerUserId()))
            .setOperation(ContractCardTag.SIGN_BUYER_TRADE_CONTRACT.getTag())
            .setButtons(Collections.singletonList(buyerButtonCon));
        SingleResponse<String> cardResp = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(card), ErrorCode.SEND_BUYER_CONTRACT_SIGN_CARD_FAIL);
        return cardResp.getData();

    }

    /**
     * 场景四: 双方合同 @买卖双方、买卖家有操作按钮的权限、客服只能查看
     */
    private String sendTogetherCard(ContractSignCardBO cardBO) {
        List<ButtonCon> togetherButtonConList = new ArrayList<>();
        //发给买卖双方和客服 需要@买卖双方 + 买、卖家签署按钮
        ButtonCon buyerButton = new ButtonCon().setLabel(ContractButtonMsg.BUYER_SIGN.getMsg())
            .setButtonUrl(List.of(new ButtonConUrl().setUrl(cardBO.getBuyerSignUrl())
                .setClientType(new ClientCommon())
                .setTargetType(new TargetTypeWebUrl())))
            .setOperationUserIds(cardBO.getBuyerUserId());
        togetherButtonConList.add(buyerButton);

        ButtonCon sellerButton = new ButtonCon().setLabel(ContractButtonMsg.SELLER_SIGN.getMsg())
            .setButtonUrl(List.of(new ButtonConUrl().setUrl(cardBO.getSellerSignUrl())
                .setClientType(new ClientCommon())
                .setTargetType(new TargetTypeWebUrl())))
            .setOperationUserIds(cardBO.getSellerUserId());
        togetherButtonConList.add(sellerButton);

        SendRichTextMsgReqDTO card = new SendRichTextMsgReqDTO();
        card.setTargetId(cardBO.getRoomId())
            .setFromUserId(cardBO.getCustomerUserId())
            .setTitle(ContractCardTitle.TOGETHER_SIGN.getTitle())
            .setMentionedIds(Arrays.asList(cardBO.getBuyerUserId(), cardBO.getSellerUserId()))
            .setContent(Arrays.asList(new RichTextContent(ContractMessage.TOGETHER_SIGN.getMessage()), new RichTextContent(ContractMessage.NOTICE.getMessage())))
            .setOperation(ContractCardTag.SIGN_TWO_PARTIES_TRADE_CONTRACT.getTag())
            .setOperatorUserIds(Arrays.asList(cardBO.getBuyerUserId(), cardBO.getSellerUserId()))
            .setButtons(togetherButtonConList);

        SingleResponse<String> customerCardResp = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(card), ErrorCode.SEND_BOTH_CONTRACT_SIGN_CARD_FAIL);
        return customerCardResp.getData();
    }

    /**
     * 解析需要打水印的卡片标记
     *
     * @param contractType
     * @return
     */
    private String analysisContractType(Integer contractType) {
        return switch (contractType) {
            case 2 -> ContractCardTag.SIGN_BUYER_TRADE_CONTRACT.getTag();
            case 3 -> ContractCardTag.SIGN_SELLER_TRADE_CONTRACT.getTag();
            case 4 -> ContractCardTag.SIGN_TWO_PARTIES_TRADE_CONTRACT.getTag();
            default -> "";
        };
    }
}
