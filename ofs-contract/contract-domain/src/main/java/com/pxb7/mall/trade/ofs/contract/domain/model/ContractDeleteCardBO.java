package com.pxb7.mall.trade.ofs.contract.domain.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 删除卡片
 *
 * <AUTHOR>
 * @since: 2024-09-18 13:09
 **/
@Data
public class ContractDeleteCardBO implements Serializable {

    @Serial
    private static final long serialVersionUID = -2645720854195197422L;
    /**
     * 房间id
     */
    private String deliveryRoomId;
    /**
     * 客服ID
     */
    private String customerUserId;

    /**
     * 消息ID
     */
    private String msgUID;

    /**
     * 合同类型 1包赔证明 2 买家 3 卖家 4 双方(2、3、4为交易合同)
     */
    private Integer contractType;
}
