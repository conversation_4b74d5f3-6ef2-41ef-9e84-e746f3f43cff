package com.pxb7.mall.trade.ofs.contract.domain.v3.model;

import com.pxb7.mall.common.client.request.risk.riskLog.detail.ReqSaveRiskLogDetailPlusDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 * 合同风控执行结果
 *
 * <AUTHOR>
 * 2025/2/21 下午4:26
 */
@Data
@AllArgsConstructor
public class RiskLogDetailBO {
    /**
     * 命中结果
     */
    private Boolean execRes;
    /**
     * 规则类型
     */
    private String argumentName;
    /**
     * 命中详情数据
     */
    private ReqSaveRiskLogDetailPlusDTO riskLogDetailBO;

    public RiskLogDetailBO(Boolean execRes, ReqSaveRiskLogDetailPlusDTO riskLogDetailBO) {
        this.execRes = execRes;
        this.riskLogDetailBO = riskLogDetailBO;
    }
}
