package com.pxb7.mall.trade.ofs.contract.domain.v3;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.GroupNotifyMsgReqDTO;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.enums.ContractStatusEnum;
import com.pxb7.mall.trade.ofs.common.config.util.DubboResultAssert;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.order.ContractStatusUpdateReqDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.OrderDeliveryInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/07/03 18:37
 **/
@Component
@Slf4j
public class CrtStatusChangeNotifyEvent {
    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    /**
     * 通知IM合同流程节点状态变更
     *
     * @param orderDeliveryInfo 交付信息
     * @param flag              操作标识
     * @param contractStatus    合同状态
     */
    public void notifyContractFlowNode(OrderDeliveryInfoRespDTO orderDeliveryInfo,
                                       boolean flag,
                                       Integer contractStatus) {
        try {
            if (ObjectUtil.isNull(orderDeliveryInfo)) {
                return;
            }
            //通知订单侧当前订单关联的合同状态变更
            ContractStatusUpdateReqDTO contractStatusUpdateReqDTO = new ContractStatusUpdateReqDTO();
            contractStatusUpdateReqDTO.setIsAddContract(flag);
            contractStatusUpdateReqDTO.setOrderItemId(orderDeliveryInfo.getOrderItemId());
            DubboResultAssert.wrapException(() -> orderInfoDubboServiceI.contractStatusUpdate(contractStatusUpdateReqDTO), ErrorCode.NOTIFY_CONTRACT_STATUS_ERROR);

            // 推送融云消息通知合同节点状态变更(新增｜删除)
            notifyContractStatus(orderDeliveryInfo, "CT_STATUS_CHANGE", contractStatus);
        } catch (Exception e) {
            log.error("notify contract flow node error:{}", e.getMessage());
        }
    }

    /**
     * 合同状态变更通知
     *
     * @param orderDeliveryInfo 交付信息
     * @param operation         操作类型
     * @param status            合同状态
     */
    public void notifyContractStatus(OrderDeliveryInfoRespDTO orderDeliveryInfo, String operation, Integer status) {
        if (ObjectUtil.isNotEmpty(orderDeliveryInfo)) {
            // 推送融云消息通知合同节点状态变更(新增｜删除)
            JSONObject content = new JSONObject();
            content.put("option", operation);
            content.put("msg", ContractStatusEnum.getStatusEnumByCode(status));

            GroupNotifyMsgReqDTO cmdMsgReqDTO = new GroupNotifyMsgReqDTO().setGroupId(orderDeliveryInfo.getDeliveryRoomId())
                .setOperation("contract_node_message")
                .setContent(content.toJSONString())
                .setFromUserId(orderDeliveryInfo.getDeliveryCustomerId());
            DubboResultAssert.wrapException(() -> sendMsgServiceI.sendGroupNotify(cmdMsgReqDTO), ErrorCode.RPC_SEND_CMD_MSG_ERROR);
        }
    }
}
