package com.pxb7.mall.trade.ofs.contract.domain.model;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 合同冻结发卡
 *
 * <AUTHOR>
 * @since: 2024-09-03 15:24
 **/
@Data
public class ContractFreezeCardBO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3155347622686517511L;
    /**
     * 群聊ID
     */
    private String deliveryRoomId;
    /**
     * 客服ID
     */
    private String customerUserId;
    /**
     * 卖家ID
     */
    private String sellerUserId;
    /**
     * 买家ID
     */
    private String buyerUserId;
}
