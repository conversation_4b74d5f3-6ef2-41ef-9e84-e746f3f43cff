package com.pxb7.mall.trade.ofs.contract.domain.v3.service;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.pxb7.mall.trade.ofs.contract.domain.model.DeliveryInfoBO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.ImMessageGateway;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantInfoRespDTO;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.enums.TradeTypeEnum;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.AmountUtil;
import com.pxb7.mall.trade.ofs.common.config.util.IDCardValidator;
import com.pxb7.mall.trade.ofs.common.config.util.IdGenUtil;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.contract.client.enums.GuaranteeTypeEnum;
import com.pxb7.mall.trade.ofs.contract.client.enums.SysUserType;
import com.pxb7.mall.trade.ofs.contract.domain.model.CollectMaterialCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractFreezeCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractIndemnityBO;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractCardDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.FddTemplateBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.FddTemplateVariableBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.ManualCreateAgreementBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.message.ExtVoucherCreateMessage;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.AgreementRiskReqBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.AutomatedContractReqBO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.MerchantGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.UserGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractDataRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractFileRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractRouteRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.TradeDictItemRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractRoute;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.ContractCollectUrlRespPO;
import com.pxb7.mall.trade.ofs.delivery.infra.messaging.MQProducer;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryProcessLogDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryResultDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessLog;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryResult;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.OrderDeliveryInfoRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.TradeIdentityRespDTO;
import com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.IndemnityStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.MainOrderSourceEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: CrtAutomationProcessDomainService.java
 * @description: 合同自动化流程服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/2/10 18:02
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class CrtAutomationProcessDomainService {

    private static final String CONTRACT_AUTO_CONTENT = "买卖双方已完成账号交付，进入合同签署阶段。核查：验号与换绑是否完成、账号 UID 是否正确。";

    @Resource
    private MQProducer mqProducer;
    @Resource
    private UserGateway userGateway;
    @Resource
    private MerchantGateway merchantGateway;
    @Resource
    private ImMessageGateway imMessageGateway;
    @Resource
    private MessageDomainService messageDomainService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;
    @Resource
    private ContractDataRepository contractDataRepository;
    @Resource
    private ContractFileRepository contractFileRepository;
    @Resource
    private ContractRouteRepository contractRouteRepository;
    @Resource
    private TradeDictItemRepository tradeDictItemRepository;
    @Resource
    private DeliveryInfoDbRepository deliveryInfoRepository;
    @Resource
    private DataSourceTransactionManager transactionManager;
    @Resource
    private CrtTemplateDomainService crtTemplateDomainService;
    @Resource
    private ContractCardDomainService contractCardDomainService;
    @Resource
    private DeliveryResultDbRepository deliveryResultRepository;
    @Resource
    private CrtSignProcessDomainService crtSignProcessDomainService;
    @Resource
    private CrtRiskControlDomainService crtRiskControlDomainService;
    @Resource
    private CrtDataConverterDomainService crtDataConverterDomainService;
    @Resource
    private DeliveryProcessLogDbRepository deliveryProcessLogDbRepository;


    /**
     * 手动创建合同
     *
     * @param manualCreateAgreementBO
     * @return
     */
    public boolean manualCreateAgreement(ManualCreateAgreementBO manualCreateAgreementBO) {
        List<DeliveryResult> deliveryResults = deliveryResultRepository.lambdaQuery()
            .eq(DeliveryResult::getOrderItemId, manualCreateAgreementBO.getOrderItemId())
            .list();
        if (CollUtil.isNotEmpty(deliveryResults)) {
            if (!(deliveryResults.get(0).getResult() == 1 || deliveryResults.get(0).getResult() == 5)) {
                throw new BusinessException(ErrorCode.DELIVERY_NOT_COMPLETED);
            }
        }

        // 查询合同路由信息
        ContractRoute contractRoute = contractRouteRepository.queryContractRouteByBizChannel(MainOrderSourceEnum.MAIN_ONLINE.getValue());
        if (ObjectUtil.isEmpty(contractRoute)) {
            throw new BusinessException(ErrorCode.CONTRACT_ROUTE_NOT_EXIST);
        }
        //订单交付信息
        OrderDeliveryInfoRespDTO orderDeliveryInfo = orderItemDubboGateway.getOrderDeliveryInfo(manualCreateAgreementBO.getOrderItemId());
        //订单关联的买卖家身份信息
        TradeIdentityRespDTO tradeIdentityData = orderItemDubboGateway.getTradeIdentity(manualCreateAgreementBO.getOrderItemId());

        String agreementId = IdGenUtil.getId();
        Boolean needSignContract = checkSigningNecessaryByManual(manualCreateAgreementBO);
        String signCheckCacheKey = String.format(RedisConstants.SIGN_CONTRACT_CHECK_KEY, manualCreateAgreementBO.getOrderItemId());
        RedissonUtils.setCacheObject(signCheckCacheKey, needSignContract, Duration.ofDays(7));

        ContractData contractData = crtDataConverterDomainService.buildContractData(agreementId, contractRoute, tradeIdentityData, needSignContract, manualCreateAgreementBO);
        //风控拦截
        if (!needSignContract) {
            AgreementRiskReqBO reqBO = crtDataConverterDomainService.buildAgreementRiskReqBO(contractData);
            boolean contractRiskResult = crtRiskControlDomainService.executeRiskControl(reqBO);
            contractData.setFreeze(contractRiskResult);
        }
        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(6);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
            contractDataRepository.save(contractData);
            //拉取合同模板并填充
            List<FddTemplateBO> fddTemplateBOList = crtTemplateDomainService.pullContractTemplate(agreementId, manualCreateAgreementBO.getGameId(), manualCreateAgreementBO.getNormalIndemnityBO()
                .getIndemnityTypeLev2(), contractRoute.getSignChannelId());
            FddTemplateBO unfilledFddIndemnityTpl = fddTemplateBOList.stream()
                .filter(fddTemplateBO -> fddTemplateBO.getTemplateType()
                    .equals(GuaranteeTypeEnum.INDEMNITY_PROVE.getCode()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ErrorCode.INDEMNITY_TEMPLATE_NOT_EXIST));
            FddTemplateBO filledFddIndemnityTpl = crtTemplateDomainService.fillFddTemplateVariable(unfilledFddIndemnityTpl, initFddIndemnityTemplateVariable(contractData, manualCreateAgreementBO, tradeIdentityData));

            ContractFile contractFile = crtDataConverterDomainService.buildBuyerIndemnityFile(contractData, filledFddIndemnityTpl, manualCreateAgreementBO.getCreateUserId());
            contractFileRepository.save(contractFile);
            //更新交付信息
            updateDeliveryInfo(contractData, manualCreateAgreementBO.getGuarantee());
            transactionManager.commit(status);
        } catch (Throwable e) {
            log.error("[创建合同 本地事务失败], orderItemId: {}", manualCreateAgreementBO.getOrderItemId(), e);
            transactionManager.rollback(status);
            throw new BusinessException(ErrorCode.CONTRACT_CREATE_ERROR);
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }
        OrderInfoDubboRespDTO orderInfo = new OrderInfoDubboRespDTO();
        orderInfo.setOrderItemId(manualCreateAgreementBO.getOrderItemId());
        orderInfo.setBuyerId(manualCreateAgreementBO.getBuyerId());
        orderInfo.setSellerId(manualCreateAgreementBO.getSellerId());
        orderInfo.setOrderSource(MainOrderSourceEnum.MAIN_ONLINE.getValue());

        return postHandle(needSignContract, manualCreateAgreementBO.getCreateUserId(), orderInfo, orderDeliveryInfo, contractData.getFreeze());
    }

    /**
     * 自动化合同流程
     *
     * @param orderItemId            订单id
     * @param createUserId           创建人id
     * @param effectiveIndemnityList 有效的包赔列表
     * @return {@link Boolean}
     */
    public boolean automationCreateAgreement(String orderItemId,
                                             String createUserId,
                                             List<OrderItemIndemnityDubboDTO> effectiveIndemnityList,
                                             DeliveryInfoBO deliveryInfo) {
        OrderDeliveryInfoRespDTO orderDeliveryInfo = orderItemDubboGateway.getOrderDeliveryInfo(orderItemId);
        //订单信息
        OrderInfoDubboRespDTO orderInfo = orderItemDubboGateway.getOrderInfo(orderItemId);
        try {
            // 查询合同路由信息
            ContractRoute contractRoute = contractRouteRepository.queryContractRouteByBizChannel(MainOrderSourceEnum.MAIN_ONLINE.getValue());
            if (ObjectUtil.isEmpty(contractRoute)) {
                throw new BusinessException(ErrorCode.CONTRACT_ROUTE_NOT_EXIST);
            }
            preCheck(orderInfo);
            OrderItemIndemnityDubboDTO orderItemNormalIndemnity = effectiveIndemnityList.stream()
                .filter(indemnityDTO -> IndemnityTypeEnum.NORMAL_INDEMNITY.equals(IndemnityTypeEnum.getEnumByCode(indemnityDTO.getIndemnityTypeLev1())))
                .findFirst()
                .orElse(null);
            if (ObjectUtil.isEmpty(orderItemNormalIndemnity)) {
                throw new BusinessException(ErrorCode.CONTRACT_NO_COMPENSATION);
            }

            //订单关联的买卖家身份信息
            TradeIdentityRespDTO tradeIdentityData = orderItemDubboGateway.getTradeIdentity(orderInfo.getOrderItemId());

            String contractId = IdGenUtil.getId();
            Boolean needSignContract = checkSigningNecessary(orderInfo, tradeIdentityData, deliveryInfo);
            log.info("automationCreateAgreement,orderInfo:{},tradeIdentityData:{},deliveryInfo:{},needSignContract:{}",
                JSON.toJSONString(orderInfo), JSON.toJSONString(tradeIdentityData), JSON.toJSONString(deliveryInfo),
                needSignContract);
            ContractData contractData = crtDataConverterDomainService.buildContractData(contractId, orderInfo, effectiveIndemnityList, contractRoute, tradeIdentityData, needSignContract, createUserId,deliveryInfo);
            //风控拦截
            if (!needSignContract) {
                AgreementRiskReqBO reqBO = crtDataConverterDomainService.buildAgreementRiskReqBO(contractData);
                boolean contractRiskResult = crtRiskControlDomainService.executeRiskControl(reqBO);
                contractData.setFreeze(contractRiskResult);
            }
            // 发送合同自动化文本信息
            messageDomainService.sendImText(orderDeliveryInfo.getDeliveryRoomId(),
                orderDeliveryInfo.getDeliveryCustomerId(), CONTRACT_AUTO_CONTENT);

            DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
            defaultTransactionDefinition.setTimeout(6);
            TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
            try {
                contractDataRepository.save(contractData);
                //拉取合同模板并填充
                List<FddTemplateBO> fddTemplateBOList = crtTemplateDomainService.pullContractTemplate(contractId, orderInfo.getGameId(), orderItemNormalIndemnity.getIndemnityTypeLev2(), contractRoute.getSignChannelId());
                FddTemplateBO unfilledFddIndemnityTpl = fddTemplateBOList.stream()
                    .filter(fddTemplateBO -> fddTemplateBO.getTemplateType()
                        .equals(GuaranteeTypeEnum.INDEMNITY_PROVE.getCode()))
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ErrorCode.INDEMNITY_TEMPLATE_NOT_EXIST));
                FddTemplateBO filledFddIndemnityTpl = crtTemplateDomainService.fillFddTemplateVariable(unfilledFddIndemnityTpl, initFddIndemnityTemplateVariable(contractData, orderInfo, tradeIdentityData, effectiveIndemnityList));

                ContractFile contractFile = crtDataConverterDomainService.buildBuyerIndemnityFile(contractData, filledFddIndemnityTpl, createUserId);
                contractFileRepository.save(contractFile);
                //更新交付信息
                updateDeliveryInfo(contractData, !needSignContract);
                transactionManager.commit(status);
            } catch (Throwable e) {
                log.error("[创建合同 本地事务失败], orderItemId: {}", orderItemId, e);
                transactionManager.rollback(status);
                throw new BusinessException(ErrorCode.CONTRACT_CREATE_ERROR);
            } finally {
                if (!status.isCompleted()) {
                    transactionManager.rollback(status);
                }
            }
            return postHandle(needSignContract, createUserId, orderInfo, orderDeliveryInfo, contractData.getFreeze());
        } catch (Throwable e) {
            log.error("[生成买家包赔证明 失败], orderItemId: {}", orderItemId, e);
            exceptionHandle(orderDeliveryInfo, orderInfo, e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 获取订单包赔列表
     *
     * @param orderItemId 订单id
     * @return {@link List<OrderItemIndemnityDubboDTO> 订单包赔列表(包含增值包赔)}
     */
    public List<OrderItemIndemnityDubboDTO> getOrderIndemnityList(String orderItemId) {
        List<OrderItemIndemnityDubboDTO> orderItemIndemnityList = orderItemDubboGateway.getIndemnityList(orderItemId);
        // 订单是否存在有效包赔
        if (CollUtil.isNotEmpty(orderItemIndemnityList)) {
            return orderItemIndemnityList.stream()
                .filter(indemnityDTO -> IndemnityStatusEnum.ACTIVATE.getValue()
                    .equals(indemnityDTO.getIndemnityStatus()) || IndemnityStatusEnum.CANCEL_PENDING.getValue()
                    .equals(indemnityDTO.getIndemnityStatus()))
                .toList();
        }
        return null;
    }

    /**
     * 前置校验
     *
     * @param orderInfo 订单信息
     */
    private void preCheck(OrderInfoDubboRespDTO orderInfo) {
        // 非交易中的订单无法发起合同
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new BusinessException(ErrorCode.ORDER_NOT_EXISTS);
        }
        //交易状态
        Tuple2<String, Integer> tuple = Tuple.of(orderInfo.getOrderItemId(), orderInfo.getOrderItemStatus());
        if (!OrderItemStatusEnum.DEALING.getValue().equals(tuple._2)) {
            throw new BusinessException(ErrorCode.CONTRACT_ORDER_NOT_IN_TRANSACTION);
        }
        // 订单是否有在途资金单据（收款单/退款单/退款单）
        boolean isExistsFundsInTransitReceipt = orderItemDubboGateway.isExistsFundsInTransitReceipt(tuple._1);
        if (isExistsFundsInTransitReceipt) {
            throw new BusinessException(ErrorCode.CONTRACT_EXIST_TRANSIT_FUND_VOUCHER);
        }
    }

    /**
     * 初试化包赔模板变量
     *
     * @param contractData           合同主数据
     * @param orderInfo              订单信息
     * @param tradeIdentityData      订单买卖家身份信息
     * @param effectiveIndemnityList 订单关联的有效包赔列表
     * @return {@link FddTemplateVariableBO}
     */
    private FddTemplateVariableBO initFddIndemnityTemplateVariable(ContractData contractData,
                                                                   OrderInfoDubboRespDTO orderInfo,
                                                                   TradeIdentityRespDTO tradeIdentityData,
                                                                   List<OrderItemIndemnityDubboDTO> effectiveIndemnityList) {
        FddTemplateVariableBO fddTemplateVariableBO = FddTemplateVariableBO.builder()
            .indemnity(effectiveIndemnityList.stream().mapToInt(OrderItemIndemnityDubboDTO::getPaidRatio).sum())
            .gameName(StringUtils.isNotBlank(contractData.getGameName()) ? contractData.getGameName() : orderInfo.getGameName())
            .passesNo(StringUtils.isNotBlank(contractData.getPassesNo()) ? contractData.getPassesNo() : orderInfo.getGameAccount())
            .orderItemId(StringUtils.isNotBlank(contractData.getOrderItemId()) ? contractData.getOrderItemId() : orderInfo.getOrderItemId())
            .discountPrice(AmountUtil.convertYuan(contractData.getDiscountPrice()))
            .tradeTime(DatePattern.NORM_DATE_FORMATTER.format(orderInfo.getCreateTime()))
            .agentName(tradeDictItemRepository.getCustomerName(MainOrderSourceEnum.MAIN_ONLINE.getValue()))
            .build();

        if (ObjectUtil.isNotEmpty(tradeIdentityData)) {
            String sellerIdentNo = "";
            fddTemplateVariableBO.setBuyerPhone(tradeIdentityData.getBuyerPhone());
            SysUserType buyerUserType = SysUserType.fromCode(tradeIdentityData.getBuyerIdentity());
            switch (buyerUserType) {
                case RETAIL -> {
                    UserCertInfoRespDTO userCertInfo = userGateway.getPersonalUserInfo(orderInfo.getBuyerId());
                    if (ObjectUtil.isNotEmpty(userCertInfo)) {
                        fddTemplateVariableBO.setBuyerName(userCertInfo.getCertName());
                        sellerIdentNo = userCertInfo.getCertId();
                    } else {
                        throw new BusinessException("买家未实名");
                    }
                }
                case MERCHANT -> {
                    MerchantInfoRespDTO merchantInfoRespDTO = merchantGateway.getMerchantInfo(orderInfo.getBuyerId(), orderInfo.getProductType());
                    if (merchantInfoRespDTO != null) {
                        fddTemplateVariableBO.setBuyerName(merchantInfoRespDTO.getContactName());
                        sellerIdentNo = merchantInfoRespDTO.getCertNo();
                    }
                }
            }
            if (!IDCardValidator.isAdult(sellerIdentNo)) {
                throw new BusinessException(ErrorCode.TRANSACTIONS_PROHIBITED_UNDERAGE);
            }
        }
        return fddTemplateVariableBO;
    }

    /**
     * 初试化买家包赔模板变量
     *
     * @param contractData            合同数九
     * @param manualCreateAgreementBO 手动创建合同BO
     * @param tradeIdentityData       交易买卖家身份信息
     * @return
     */
    private FddTemplateVariableBO initFddIndemnityTemplateVariable(ContractData contractData,
                                                                   ManualCreateAgreementBO manualCreateAgreementBO,
                                                                   TradeIdentityRespDTO tradeIdentityData) {
        Integer enhanceCompensationRatio = manualCreateAgreementBO.getEnhancedIndemnityBO()
            .stream()
            .map(ContractIndemnityBO::getPercentCompensation)
            .mapToInt(Integer::intValue)
            .sum();
        Integer normalCompensationRatio = manualCreateAgreementBO.getNormalIndemnityBO().getPercentCompensation();
        FddTemplateVariableBO fddTemplateVariableBO = FddTemplateVariableBO.builder()
            .indemnity(enhanceCompensationRatio + normalCompensationRatio)
            .gameName(manualCreateAgreementBO.getGameName())
            .passesNo(manualCreateAgreementBO.getPassesNo())
            .orderItemId(manualCreateAgreementBO.getOrderItemId())
            .discountPrice(AmountUtil.convertYuan(contractData.getDiscountPrice()))
            .tradeTime(DatePattern.NORM_DATE_FORMATTER.format(manualCreateAgreementBO.getTradeTime()))
            .agentName(tradeDictItemRepository.getCustomerName(MainOrderSourceEnum.MAIN_ONLINE.getValue()))
            .build();

        SysUserType buyerUserType = SysUserType.fromCode(manualCreateAgreementBO.getBuyerIdentity());
        String sellerIdentNo = "";
        switch (buyerUserType) {
            case RETAIL -> {
                UserCertInfoRespDTO userCertInfo = userGateway.getPersonalUserInfo(manualCreateAgreementBO.getBuyerId());
                if (ObjectUtil.isNotEmpty(userCertInfo)) {
                    fddTemplateVariableBO.setBuyerName(userCertInfo.getCertName());
                    sellerIdentNo = userCertInfo.getCertId();
                } else {
                    throw new BusinessException("买家未实名");
                }
            }
            case MERCHANT -> {
                MerchantInfoRespDTO merchantInfoRespDTO = merchantGateway.getMerchantInfo(manualCreateAgreementBO.getBuyerId(), manualCreateAgreementBO.getProductType());
                if (merchantInfoRespDTO != null) {
                    fddTemplateVariableBO.setBuyerName(merchantInfoRespDTO.getContactName());
                    sellerIdentNo = merchantInfoRespDTO.getCertNo();
                }
            }
        }
        if (!IDCardValidator.isAdult(sellerIdentNo)) {
            throw new BusinessException(ErrorCode.TRANSACTIONS_PROHIBITED_UNDERAGE);
        }
        if (ObjectUtil.isNotEmpty(tradeIdentityData)) {
            fddTemplateVariableBO.setBuyerPhone(tradeIdentityData.getBuyerPhone());
        }
        return fddTemplateVariableBO;
    }

    /**
     * 构建卖家信息收集卡片参数
     *
     * @param orderInfo         订单信息
     * @param orderDeliveryInfo 订单交付信息
     * @return {@link CollectMaterialCardBO}
     */
    private CollectMaterialCardBO initMaterialCardParam(OrderInfoDubboRespDTO orderInfo,
                                                        OrderDeliveryInfoRespDTO orderDeliveryInfo) {
        if (ObjectUtil.isEmpty(orderDeliveryInfo)) {
            throw new BusinessException(ErrorCode.ORDER_DELIVERY_INFO_NOT_EXIST);
        }
        return CollectMaterialCardBO.builder()
            .buyerUserId(orderInfo.getBuyerId())
            .sellerUserId(orderInfo.getSellerId())
            .customerUserId(orderDeliveryInfo.getDeliveryCustomerId())
            .deliveryRoomId(orderDeliveryInfo.getDeliveryRoomId())
            .orderItemId(orderInfo.getOrderItemId())
            .orderSource(orderInfo.getOrderSource())
            .phone(orderDeliveryInfo.getSellerPhone())
            .sellerMerchantId(orderDeliveryInfo.getSellerMerchantId())
            .buyerMerchantId(orderDeliveryInfo.getBuyerMerchantId())
            .build();
    }

    /**
     * 判断是否需要签署合同
     *
     * @param orderInfo         订单信息
     * @param tradeIdentityData 交易信息买卖家身份信息
     * @return {@link Boolean}
     */
    private Boolean checkSigningNecessary(OrderInfoDubboRespDTO orderInfo, TradeIdentityRespDTO tradeIdentityData,DeliveryInfoBO deliveryInfo) {
        Map<String, Boolean> signContractTagMap = crtSignProcessDomainService.isNeedSignContract(
                    orderInfo.getSellerId(),
                    orderInfo.getProductType(),
                    ObjectUtil.isNotEmpty(tradeIdentityData) ? tradeIdentityData.getSellerIdentity() : SysUserType.RETAIL.getCode(),
                    orderInfo.getOrderItemId(),
                    deliveryInfo);
        return signContractTagMap.getOrDefault(orderInfo.getOrderItemId(), Boolean.TRUE);
    }

    /**
     * IM客服手动发起合同判断卖家是否需要签署合同
     *
     * @param manualCreateAgreementBO 手动创建合同BO
     * @return {@link Boolean}
     */
    private Boolean checkSigningNecessaryByManual(ManualCreateAgreementBO manualCreateAgreementBO) {
        TradeTypeEnum tradeTypeEnum = TradeTypeEnum.getTradeTypeEnumByCode(manualCreateAgreementBO.getTradeContractType());
        return switch (tradeTypeEnum) {
            case BUYER_CONTRACT, BOTH_PARTIES_CONTRACT, SELLER_CONTRACT -> Boolean.TRUE;   //卖家合同需要签署
            case NO_TRANSACTION_CONTRACT -> Boolean.FALSE; //选择无交易合同则不需要签署
        };
    }

    /**
     * 自动化合同流程完成后后置处理
     *
     * @param needSignContract  卖家是否需要签署
     * @param createUserId      创建人
     * @param orderInfo         订单信息
     * @param orderDeliveryInfo 订单交付信息
     * @param freeze            合同冻结状态 true冻结 false未冻结
     * @return {@link Boolean}
     */
    private Boolean postHandle(Boolean needSignContract,
                               String createUserId,
                               OrderInfoDubboRespDTO orderInfo,
                               OrderDeliveryInfoRespDTO orderDeliveryInfo,
                               Boolean freeze) {
        log.info("postHandle,orderInfo:{},orderDeliveryInfo:{},needSignContract:{},createUserId:{},freeze:{}",
            orderInfo, orderDeliveryInfo, needSignContract, createUserId, freeze);
        if (needSignContract) {
            List<ContractCollectUrlRespPO> urlList =
                tradeDictItemRepository.getContractCollectionUrl("sellerCollectUrl");
            contractCardDomainService.sendSellerInfoCollectCard(initMaterialCardParam(orderInfo, orderDeliveryInfo),
                urlList);
        } else {
            if (!freeze) {
                // 无需签署的合同通知创建放款单 冻结不通知 解冻后人工通知放款
                ExtVoucherCreateMessage extVoucherCreateMessage =
                    new ExtVoucherCreateMessage().setOrderItemId(orderInfo.getOrderItemId());
                if (StrUtil.isNotBlank(createUserId)) {
                    extVoucherCreateMessage.setCreateUserId(createUserId);
                }
                String result = mqProducer.send(MqConstants.NOTIFY_EXT_VOUCHER_CREATE_TOPIC,
                    MqConstants.NOTIFY_EXT_VOUCHER_CREATE_TAG, extVoucherCreateMessage);
                imMessageGateway.sendIMMessage(orderDeliveryInfo.getDeliveryCustomerId(), orderDeliveryInfo.getDeliveryRoomId(), Lists.newArrayList(orderDeliveryInfo.getBuyerId(), orderDeliveryInfo.getSellerId()), "合同签署完成", "合同已签署完成,待放款");
                log.warn("send ext_voucher_create_topic message to mq, orderItemId: {}, createUserId:{}, msgId: {}",
                    orderInfo.getOrderItemId(), createUserId, result);
            } else {
                // 发送冻结提示
                ContractFreezeCardBO freezeBO = new ContractFreezeCardBO();
                freezeBO.setBuyerUserId(orderInfo.getBuyerId());
                freezeBO.setCustomerUserId(orderDeliveryInfo.getDeliveryCustomerId());
                freezeBO.setSellerUserId(orderInfo.getSellerId());
                freezeBO.setDeliveryRoomId(orderDeliveryInfo.getDeliveryRoomId());
                contractCardDomainService.sendFreezeCard(freezeBO);
            }
        }
        if (ObjectUtil.isNotEmpty(orderDeliveryInfo)) {
            return contractDomainService.notifyContractFlowNode(orderInfo.getOrderItemId(), orderDeliveryInfo.getDeliveryRoomId(), orderDeliveryInfo.getDeliveryCustomerId(), Boolean.TRUE);
        }
        return Boolean.TRUE;
    }

    /**
     * 自动发起合同异常处理
     *
     * @param orderDeliveryInfo 订单交付信息
     * @param orderInfo         订单信息
     * @param e                 异常信息
     */
    private void exceptionHandle(OrderDeliveryInfoRespDTO orderDeliveryInfo,
                                 OrderInfoDubboRespDTO orderInfo,
                                 Throwable e) {
        AutomatedContractReqBO reqBO = new AutomatedContractReqBO();
        reqBO.setOrderItemId(orderInfo.getOrderItemId());
        if (ObjectUtil.isNotNull(orderDeliveryInfo)) {
            reqBO.setCustomerId(orderDeliveryInfo.getDeliveryCustomerId());
            reqBO.setRoomId(orderDeliveryInfo.getDeliveryRoomId());
        }
        reqBO.setProductUniqueNo(orderInfo.getProductUniqueNo());
        reqBO.setGameName(orderInfo.getGameName());
        reqBO.setPassPermitAccount(orderInfo.getGameAccount());
        reqBO.setBuyerId(orderInfo.getBuyerId());
        messageDomainService.sendImTextInfo(reqBO);
        messageDomainService.sendReminderInfo(reqBO);
        messageDomainService.sendFeiShuMsg(reqBO, e.getMessage());
    }

    private void updateDeliveryInfo(ContractData contractData, Boolean continuedGuarantee) {
        String orderItemId = contractData.getOrderItemId();
        LambdaQueryWrapper<DeliveryProcessLog> wq = new LambdaQueryWrapper<>();
        wq.eq(DeliveryProcessLog::getOrderItemId, orderItemId);
        wq.last("LIMIT 1");
        DeliveryProcessLog processLog = deliveryProcessLogDbRepository.getOne(wq);
        if (processLog != null) {
            return;
        }

        DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(orderItemId);
        if (ObjectUtil.isEmpty(deliveryInfo)) {
            deliveryInfo = new DeliveryInfo();
            deliveryInfo.setOrderItemId(orderItemId);
            deliveryInfo.setAccountSource(contractData.getAccountSource());
            deliveryInfo.setAccountUid(contractData.getPassesNo());
            deliveryInfo.setGameAccount(contractData.getPassesNo());
            deliveryInfo.setRepackage(continuedGuarantee);
            deliveryInfoRepository.save(deliveryInfo);
            return;
        }
        if (contractData.getAccountSource() != null) {
            deliveryInfo.setAccountSource(contractData.getAccountSource());
        }
        if (contractData.getPassesNo() != null) {
            deliveryInfo.setAccountUid(contractData.getPassesNo());
        }
        deliveryInfo.setRepackage(continuedGuarantee);
        deliveryInfoRepository.lambdaUpdate().eq(DeliveryInfo::getOrderItemId, orderItemId).update(deliveryInfo);
    }

}
