package com.pxb7.mall.trade.ofs.contract.domain.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: CollectMaterialCardBO.java
 * @description: 资料收集卡片BO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/11/4 17:15
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Builder
public class CollectMaterialCardBO implements Serializable {
    /**
     * 买家用户ID
     */
    private String buyerUserId;
    /**
     * 卖家用户ID
     */
    private String sellerUserId;
    /**
     * 客服ID
     */
    private String customerUserId;

    /**
     * 交付群聊房间id
     */
    private String deliveryRoomId;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 客户端 0-pc, 1-android, 2-wap,3-ios, 4-wechat小程序,5-alipay小程序, 6-闲鱼, 7-鸿蒙, 8-客服端
     */
    private Integer clientType;
    /**
     * 订单来源1-主站立即购买 2-主站IM 3-闲鱼 4-支付宝小程序
     */
    private Integer orderSource;
    /**
     * 账号来源  0自己注册 1螃蟹平台 2其他平台
     */
    private Integer accountSource;
    /**
     * 资料手机方手机号
     */
    private String phone;
    /**
     * 买家的商家id
     */
    private String buyerMerchantId;
    /**
     * 卖家的商家id
     */
    private String sellerMerchantId;

}
