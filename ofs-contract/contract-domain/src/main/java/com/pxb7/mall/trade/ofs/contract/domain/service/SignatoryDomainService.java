package com.pxb7.mall.trade.ofs.contract.domain.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.IdGenUtil;
import com.pxb7.mall.trade.ofs.contract.domain.mapping.SignatoryDomainMapping;
import com.pxb7.mall.trade.ofs.contract.domain.model.SignatoryBO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.AddRegisterAccountReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.PersonVerifyUrlReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.VerifyReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.response.fdd.PersonRespPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FddGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractRouteRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractSignChannelRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractSignatoryUserRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractVerifiedRecordRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractRoute;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignChannel;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignatoryUser;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractVerifiedRecord;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: SignatoryDomainService.java
 * @description: 签约人service
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/8/30 11:18
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class SignatoryDomainService {

    @Resource
    private ContractSignatoryUserRepository contractSignatoryUserRepository;

    @Resource
    private ContractSignChannelRepository contractSignChannelRepository;

    @Resource
    private ContractRouteRepository contractRouteRepository;

    @Resource
    private ContractVerifiedRecordRepository contractVerifiedRecordRepository;

    @Resource
    private FddGateway fddGateway;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 查询最新一条签约人记录
     *
     * @param latestSignatoryId 签约人id
     * @return SignatoryBO
     */
    public SignatoryBO queryLatestSignatoryRecord(String latestSignatoryId) {
        ContractSignatoryUser contractSignatoryUser = contractSignatoryUserRepository.queryLatestSignatoryRecord(latestSignatoryId);
        return SignatoryDomainMapping.INSTANCE.transformSignatoryUserEntity(contractSignatoryUser);
    }

    /**
     * 四要素查询签约人信息
     *
     * @param signChannelId 电子签渠道id
     * @param userName      姓名
     * @param certNo        身份证号
     * @param phone         手机号
     * @return Boolean
     */
    public SignatoryBO querySignatoryByFourFactors(String signChannelId, String userName, String certNo, String phone) {
        ContractSignatoryUser contractSignatoryUser = contractSignatoryUserRepository.querySignatoryByFourFactors(signChannelId, userName, certNo, phone);
        return SignatoryDomainMapping.INSTANCE.transformSignatoryUserEntity(contractSignatoryUser);

    }

    public Boolean saveSignatory(SignatoryBO signatoryBO) {
        ContractSignatoryUser contractSignatoryUser = SignatoryDomainMapping.INSTANCE.transformSignatoryBO(signatoryBO);
        return contractSignatoryUserRepository.saveSignatory(contractSignatoryUser);
    }

    public Boolean updateSignatory(SignatoryBO signatoryBO) {
        ContractSignatoryUser contractSignatoryUser = SignatoryDomainMapping.INSTANCE.transformSignatoryBO(signatoryBO);
        return contractSignatoryUserRepository.updateSignatory(contractSignatoryUser);
    }

    /**
     * 添加签约人信息，同时注册法大大账号
     *
     * @param userId            用户id
     * @param busChannel        业务渠道1-主站 2-支付宝
     * @param signatoryBO       历史签约人信息
     * @param latestSignatoryBO 本次最新的签约人信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String handleFddAuthInfo(String userId,
                                    Integer busChannel,
                                    SignatoryBO signatoryBO,
                                    SignatoryBO latestSignatoryBO) {
        //查询合同路由信息
        ContractRoute contractRoute = contractRouteRepository.queryContractRouteByBizChannel(busChannel);
        if (ObjectUtil.isEmpty(contractRoute)) {
            throw new BusinessException(ErrorCode.CONTRACT_ROUTE_NOT_EXIST);
        }
        //通过路由查询电子签渠道信息
        ContractSignChannel contractSignChannel = contractSignChannelRepository.querySignChannelConfig(contractRoute.getSignChannelId());
        if (ObjectUtil.isEmpty(contractSignChannel)) {
            throw new BusinessException(ErrorCode.SIGN_CHANNEL_NOT_EXIST);
        }
        var providerId = contractSignChannel.getSignChannelId();
        var appId = contractSignChannel.getAppId();
        var appSecret = contractSignChannel.getAppSecret();

        if (ObjectUtil.isEmpty(signatoryBO)) {
            latestSignatoryBO.setCustomerId(registerFddAccount(latestSignatoryBO.getSignatoryUserId(), providerId, appId, appSecret));
            latestSignatoryBO.setUserId(userId);
            latestSignatoryBO.setSignChannelId(contractRoute.getSignChannelId());
            //获取个人实名认证地址
            SingleResponse<JSONObject> jsonObjResp = getPersonVerifyInfo(appId, appSecret, latestSignatoryBO);
            String transactionNo = jsonObjResp.getData().getJSONObject("data").getString("transactionNo");
            String authUrl = Base64.decodeStr(jsonObjResp.getData().getJSONObject("data").getString("url"));

            ContractVerifiedRecord contractVerifiedRecord = buildContractVerifiedRecord(latestSignatoryBO, transactionNo, authUrl);
            latestSignatoryBO.setTransactionNo(transactionNo);
            saveSignatory(latestSignatoryBO);
            contractVerifiedRecordRepository.save(contractVerifiedRecord);
            return authUrl;
        } else {
            log.warn("===========handleFddAuthInfo start,signatory already exists, userId:{}, userName:{},signatoryUserId:{}", userId, signatoryBO.getUserName(), signatoryBO.getSignatoryUserId());
            String authUrl = null;
            //后台复制链接到C端让用户填写的，需要更新transactionNo,生成authUrl
            if (StrUtil.isBlank(signatoryBO.getTransactionNo())) {
                SingleResponse<JSONObject> jsonObjResp = getPersonVerifyInfo(appId, appSecret, signatoryBO);
                String transactionNo = jsonObjResp.getData().getJSONObject("data").getString("transactionNo");
                signatoryBO.setTransactionNo(transactionNo);
                updateSignatory(signatoryBO);
                authUrl = Base64.decodeStr(jsonObjResp.getData().getJSONObject("data").getString("url"));
            }
            //查询历史认证记录
            String signatoryUserId = signatoryBO.getSignatoryUserId();
            String customerId = signatoryBO.getCustomerId();
            ContractVerifiedRecord hisVerifiedRecord = contractVerifiedRecordRepository.queryVerifiedRecord(signatoryUserId, customerId);
            if (ObjUtil.isNotEmpty(hisVerifiedRecord)) {
                authUrl = hisVerifiedRecord.getAuthUrl();
                if (hisVerifiedRecord.getVerifiedStatus() == 2) { //认证通过
                    return null;
                } else {
                    //查询该签约人是否在电子签渠道认证完成
                    VerifyReqPO verifyReqPO = new VerifyReqPO();
                    verifyReqPO.setAppId(appId);
                    verifyReqPO.setAppSecret(appSecret);
                    verifyReqPO.setCustomerId(signatoryBO.getCustomerId());
                    verifyReqPO.setVerifiedSerialNo(signatoryBO.getTransactionNo());
                    JSONObject jsonObject = fddGateway.findPersonCert(verifyReqPO).getData();
                    String code = jsonObject.getString("code");
                    if (!StrUtil.equals(code, "1")) {
                        throw new BizException(ErrorCode.FDD_QUERY_PERSON_AUTH_FAIL.getErrCode(), String.format(ErrorCode.FDD_QUERY_PERSON_AUTH_FAIL.getErrDesc(), jsonObject.getString("msg")));
                    }
                    PersonRespPO personPO = jsonObject.getJSONObject("data").getObject("person", PersonRespPO.class);
                    String transactionNo = jsonObject.getJSONObject("data").getString("transactionNo");
                    transactionNo = StrUtil.isNotEmpty(transactionNo) ? transactionNo : signatoryBO.getTransactionNo();
                    ContractVerifiedRecord contractVerifiedRecord = buildContractVerifiedRecord(signatoryBO, transactionNo, authUrl);
                    if (ObjUtil.isNotEmpty(personPO)) {
                        contractVerifiedRecord.setVerifiedPhone(personPO.getMobile());
                        contractVerifiedRecord.setBankCardNo(personPO.getBankCardNo());
                        contractVerifiedRecord.setVerifiedWay(StrUtil.isNotBlank(personPO.getVerifyType())
                            ? Integer.valueOf(personPO.getVerifyType())
                            : 1);
                        if (StrUtil.isNotBlank(personPO.getAuditorTime())) {
                            contractVerifiedRecord.setAuditTime(LocalDateTime.parse(personPO.getAuditorTime(), formatter));
                        }
                        //认证不成功，返回上一次的认证链接，否则让前端跳转页面
                        authUrl = !StrUtil.equals(personPO.getStatus(), "2") ? authUrl : null;
                    }
                    contractVerifiedRecord.setVerifiedRecordId(hisVerifiedRecord.getVerifiedRecordId());
                    contractVerifiedRecord.setVerifiedStatus((ObjUtil.isNotEmpty(personPO)
                        ? Integer.valueOf(personPO.getStatus())
                        : hisVerifiedRecord.getVerifiedStatus()));
                    contractVerifiedRecordRepository.lambdaUpdate()
                        .eq(ContractVerifiedRecord::getSignatoryUserId, signatoryUserId)
                        .eq(ContractVerifiedRecord::getCustomerId, customerId)
                        .eq(ContractVerifiedRecord::getTransactionNo, transactionNo)
                        .update(contractVerifiedRecord);
                    return authUrl;
                }
            } else {
                //历史数据，没有认证记录，根据transactionNo来判断
                if (StrUtil.isNotBlank(signatoryBO.getTransactionNo())) {
                    //已经认证过，补一条认证记录数据，默认认证通过
                    ContractVerifiedRecord contractVerifiedRecord = buildContractVerifiedRecord(signatoryBO, signatoryBO.getTransactionNo(), "");
                    contractVerifiedRecord.setAuditTime(LocalDateTime.now());
                    contractVerifiedRecord.setVerifiedStatus(2);
                    contractVerifiedRecordRepository.save(contractVerifiedRecord);
                    return authUrl;
                } else {
                    //没有认证过
                    SingleResponse<JSONObject> jsonObjResp = getPersonVerifyInfo(appId, appSecret, signatoryBO);
                    String transactionNo = jsonObjResp.getData().getJSONObject("data").getString("transactionNo");
                    authUrl = Base64.decodeStr(jsonObjResp.getData().getJSONObject("data").getString("url"));

                    ContractVerifiedRecord contractVerifiedRecord = buildContractVerifiedRecord(latestSignatoryBO, transactionNo, authUrl);
                    contractVerifiedRecordRepository.save(contractVerifiedRecord);

                    signatoryBO.setTransactionNo(transactionNo);
                    updateSignatory(signatoryBO);
                }
                return authUrl;
            }
        }
    }

    /**
     * 注册法大大账号并返回customerId
     *
     * @param userId
     * @param providerId
     * @param appId
     * @param appSecret
     * @return
     */
    public String registerFddAccount(String userId, String providerId, String appId, String appSecret) {
        //去法大大注册账号(本期只支持个人)
        AddRegisterAccountReqPO addRegisterAccountReqPO = new AddRegisterAccountReqPO();
        addRegisterAccountReqPO.setAccountType(String.valueOf(1));
        addRegisterAccountReqPO.setUserId(userId);
        addRegisterAccountReqPO.setProviderId(providerId);
        addRegisterAccountReqPO.setAppId(appId);
        addRegisterAccountReqPO.setAppSecret(appSecret);

        return fddGateway.accountRegister(addRegisterAccountReqPO).getData();
    }

    /**
     * 获取个人实名信息
     *
     * @param appId
     * @param appSecret
     * @param latestSignatoryBO
     * @return
     */
    public SingleResponse<JSONObject> getPersonVerifyInfo(String appId,
                                                          String appSecret,
                                                          SignatoryBO latestSignatoryBO) {
        PersonVerifyUrlReqPO verifyUrlReqPO = new PersonVerifyUrlReqPO();
        verifyUrlReqPO.setAppId(appId);
        verifyUrlReqPO.setAppSecret(appSecret);
        verifyUrlReqPO.setUserId(latestSignatoryBO.getSignatoryUserId());
        verifyUrlReqPO.setCustomerId(latestSignatoryBO.getCustomerId());
        verifyUrlReqPO.setVerifiedWay("1");
        verifyUrlReqPO.setPageModify("2");
        verifyUrlReqPO.setCustomerIdentType("0");
        verifyUrlReqPO.setCustomerName(latestSignatoryBO.getUserName());
        verifyUrlReqPO.setMobile(latestSignatoryBO.getPhone());
        verifyUrlReqPO.setCustomerIdentNo(latestSignatoryBO.getCertNo());
        verifyUrlReqPO.setIdPhotoOptional("2");
        verifyUrlReqPO.setCertFlag("1");
        return fddGateway.getPersonVerifyUrl(verifyUrlReqPO);
    }

    /**
     * 构建签约人认证记录
     *
     * @param signatoryBO
     * @param transactionNo
     * @param authUrl
     * @return
     */

    public ContractVerifiedRecord buildContractVerifiedRecord(SignatoryBO signatoryBO,
                                                              String transactionNo,
                                                              String authUrl) {
        ContractVerifiedRecord contractVerifiedRecord = new ContractVerifiedRecord();
        contractVerifiedRecord.setVerifiedRecordId(IdGenUtil.getId());
        contractVerifiedRecord.setSignatoryUserId(signatoryBO.getSignatoryUserId());
        contractVerifiedRecord.setTransactionNo(transactionNo);
        contractVerifiedRecord.setCustomerId(signatoryBO.getCustomerId());
        contractVerifiedRecord.setVerifiedPhone(signatoryBO.getPhone());
        contractVerifiedRecord.setAuthUrl(authUrl);
        contractVerifiedRecord.setVerifiedWay(99);
        return contractVerifiedRecord;
    }

    /**
     * 获取实名认证记录
     *
     * @param signatoryBO 签约人业务模型
     * @return
     */
    private List<ContractVerifiedRecord> getVerifiedRecordList(SignatoryBO signatoryBO) {
        return contractVerifiedRecordRepository.getVerifiedRecordListBySignatoryId(signatoryBO.getSignatoryUserId());
    }
}
