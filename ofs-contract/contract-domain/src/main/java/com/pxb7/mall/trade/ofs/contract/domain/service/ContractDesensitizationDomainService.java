package com.pxb7.mall.trade.ofs.contract.domain.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.common.client.api.OcrServiceI;
import com.pxb7.mall.common.client.api.OssServiceI;
import com.pxb7.mall.common.client.response.censor.NeteaseIMageOcrReqDTO;
import com.pxb7.mall.common.client.response.censor.NeteaseImageOcrRespDTO;
import com.pxb7.mall.trade.ofs.common.config.util.DownloadPdfUtil;
import com.pxb7.mall.trade.ofs.common.config.util.PdfToImgUtil;
import com.pxb7.mall.trade.ofs.common.config.util.WatermarkUtil;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractDesensitizationBO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.DownloadContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FddGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractFileRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

/**
 * 合同脱敏
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@Slf4j
public class ContractDesensitizationDomainService {
    @Resource
    private FddGateway fddGateway;
    @Resource
    private ContractFileRepository contractFileRepository;
    @DubboReference
    private OcrServiceI ocrServiceI;
    @DubboReference
    private OssServiceI ossServiceI;

    public boolean dealContractDocumentViewUrl(ContractDesensitizationBO dataBO) {
        DownloadContractReqPO downloadContractReqPO = new DownloadContractReqPO();
        downloadContractReqPO.setContractId(dataBO.getContractNo());
        downloadContractReqPO.setAppId(dataBO.getAppId());
        downloadContractReqPO.setAppSecret(dataBO.getAppSecret());
        SingleResponse<JSONObject> downloadContractResp = fddGateway.downloadContract(downloadContractReqPO);
        if (downloadContractResp.isSuccess()) {
            try {
                String localUrl = DownloadPdfUtil.down(downloadContractResp.getData().getString("url"));
                String viewContractUrl = contractDesensitization(localUrl, dataBO.getContractFileId());
                if (StrUtil.isNotBlank(viewContractUrl)) {
                    contractFileRepository.updateViewUrlById(dataBO.getContractFileId(), viewContractUrl);
                }
                // 删除本地文件
                DownloadPdfUtil.delLocalFile(localUrl);
            } catch (Exception e) {
                log.error("合同脱敏异常contract_file.id-{}", dataBO.getContractFileId(), e);
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 合同脱敏
     *
     * @param pdfPath        合同下载地址
     * @param contractFileId 文件ID
     * @return 脱敏后上传法大大返回的地址
     */
    public String contractDesensitization(String pdfPath, String contractFileId) throws IOException {
        // pdf 转图片
        List<BufferedImage> images = PdfToImgUtil.convertPDFToImages(pdfPath);
        if (!images.isEmpty()) {
            for (int pageNo = 0; pageNo < images.size(); pageNo++) {
                if (pageNo == 0 || pageNo == 2) {  // 加水印 合同敏感信息只存在于第一页和第三页，只针对第一张、第三张脱敏处理
                    BufferedImage firstImage = images.get(pageNo);
                    // 调易盾接口
                    NeteaseIMageOcrReqDTO ocrReqDTO = new NeteaseIMageOcrReqDTO();
                    List<String> keys = pageNo == 0
                        ? Arrays.asList("身份证号码:", "身份H证号码:", "住所地", "通讯地址", "联系电话", "电子邮箱")
                        : Arrays.asList("指定收款账号");
                    ocrReqDTO.setKeys(keys);
                    ocrReqDTO.setImageBase64(PdfToImgUtil.encodeImageToBase64(firstImage));
                    SingleResponse<NeteaseImageOcrRespDTO> ocrRes = ocrServiceI.imageOrc(ocrReqDTO);
                    if (ocrRes.isSuccess() && ObjectUtil.isNotEmpty(ocrRes.getData())) {
                        // 加水印
                        BufferedImage newFile = WatermarkUtil.addWatermarkToPDF(firstImage, ocrRes.getData(), contractFileId, pageNo);
                        images.set(pageNo, newFile);

                    } else {
                        log.error("合同脱敏易盾调用失败，合同ID:{}, ocrRes:{}", contractFileId, ocrRes);
                    }
                }
            }
            // 拼接图片
            BufferedImage stitchedImage = PdfToImgUtil.stitchImagesVertically(images);
            // 转成字节流
            byte[] imgByte = PdfToImgUtil.convertBufferedImageToByteArray(stitchedImage);
            // 上传到OSS
            SingleResponse<String> ossRes = ossServiceI.uploadFile(imgByte, "contract/uplod-contract/" + contractFileId + "-" + Instant.now()
                .getEpochSecond() + ".jpg");
            // 输出OSS文件地址
            return ossRes.getData();
        }
        return "";
    }
}
