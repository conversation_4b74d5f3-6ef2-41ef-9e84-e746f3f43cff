package com.pxb7.mall.trade.ofs.contract.domain.service;

import java.time.LocalDateTime;
import java.util.*;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.GroupNotifyMsgReqDTO;
import com.pxb7.mall.merchant.client.api.MerchantInfoServiceI;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantInfoRespDTO;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.enums.GuaranteeTypeEnum;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.DubboResultAssert;
import com.pxb7.mall.trade.ofs.common.config.util.IdGenUtil;
import com.pxb7.mall.trade.ofs.contract.client.dto.enumns.ContractTypeEnum;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.CheckContract;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.InfoSubmitStatusReqDTO;
import com.pxb7.mall.trade.ofs.contract.domain.ContractSignBO;
import com.pxb7.mall.trade.ofs.contract.domain.mapping.ContractDataDomainMapping;
import com.pxb7.mall.trade.ofs.contract.domain.model.*;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractType;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.SysUserType;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.MerchantGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.ExtSignReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.PersonVerifySignReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FddGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.*;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.*;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.ContractDeliverySearchReq;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.BuyerIndemnityContractRespPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request.ContractDataReqPO;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.order.ContractStatusUpdateReqDTO;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.TradeIdentityReqDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.TradeIdentityRespDTO;
import com.pxb7.mall.user.api.UserServiceI;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractDomainService.java
 * @description: 合同domain Service
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/2 14:19
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class ContractDomainService {
    @Resource
    private SignatoryDomainService signatoryDomainService;
    @Resource
    private TradeDictItemRepository tradeDictItemRepository;
    @Resource
    private ContractDataDbRepository contractDataRepository;
    @Resource
    private ContractFileRepository contractFileRepository;
    @Resource
    private ContractSignInfoRepository contractSignInfoRepository;
    @Resource
    private ContractRouteRepository contractRouteRepository;
    @Resource
    private ContractBuyerInfoDbRepository contractBuyerInfoRepository;
    @Resource
    private ContractSellerInfoDbRepository contractSellerInfoRepository;
    @Resource
    private ContractSignetRepository contractSignetRepository;
    @Resource
    private ContractSignChannelRepository contractSignChannelRepository;
    @Resource
    private DeliveryInfoDbRepository deliveryInfoDbRepository;
    @DubboReference(providedBy = "order")
    private OrderInfoDubboServiceI orderInfoDubboServiceI;
    @DubboReference(providedBy = "user-c")
    private UserServiceI userServiceI;
    @DubboReference(providedBy = "merchant-admin")
    private MerchantInfoServiceI merchantInfoServiceI;
    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @Resource
    private FddGateway fddGateway;
    @Resource
    private MerchantGateway merchantGateway;

    @Value("${fdd.customerId}")
    private String customerId;

    public ContractRoute queryContractRouteByBizChannel(Integer bizChannel) {
        return contractRouteRepository.queryContractRouteByBizChannel(bizChannel);
    }

    /**
     * 获取
     *
     * @param orderItemId
     * @param buyerId
     * @return
     */
    public ContractBuyerInfo getContractBuyerInfo(String orderItemId, String buyerId) {
        return contractBuyerInfoRepository.getBuyerInfo(orderItemId, buyerId);
    }

    /**
     * 生成合同主信息
     *
     * @param contractMainInfoBO 合同主信息
     * @param busChannel         业务渠道1主站2支付宝
     */
    public void addContractMainInfo(ContractMainInfoBO contractMainInfoBO, Integer busChannel) {
        // 查询合同路由信息
        ContractRoute contractRoute = contractRouteRepository.queryContractRouteByBizChannel(busChannel);
        contractMainInfoBO.setRouteId(contractRoute.getRouteId());
        contractMainInfoBO.setSignChannelId(contractRoute.getSignChannelId());
        contractMainInfoBO.setStartTime(LocalDateTime.now());
        ContractData contractData = ContractDataDomainMapping.INSTANCE.transformContractMainInfoEntity(contractMainInfoBO);
        contractDataRepository.save(contractData);
    }

    /**
     * 生成合同文件
     *
     * @param fillingCompletedTplBO 填充完成的模板业务模型
     * @param orderItemId           订单id
     */
    public ContractFileBO generateContractFile(ContractTemplateBO fillingCompletedTplBO,
                                               String orderItemId,
                                               String contractId) {
        ContractFileBO contractFileBO = new ContractFileBO();
        contractFileBO.setContractDataId(contractId);
        contractFileBO.setContractFileId(fillingCompletedTplBO.getContractFillId());
        contractFileBO.setArchive(Boolean.FALSE);
        contractFileBO.setFileStatus(fillingCompletedTplBO.getContractType() == 1 ? 1 : 0);
        contractFileBO.setExternalName(fillingCompletedTplBO.getExternalName());
        contractFileBO.setInternalName(fillingCompletedTplBO.getInternalName());
        contractFileBO.setContractType(fillingCompletedTplBO.getContractType());
        contractFileBO.setDocumentViewUrl(fillingCompletedTplBO.getViewPdfUrl());
        contractFileBO.setDocumentDownloadUrl(fillingCompletedTplBO.getDownloadUrl());
        contractFileBO.setDocumentUploadTime(LocalDateTime.now());
        contractFileBO.setOrderItemId(orderItemId);

        ContractFile contractFile = ContractDataDomainMapping.INSTANCE.transformContractFileEntity(contractFileBO);
        contractFile.setCreateUserId(ImUserUtil.getUserId());
        contractFile.setUpdateUserId(ImUserUtil.getUserId());
        contractFileRepository.save(contractFile);
        return contractFileBO;

    }

    /**
     * 保存合同签署信息
     *
     * @param contractSignInfoBO 合同签署信息业务模型
     * @param contractId
     * @param msgUid
     */
    public void addContractFileSignInfo(ContractSignInfoBO contractSignInfoBO, String contractId, String msgUid) {
        ContractSignInfo contractSignInfo = ContractDataDomainMapping.INSTANCE.transformContractFileSignInfo(contractSignInfoBO);
        contractSignInfo.setContractDataId(contractId);
        contractSignInfo.setMsgUid(msgUid);
        contractSignInfo.setCreateUserId(ImUserUtil.getUserId());
        contractSignInfo.setUpdateUserId(ImUserUtil.getUserId());
        contractSignInfoRepository.save(contractSignInfo);
    }

    /**
     * 买卖家提交资料通知订单侧
     *
     * @param infoSubmitStatusReqDTO 资料提交状态请求的DTO
     * @return
     */
    public InfoSubmitStatusBO notifyInfoSubmitStatus(InfoSubmitStatusReqDTO infoSubmitStatusReqDTO) {
        String orderItemId = infoSubmitStatusReqDTO.getOrderItemId();
        String buyerId = infoSubmitStatusReqDTO.getBuyerId();
        Integer buyerIdentity = infoSubmitStatusReqDTO.getBuyerIdentity();
        String sellerId = infoSubmitStatusReqDTO.getSellerId();
        Integer sellerIdentity = infoSubmitStatusReqDTO.getSellerIdentity();

        InfoSubmitStatusBO infoSubmitStatusBO = new InfoSubmitStatusBO();
        SysUserType buyerUserType = SysUserType.fromCode(buyerIdentity);
        //查询买家资料信息
        Boolean buyerFlag = contractBuyerInfoRepository.checkBuyerInfo(orderItemId, buyerId, null);
        switch (buyerUserType) {
            case RETAIL -> infoSubmitStatusBO.setBuyerCommitStatus(buyerFlag);
            case MERCHANT -> {
                Integer contractType = merchantGateway.getMerchantContractType(buyerId, infoSubmitStatusReqDTO.getGameId());
                GuaranteeTypeEnum guaranteeTypeEnum = GuaranteeTypeEnum.getGuaranteeNameByCode(contractType);
                if (guaranteeTypeEnum.equals(GuaranteeTypeEnum.UNKNOWN_TYPE)) {
                    infoSubmitStatusBO.setBuyerCommitStatus(buyerFlag);
                } else {
                    switch (guaranteeTypeEnum) {
                        case INDEMNITY_PROVE -> infoSubmitStatusBO.setBuyerCommitStatus(Boolean.TRUE);
                        case TRADE_CONTRACT -> infoSubmitStatusBO.setBuyerCommitStatus(buyerFlag);
                    }
                }
            }
        }

        // 查询卖家资料信息(卖家需要判断续包)
        SysUserType sellerUserType = SysUserType.fromCode(sellerIdentity);
        Boolean sellerFlag = contractSellerInfoRepository.checkSellerInfo(orderItemId, sellerId, null);
        if (sellerFlag) {  //提交过资料
            infoSubmitStatusBO.setSellerCommitStatus(Boolean.TRUE);
        } else {
            DeliveryInfo deliveryInfo = deliveryInfoDbRepository.getDeliveryInfo(orderItemId); //续包
            if (ObjectUtil.isNotEmpty(deliveryInfo)) {
                infoSubmitStatusBO.setSellerCommitStatus(deliveryInfo.isRepackage());
            } else {
                switch (sellerUserType) {
                    case RETAIL -> infoSubmitStatusBO.setSellerCommitStatus(Boolean.FALSE);
                    case MERCHANT -> infoSubmitStatusBO.setSellerCommitStatus(infoSubmitStatusReqDTO.getSellerEffect());
                }
            }
        }

        return infoSubmitStatusBO;
    }

    /**
     * 通知IM合同流程节点状态变更
     *
     * @param orderItemId        订单id
     * @param deliveryRoomId     交付房间id
     * @param deliveryCustomerId 交付客服id
     * @param addContractFlag    操作标识
     * @return {@link Boolean}
     */
    public Boolean notifyContractFlowNode(String orderItemId,
                                          String deliveryRoomId,
                                          String deliveryCustomerId,
                                          boolean addContractFlag) {
        //通知订单侧当前订单关联的合同状态变更
        ContractStatusUpdateReqDTO contractStatusUpdateReqDTO = new ContractStatusUpdateReqDTO();
        contractStatusUpdateReqDTO.setIsAddContract(addContractFlag);
        contractStatusUpdateReqDTO.setOrderItemId(orderItemId);
        SingleResponse<Boolean> updateResp = DubboResultAssert.wrapException(() -> orderInfoDubboServiceI.contractStatusUpdate(contractStatusUpdateReqDTO), ErrorCode.NOTIFY_CONTRACT_STATUS_ERROR);

        // 推送融云消息通知合同节点状态变更(新增｜删除)
        JSONObject content = new JSONObject();
        content.put("option", "addContract");
        content.put("msg", addContractFlag);
        GroupNotifyMsgReqDTO cmdMsgReqDTO = new GroupNotifyMsgReqDTO().setGroupId(deliveryRoomId)
            .setOperation("contract_node_message")
            .setContent(content.toJSONString())
            .setFromUserId(deliveryCustomerId);
        SingleResponse<Boolean> notifyResp = DubboResultAssert.wrapException(() -> sendMsgServiceI.sendGroupNotify(cmdMsgReqDTO), ErrorCode.RPC_SEND_CMD_MSG_ERROR);
        return updateResp.getData() && notifyResp.getData();
    }

    public List<ContractData> searchContract(SearchContractParamBO searchParamBO) {
        ContractDeliverySearchReq contractDeliverySearchReq = ContractDataDomainMapping.INSTANCE.transSearchContractParam(searchParamBO);
        return contractDataRepository.queryContractDeliveryData(contractDeliverySearchReq);
    }

    public Boolean checkContractExist(CheckContract checkContract) {
        return contractDataRepository.checkContractExist(checkContract);
    }

    /**
     * 查询配置期限内某个用户的有效合同
     *
     * @param startDate 起始日期
     * @param sellerId  用户ID
     * @return 匹配的签约人信息
     */
    public HashMap<String, List> queryWithInPeriodUserContract(LocalDateTime startDate, String sellerId) {
        HashMap<String, List> dataMap = new HashMap<>();
        List<ContractData> contractDataList = contractDataRepository.queryWithInPeriodUserContract(startDate, sellerId);
        if (CollUtil.isNotEmpty(contractDataList)) {
            List<String> signUserIds = new ArrayList<>();
            List<ContractData> contractData = new ArrayList<>();
            List<String> contractDataIds = contractDataList.stream().map(ContractData::getContractDataId).toList();
            List<ContractSignInfo> contractSignInfoList = contractSignInfoRepository.queryContractSignInfos(sellerId, contractDataIds);
            if (CollUtil.isNotEmpty(contractSignInfoList)) {
                contractSignInfoList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getSignatoryUserId()))
                    .forEach(data -> {
                        if (!signUserIds.contains(data.getSignatoryUserId())) {
                            signUserIds.add(data.getSignatoryUserId());
                        }
                        contractDataList.stream()
                            .filter(item -> Objects.equals(item.getContractDataId(), data.getContractDataId()))
                            .forEach(contractData::add);
                    });
            }
            dataMap.put("signUserIds", new ArrayList<>(signUserIds));
            dataMap.put("contractData", contractData);
        }
        return dataMap;
    }

    /**
     * 合同表中，当前合同数据中卖家的签约人，在X的日期区间内，该签约人涉及到的有效合同所对应的注册用户数量大于Y
     *
     * @param startDate       限定日期
     * @param signatoryUserId 签约人ID
     * @return 匹配的注册用户数量
     */
    public Map<String, List> queryContractUserCount(LocalDateTime startDate, String signatoryUserId) {
        Map<String, List> resultMap = new HashMap<>();
        HashSet<String> userIds = new HashSet<>();
        List<ContractSignInfo> contractSignInfoList = contractSignInfoRepository.queryContractSignInfo(signatoryUserId);
        if (CollUtil.isEmpty(contractSignInfoList)) {
            return resultMap;
        }
        List<String> contractDataIds = contractSignInfoList.stream().map(ContractSignInfo::getContractDataId).toList();
        List<ContractData> contractDataList = contractDataRepository.querySignatoryContractWithInPeriod(contractDataIds, startDate);
        resultMap.put("contractData", contractDataList);
        List<String> contractIds = contractDataList.stream().map(ContractData::getContractDataId).toList();
        contractSignInfoList.stream()
            .filter(item -> contractIds.contains(item.getContractDataId()))
            .forEach(data -> userIds.add(data.getUserId()));
        resultMap.put("userIds", new ArrayList<>(userIds));
        return resultMap;
    }

    /**
     * 一号多卖验证
     *
     * @param gameId           游戏ID
     * @param makerGameIds     腾讯系所有游戏ID
     * @param passesNo         游戏账号
     * @param sellerUserId     卖家ID
     * @param sellerMerchantId 卖家号商ID
     * @return 匹配的合同数量
     */
    public List<ContractData> queryContractByUserIdAndGameIds(String gameId,
                                                              List<String> makerGameIds,
                                                              String passesNo,
                                                              String sellerUserId,
                                                              String sellerMerchantId) {
        return contractDataRepository.queryContractByUserIdAndGameIds(gameId, makerGameIds, passesNo, sellerUserId, sellerMerchantId);

    }

    /**
     * 账号转手交易
     *
     * @param gameId           游戏ID
     * @param passesNo         游戏账号
     * @param sellerUserId     卖家ID
     * @param sellerMerchantId 卖家号商ID
     * @return 匹配的合同数量
     */
    public List<ContractData> queryContractUserAndGameId(String gameId,
                                                         String passesNo,
                                                         String sellerUserId,
                                                         String sellerMerchantId) {
        return contractDataRepository.queryContractUserAndGameId(gameId, passesNo, sellerUserId, sellerMerchantId);
    }

    /**
     * 获取当前用户的合同签署列表
     *
     * @param userIds         用户id
     * @param isCurrentUser   是否是当前用户 status=2(签署中)时，不为空
     * @param createStartTime
     * @param createEndTime
     * @return
     */
    public List<ContractSignInfo> getOwnerContractSignList(Set<String> userIds,
                                                           Boolean isCurrentUser,
                                                           LocalDateTime createStartTime,
                                                           LocalDateTime createEndTime) {
        return contractSignInfoRepository.getOwnerContractSignList(userIds, isCurrentUser, createStartTime, createEndTime);
    }

    /**
     * 查询当前用户选择包赔时的合同列表信息
     *
     * @param contractDataReqPO
     * @return
     */
    public List<BuyerIndemnityContractRespPO> getOwnerIndemnityContractList(ContractDataReqPO contractDataReqPO) {
        return contractDataRepository.getOwnerIndemnityContractList(contractDataReqPO);
    }

    public ContractUserInfoBO contractUserInfo(String orderItemId, String gameId) {
        TradeIdentityReqDTO reqDTO = new TradeIdentityReqDTO();
        reqDTO.setOrderItemId(orderItemId);
        SingleResponse<TradeIdentityRespDTO> tradeIdentity = orderInfoDubboServiceI.getTradeIdentity(reqDTO);
        if (ObjectUtils.isNotEmpty(tradeIdentity)) {
            TradeIdentityRespDTO data = tradeIdentity.getData();
            String buyerId = data.getBuyerId();
            // 查询买家信息
            ContractUserInfoBO userInfo = new ContractUserInfoBO().setBuyerId(buyerId)
                .setBuyerPhone(data.getBuyerPhone())
                .setSellerId(data.getSellerId())
                .setSellerPhone(data.getSellerPhone());
            SysUserType buyerUserType = SysUserType.fromCode(data.getBuyerIdentity());
            Boolean buyerFlag = contractBuyerInfoRepository.checkBuyerInfo(orderItemId, buyerId, null);
            switch (buyerUserType) {
                case RETAIL -> {
                    MultiResponse<UserCertInfoRespDTO> userCertInfoList = userServiceI.getUserCertInfoList(Collections.singletonList(buyerId));
                    if (ObjectUtils.isNotEmpty(userCertInfoList.getData())) {
                        UserCertInfoRespDTO data1 = userCertInfoList.getData().get(0);
                        userInfo.setBuyerUserName(data1.getCertName());
                    }
                    userInfo.setBuyerCommitStatus(buyerFlag);
                }
                case MERCHANT -> {
                    SingleResponse<MerchantInfoRespDTO> buyerMerchantInfo = DubboResultAssert.wrapException(() -> merchantInfoServiceI.getMerchantBusinessInfoByUserId(buyerId, data.getProductType()), ErrorCode.RPC_GET_MERCHANT_INFO_ERROR);
                    MerchantInfoRespDTO merchantInfoRespDTO = buyerMerchantInfo.getData();
                    if (ObjectUtils.isNotEmpty(merchantInfoRespDTO)) {
                        userInfo.setBuyerUserName(merchantInfoRespDTO.getMerchantName());
                        userInfo.setBuyerEffect(merchantInfoRespDTO.isBusinessFlag());
                        Integer contractType = merchantGateway.getMerchantContractType(buyerId, gameId);
                        GuaranteeTypeEnum guaranteeTypeEnum = GuaranteeTypeEnum.getGuaranteeNameByCode(contractType);
                        if (guaranteeTypeEnum.equals(GuaranteeTypeEnum.UNKNOWN_TYPE)) {
                            userInfo.setBuyerCommitStatus(buyerFlag);
                        } else {
                            switch (guaranteeTypeEnum) {
                                case INDEMNITY_PROVE -> userInfo.setBuyerCommitStatus(Boolean.TRUE);
                                case TRADE_CONTRACT -> userInfo.setBuyerCommitStatus(buyerFlag);
                            }
                        }
                    }
                }
            }
            // 查询卖家信息
            DeliveryInfo deliveryInfo = deliveryInfoDbRepository.getDeliveryInfo(orderItemId);
            SysUserType sellerUserType = SysUserType.fromCode(data.getSellerIdentity());
            Boolean sellerFlag = contractSellerInfoRepository.checkSellerInfo(orderItemId, data.getSellerId(), null);
            switch (sellerUserType) {
                case RETAIL -> {
                    MultiResponse<UserCertInfoRespDTO> userCertInfoList = userServiceI.getUserCertInfoList(Collections.singletonList(data.getSellerId()));
                    if (ObjectUtils.isNotEmpty(userCertInfoList.getData())) {
                        UserCertInfoRespDTO data1 = userCertInfoList.getData().get(0);
                        userInfo.setSellerUserName(data1.getCertName());
                    }
                    Boolean sellerCommitStatus = sellerFlag
                        ? Boolean.TRUE
                        : (ObjectUtil.isNotEmpty(deliveryInfo) ? deliveryInfo.isRepackage() : Boolean.FALSE);
                    userInfo.setSellerCommitStatus(sellerCommitStatus);
                }
                case MERCHANT -> {
                    SingleResponse<MerchantInfoRespDTO> sellerMerchantInfo = DubboResultAssert.wrapException(() -> merchantInfoServiceI.getMerchantBusinessInfoByUserId(data.getSellerId(), data.getProductType()), ErrorCode.RPC_GET_MERCHANT_INFO_ERROR);
                    MerchantInfoRespDTO merchantInfoRespDTO = sellerMerchantInfo.getData();
                    if (ObjectUtils.isNotEmpty(merchantInfoRespDTO)) {
                        userInfo.setSellerUserName(merchantInfoRespDTO.getMerchantName());
                        userInfo.setSellerEffect(merchantInfoRespDTO.isBusinessFlag());
                        Boolean sellerCommitStatus = sellerFlag
                            ? Boolean.TRUE
                            : (ObjectUtil.isNotEmpty(deliveryInfo)
                                ? deliveryInfo.isRepackage()
                                : merchantInfoRespDTO.isBusinessFlag());
                        userInfo.setSellerCommitStatus(sellerCommitStatus);
                    }
                }
            }
            return userInfo;
        }
        return null;
    }

    /**
     * 发送签署卡片&保存文件需要签署的信息
     *
     * @param contractSignCardBO
     * @param contractInitiateReqBO 发起合同请求的模型
     * @param contractTemplateBO    合同模版业务模型
     * @param contractFileBO        合同文件业务模型
     * @param contractSignBO        合同签署业务模型
     */
    public List<ContractSignInfoBO> buildContractSignInfoList(ContractSignCardBO contractSignCardBO,
                                                              ContractInitiateReqBO contractInitiateReqBO,
                                                              ContractTemplateBO contractTemplateBO,
                                                              ContractFileBO contractFileBO,
                                                              ContractSignBO contractSignBO) {
        ContractTypeEnum contractTypeEnum = ContractTypeEnum.getContractTypeEnumByCode(contractFileBO.getContractType());
        var contractFileId = contractFileBO.getContractFileId();
        var buyerSignatoryId = contractSignBO.getBuyerSignatoryId();
        var sellerSignatoryId = contractSignBO.getSellerSignatoryId();

        List<ContractSignInfoBO> contractSignInfoBOList = Lists.newArrayList();
        // 合同文件需要那方签署
        if (contractTemplateBO.getNeedSign()) {
            switch (contractTypeEnum) {
                case INDEMNITY_PROVE -> { // 平台
                }
                case BUYER_CONTRACT -> { // 合同签署人为买家+平台
                    String buyerSignUrl = generateSignUrl(buyerSignatoryId, contractSignBO.getBuyerName(), contractInitiateReqBO.getOrderItemId(), contractTemplateBO);
                    contractSignCardBO.setBuyerSignUrl(buyerSignUrl);
                    contractSignCardBO.setContractType(ContractType.BUYER_CONTRACT);
                    String transactionId = contractTemplateBO.getContractSignInfoId();

                    ContractSignInfoBO contractBuyerSignInfoBO = assembleContractSignInfoBO(1, contractInitiateReqBO, buyerSignatoryId);
                    contractBuyerSignInfoBO.setContractSignId(transactionId);
                    contractBuyerSignInfoBO.setContractFileId(contractFileId);
                    contractBuyerSignInfoBO.setSignUrl(buyerSignUrl);
                    contractBuyerSignInfoBO.setTransactionId(transactionId);
                    contractBuyerSignInfoBO.setPeerSignStatus(Boolean.TRUE);
                    contractSignInfoBOList.add(contractBuyerSignInfoBO);
                    // 平台或者代签都视为自动签
                    extSignAuto(contractInitiateReqBO.getBusChannel(), contractTemplateBO);
                }
                case SELLER_CONTRACT -> { // 合同签署人为买家代签（博淳）+卖家+平台
                    String sellerSignUrl = generateSignUrl(sellerSignatoryId, contractSignBO.getSellerName(), contractInitiateReqBO.getOrderItemId(), contractTemplateBO);
                    contractSignCardBO.setSellerSignUrl(sellerSignUrl);
                    contractSignCardBO.setContractType(ContractType.CLAIM_SELLER);
                    String transactionId = contractTemplateBO.getContractSignInfoId();

                    ContractSignInfoBO contractSellerSignInfoBO = assembleContractSignInfoBO(2, contractInitiateReqBO, sellerSignatoryId);
                    contractSellerSignInfoBO.setContractSignId(transactionId);
                    contractSellerSignInfoBO.setContractFileId(contractFileId);
                    contractSellerSignInfoBO.setSignUrl(sellerSignUrl);
                    contractSellerSignInfoBO.setTransactionId(transactionId);
                    contractSellerSignInfoBO.setPeerSignStatus(Boolean.TRUE);
                    contractSignInfoBOList.add(contractSellerSignInfoBO);
                    // 平台或者代签都视为自动签
                    extSignAuto(contractInitiateReqBO.getBusChannel(), contractTemplateBO);
                }
                case BOTH_PARTIES_CONTRACT -> { // 合同签署人为买家+卖家+平台
                    String buyerSignUrl = generateSignUrl(buyerSignatoryId, contractSignBO.getBuyerName(), contractInitiateReqBO.getOrderItemId(), contractTemplateBO);
                    contractSignCardBO.setBuyerSignUrl(buyerSignUrl);
                    contractSignCardBO.setContractType(ContractType.TOGETHER);
                    String buyerTransactionId = contractTemplateBO.getContractSignInfoId();

                    ContractSignInfoBO contractBuyerSignInfoBO = assembleContractSignInfoBO(1, contractInitiateReqBO, buyerSignatoryId);
                    contractBuyerSignInfoBO.setContractSignId(buyerTransactionId);
                    contractBuyerSignInfoBO.setContractFileId(contractFileId);
                    contractBuyerSignInfoBO.setTransactionId(buyerTransactionId);
                    contractBuyerSignInfoBO.setSignUrl(buyerSignUrl);
                    contractBuyerSignInfoBO.setPeerSignStatus(Boolean.FALSE);
                    contractSignInfoBOList.add(contractBuyerSignInfoBO);

                    String sellerSignUrl = generateSignUrl(sellerSignatoryId, contractSignBO.getSellerName(), contractInitiateReqBO.getOrderItemId(), contractTemplateBO);
                    contractSignCardBO.setSellerSignUrl(sellerSignUrl);
                    String sellerTransactionId = contractTemplateBO.getContractSignInfoId();

                    ContractSignInfoBO contractSellerSignInfoBO = assembleContractSignInfoBO(2, contractInitiateReqBO, sellerSignatoryId);
                    contractSellerSignInfoBO.setContractSignId(sellerTransactionId);
                    contractSellerSignInfoBO.setContractFileId(contractFileId);
                    contractSellerSignInfoBO.setSignUrl(sellerSignUrl);
                    contractSellerSignInfoBO.setTransactionId(sellerTransactionId);
                    contractBuyerSignInfoBO.setPeerSignStatus(Boolean.FALSE);
                    contractSignInfoBOList.add(contractSellerSignInfoBO);
                    // 平台或者代签都视为自动签
                    extSignAuto(contractInitiateReqBO.getBusChannel(), contractTemplateBO);
                }
            }
        }
        return contractSignInfoBOList;
    }

    /**
     * 生成合同签署链接
     *
     * @param contractFileId     合同文件id
     * @param contractSignInfoId 合同签署信息id
     * @param signatoryId        签约人id
     * @return
     */
    public String updateSignUrl(String contractFileId, String contractSignInfoId, String signatoryId) {
        log.warn("updateSignUrl start----contractFileId:{},contractSignInfoId:{},signatoryId:{}", contractFileId, contractSignInfoId, signatoryId);
        PersonVerifySignReqPO verifySignReqPO = new PersonVerifySignReqPO();
        SignatoryBO signatoryBO = signatoryDomainService.queryLatestSignatoryRecord(signatoryId);
        if (ObjectUtil.isEmpty(signatoryBO)) {
            return null;
        }
        ContractSignChannel signChannel = contractSignChannelRepository.querySignChannelConfig(signatoryBO.getSignChannelId());
        verifySignReqPO.setAppId(signChannel.getAppId());
        verifySignReqPO.setAppSecret(signChannel.getAppSecret());

        verifySignReqPO.setContractId(contractFileId);
        verifySignReqPO.setTransactionId(contractSignInfoId);
        verifySignReqPO.setCustomerId(signatoryBO.getCustomerId());
        // 允许用户修改认证页面信息 1允许 2不允许
        verifySignReqPO.setPageModify("2");
        // 实名认证套餐类型 0：三要素标准方案 1：三要素补充方案
        verifySignReqPO.setVerifiedWay("1");
        verifySignReqPO.setIdPhotoOptional("2");
        verifySignReqPO.setSignKeyword(signatoryBO.getUserName());
        verifySignReqPO.setCertType("0");
        verifySignReqPO.setCustomerName(signatoryBO.getUserName());
        verifySignReqPO.setCustomerIdentNo(signatoryBO.getCertNo());
        verifySignReqPO.setMobile(signatoryBO.getPhone());
        return fddGateway.personVerifySign(verifySignReqPO);
    }

    /**
     * 生成签约的url(快捷签)
     *
     * @param signatoryUserId    签约人id
     * @param userName           用户名称
     * @param contractTemplateBO 合同模板业务模型
     * @return String
     */
    private String generateSignUrl(String signatoryUserId,
                                   String userName,
                                   String orderItemId,
                                   ContractTemplateBO contractTemplateBO) {
        log.info("===============generateSignUrl: request param: orderItemId:{},signatoryUserId:{},signChannelId:{},contractTemplateBO:{}", orderItemId, signatoryUserId, contractTemplateBO.getSignChannelId(), contractTemplateBO);
        PersonVerifySignReqPO verSignReqPO = new PersonVerifySignReqPO();
        verSignReqPO.setAppId(contractTemplateBO.getAppId());
        verSignReqPO.setAppSecret(contractTemplateBO.getAppSecret());
        verSignReqPO.setContractId(contractTemplateBO.getContractFillId());
        String contractSignInfoId = IdGenUtil.getId();
        SignatoryBO signatoryBO = signatoryDomainService.queryLatestSignatoryRecord(signatoryUserId);

        verSignReqPO.setCustomerId(signatoryBO.getCustomerId());
        verSignReqPO.setTransactionId(contractSignInfoId);
        // 允许用户修改认证页面信息 1允许 2不允许
        verSignReqPO.setPageModify("2");
        // 实名认证套餐类型 0：三要素标准方案 1：三要素补充方案
        verSignReqPO.setVerifiedWay("1");
        verSignReqPO.setIdPhotoOptional("2");
        verSignReqPO.setSignKeyword(userName);
        verSignReqPO.setCertType("0");
        verSignReqPO.setCustomerName(signatoryBO.getUserName());
        verSignReqPO.setCustomerIdentNo(signatoryBO.getCertNo());
        verSignReqPO.setMobile(signatoryBO.getPhone());
        contractTemplateBO.setContractSignInfoId(contractSignInfoId);
        return fddGateway.personVerifySign(verSignReqPO);
    }

    /**
     * 自动签署
     *
     * @param sourceType         业务渠道 1：主站 2：支付宝
     * @param contractTemplateBO 合同模板业务模型
     */
    private void extSignAuto(Integer sourceType, ContractTemplateBO contractTemplateBO) {
        String agentName = tradeDictItemRepository.getCustomerName(sourceType);
        ExtSignReqPO extSignReqPO = new ExtSignReqPO();
        String contractSignInfoId = IdGenUtil.getId();
        extSignReqPO.setAppId(contractTemplateBO.getAppId());
        extSignReqPO.setAppSecret(contractTemplateBO.getAppSecret());
        extSignReqPO.setTransactionId(contractSignInfoId);
        extSignReqPO.setContractId(contractTemplateBO.getContractFillId());
        extSignReqPO.setCustomerId(customerId);
        extSignReqPO.setDocTitle(contractTemplateBO.getExternalName());
        extSignReqPO.setSignKeyword(agentName);
        extSignReqPO.setSignatureId(getSignetNo(contractTemplateBO.getSignChannelId(), sourceType));

        JSONObject jsonObject = fddGateway.extSignAuto(extSignReqPO).getData();
        if (ObjectUtil.isEmpty(jsonObject)) {
            return;
        }
        String code = jsonObject.getString("code");
        if (!StrUtil.equals(code, "1000")) {
            String msg = jsonObject.getString("msg");
            throw new BizException(ErrorCode.FDD_AUTO_EXT_SIGN_FAIL.getErrCode(), String.format(ErrorCode.FDD_AUTO_EXT_SIGN_FAIL.getErrDesc(), contractTemplateBO.getContractFillId(), msg));
        }
        contractTemplateBO.setContractSignInfoId(contractSignInfoId);
    }

    /**
     * 获取印章编号
     *
     * @param signChannelId 电子签渠道id
     * @param sourceType    业务渠道1主站2支付宝
     * @return 印章编号
     */
    private String getSignetNo(String signChannelId, Integer sourceType) {
        String signetId = contractRouteRepository.getSignetIdByChannelIdAndSourceType(signChannelId, sourceType);
        if (StrUtil.isBlank(signetId)) {
            throw new BusinessException(ErrorCode.CONTRACT_SIGNET_NOT_CONFIGURED);
        }
        return contractSignetRepository.getOptById(signetId)
            .orElseThrow(() -> new BusinessException(ErrorCode.CONTRACT_SIGNET_NOT_CONFIGURED))
            .getSignetNo();
    }

    /**
     * 组装合同签署信息
     *
     * @param identityType          用户身份标识 1:买家 2：卖家
     * @param contractInitiateReqBO 发起合同请求模型
     * @param signatoryUserId       签约用户id
     */
    private ContractSignInfoBO assembleContractSignInfoBO(int identityType,
                                                          ContractInitiateReqBO contractInitiateReqBO,
                                                          String signatoryUserId) {
        ContractSignInfoBO contractSignInfoBO = new ContractSignInfoBO();
        contractSignInfoBO.setSignedStatus(0);
        contractSignInfoBO.setOrderItemId(contractInitiateReqBO.getOrderItemId());
        switch (identityType) {
            case 1 -> {
                var buyerId = contractInitiateReqBO.getBuyerId();
                contractSignInfoBO.setSignatoryType(1);
                SignatoryBO buyerSignatoryBO = signatoryDomainService.queryLatestSignatoryRecord(signatoryUserId);
                contractSignInfoBO.setCustomerId(buyerSignatoryBO.getCustomerId());
                contractSignInfoBO.setSignatoryUserId(signatoryUserId);
                contractSignInfoBO.setUserId(buyerId);
            }
            case 2 -> {
                var sellerId = contractInitiateReqBO.getSellerId();
                SignatoryBO sellerSignatoryBO = signatoryDomainService.queryLatestSignatoryRecord(signatoryUserId);
                contractSignInfoBO.setCustomerId(sellerSignatoryBO.getCustomerId());
                contractSignInfoBO.setUserId(sellerId);
                contractSignInfoBO.setSignatoryType(2);
                contractSignInfoBO.setSignatoryUserId(signatoryUserId);
            }
        }
        return contractSignInfoBO;
    }

    public ContractData getValidContract(String orderItemId) {
        return contractDataRepository.getValidContract(orderItemId);
    }

    public ContractSellerInfo getContractSellerInfo(String orderItemId, String sellerId) {
        return contractSellerInfoRepository.getSellerInfo(orderItemId, sellerId);
    }

    public List<ContractData> queryContractByGameIdAndGameAccount(String gameId,
                                                                  List<String> makerGameIds,
                                                                  String passesNo) {
        return contractDataRepository.queryContractByGameIdAndGameAccount(gameId, makerGameIds, passesNo);
    }
    
    public ContractData findOneByContractDataIdAndOrderItemId(String contractDataId, String orderItemId) {
        return contractDataRepository.findOneByContractDataIdAndOrderItemId(contractDataId, orderItemId);
    }

    public Boolean deleteContract(String contractDataId, String orderItemId, String userId) {
        return contractDataRepository.delByContractDataIdAndOrderItemId(contractDataId, orderItemId, userId);
    }
}
