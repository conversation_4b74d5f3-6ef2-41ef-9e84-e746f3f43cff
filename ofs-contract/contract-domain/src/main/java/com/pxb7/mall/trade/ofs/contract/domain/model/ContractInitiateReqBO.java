package com.pxb7.mall.trade.ofs.contract.domain.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractInitiateReqDTO.java
 * @description: 合同发起请求的DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/8/28 14:40
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ContractInitiateReqBO implements Serializable {

    /**
     * 买家用户类型 1:个人 2商家
     */
    private Integer buyerType;

    /**
     * 卖家用户类型 1:个人 2商家
     */
    private Integer sellerType;

    /**
     * 游戏账号｜通行证
     */
    private String passesNo;

    /**
     * 买家id
     */
    private String buyerId;

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 买家的商家id
     */
    private String buyerMerchantId;
    /**
     * 卖家的商家id
     */
    private String sellerMerchantId;
    /**
     * 账号来源  0自己注册 1螃蟹平台 2其他平台
     */
    private Integer accountSource;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 商品编号
     */
    private String productUniqueNo;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 优惠前的号价
     */
    private Long preDiscountPrice;

    /**
     * 优惠后的号价
     */
    private Long discountPrice;
    /**
     * 订单关联的包赔信息
     */
    private List<ContractIndemnityBO> contractIndemnityInfos;

    /**
     * 买家保障类型 1 包赔证明 2 交易合同
     */
    private Integer buyerGuaranteeType;

    /**
     * 交易合同类型 20无交易合同 21 买家合同 22 卖家合同 23 双方合同
     */
    private Integer tradeContractType;

    /**
     * 业务渠道1主站2支付宝
     */
    private Integer busChannel;

    /**
     * 交付群聊房间ID
     */
    private String roomId;

    /**
     * 客服用户ID
     */
    private String customerUserId;
    /**
     * 交易时间
     */
    private LocalDateTime tradeTime;

    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
     */
    private Integer productType;
    /**
     * 客户端类型
     */
    private Integer clientType;

    /**
     * 是否冻结 0 否 1 是（需风控埋点）
     */
    private Boolean freeze;
    /**
     * 是否续包
     */
    private Boolean guarantee;
}
