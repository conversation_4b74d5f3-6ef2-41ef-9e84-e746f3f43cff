package com.pxb7.mall.trade.ofs.contract.domain.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: SignatoryBO.java
 * @description: 签约人业务模型
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/8/29 17:09
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class SignatoryBO implements Serializable {

    /**
     * 螃蟹平台签约人id
     */
    private String signatoryUserId;

    /**
     * 电子签渠道ID
     */
    private String signChannelId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 用户类型 1个人 2 商家
     */
    private Integer userType;
    /**
     * 证件类型 1:身份证 2港澳通行证 3台湾通行证 4护照
     */
    private Integer certType;
    /**
     * 证件号码(本期只支持身份证)
     */
    private String certNo;
    /**
     * 证件正面照片
     */
    private String certFrontImg;
    /**
     * 证件反面照片
     */

    private String certBackImg;
    /**
     * 法大大的客户编号
     */

    private String customerId;
    /**
     * 法大大实名认证交易号(认证成功之后的交易号)
     */
    private String transactionNo;

}
