package com.pxb7.mall.trade.ofs.contract.domain.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractSignChannelBO.java
 * @description: 电子签渠道业务模型
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/2 19:19
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ContractSignChannelBO implements Serializable {
    /**
     * 电子签服务商
     */
    private String providerName;
    /**
     * 签约主体
     */
    private String subjectName;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 应用密钥
     */
    private String appSecret;
}
