<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pxb7.mall.trade</groupId>
        <artifactId>ofs</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>contract-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>contract-infrastructure</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>ofs-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>sentryext-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>sentinel-component-starter</artifactId>
        </dependency>
    </dependencies>
</project>
