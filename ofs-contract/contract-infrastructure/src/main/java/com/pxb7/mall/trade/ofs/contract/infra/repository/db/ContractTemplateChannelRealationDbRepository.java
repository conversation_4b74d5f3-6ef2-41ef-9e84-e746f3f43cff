package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractTemplateChannelRealation;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractTemplateChannelRealationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合同模板和电子签渠道关联表(ContractTemplateChannelRealation)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-03 17:24:40
 */
@Slf4j
@Repository
public class ContractTemplateChannelRealationDbRepository extends ServiceImpl<ContractTemplateChannelRealationMapper, ContractTemplateChannelRealation> implements ContractTemplateChannelRealationRepository {

    @Override
    public List<String> getTplIdByChannelId(String signChannelId) {
        return lambdaQuery().eq(ContractTemplateChannelRealation::getSignChannelId, signChannelId)
            .eq(ContractTemplateChannelRealation::getDeleted, false)
            .list()
            .stream()
            .map(ContractTemplateChannelRealation::getTemplateId)
            .toList();
    }

}
