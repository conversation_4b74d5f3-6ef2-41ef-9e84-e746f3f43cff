package com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-06-11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RegisterAccountParams extends AbstractApiParams {

    /**
     * 用户唯一标识 必填
     */
    @JSONField(name = "open_id")
    private String openId;

    /**
     * 1 个人 2 企业 必填
     */
    @JSONField(name = "account_type")
    private String accountType;

    @Override
    public String joinContentStr() {
        return this.accountType + this.openId;
    }
}
