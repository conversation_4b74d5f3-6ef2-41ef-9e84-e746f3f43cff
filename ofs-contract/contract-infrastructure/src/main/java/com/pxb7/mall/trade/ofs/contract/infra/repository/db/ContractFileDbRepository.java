package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractTypeEnum;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractFileMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 合同文件(ContractFile)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:08
 */
@Slf4j
@Repository
public class ContractFileDbRepository extends ServiceImpl<ContractFileMapper, ContractFile> implements ContractFileRepository {

    @Override
    public List<ContractFile> getContractFilesByContractId(String contractId, Integer contractType) {
        LambdaQueryWrapper<ContractFile> query = new LambdaQueryWrapper<>();
        query.eq(ContractFile::getContractDataId, contractId)
            .eq(contractType != null, ContractFile::getContractType, contractType)
            .eq(ContractFile::getDeleted, false);
        return baseMapper.selectList(query);
    }

    @Override
    public List<ContractFile> getContractFilesByContractIds(List<String> contractIds) {
        LambdaQueryWrapper<ContractFile> query = new LambdaQueryWrapper<>();
        query.in(ContractFile::getContractDataId, contractIds).eq(ContractFile::getDeleted, false);
        return baseMapper.selectList(query);
    }

    @Override
    public List<ContractFile> getSellerTradeFilesByContractId(String contractId, List<Integer> sellerTradeTypeList) {
        LambdaQueryWrapper<ContractFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractFile::getContractDataId, contractId)
            .in(ContractFile::getContractType, sellerTradeTypeList);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public boolean updateViewUrlById(String contractFileId, String viewUrl) {
        return lambdaUpdate().eq(ContractFile::getContractFileId, contractFileId)
            .set(ContractFile::getDocumentViewUrl, viewUrl)
            .update();
    }

    @Override
    public boolean updateVendorAgreementFile(ContractFile vendorContractFile) {
        return lambdaUpdate().eq(ContractFile::getContractDataId, vendorContractFile.getContractDataId())
            .eq(ContractFile::getOrderItemId, vendorContractFile.getOrderItemId())
            .eq(ContractFile::getContractType, ContractTypeEnum.SELLER_CONTRACT.getType())
            .set(ContractFile::getContractFileId, vendorContractFile.getContractFileId())
            .set(ContractFile::getExternalName, vendorContractFile.getExternalName())
            .set(ContractFile::getInternalName, vendorContractFile.getInternalName())
            .set(ContractFile::getDocumentDownloadUrl, vendorContractFile.getDocumentDownloadUrl())
            .set(ContractFile::getDocumentViewUrl, vendorContractFile.getDocumentViewUrl())
            .set(ContractFile::getDocumentUploadTime, vendorContractFile.getDocumentUploadTime())
            .set(ContractFile::getUpdateUserId, vendorContractFile.getUpdateUserId())
            .set(ContractFile::getFileStatus, vendorContractFile.getFileStatus())
            .set(ContractFile::getArchive, vendorContractFile.getArchive())
            .update();
    }

    @Override
    public void batchDeleteContractFile(List<String> fileIdList) {
        LambdaUpdateWrapper<ContractFile> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ContractFile::getContractFileId, fileIdList).set(ContractFile::getDeleted, true);
        this.update(updateWrapper);
    }

    @Override
    public List<ContractFile> queryFileListByContractDataId(String contractDataId) {
        LambdaQueryWrapper<ContractFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractFile::getContractDataId, contractDataId).eq(ContractFile::getDeleted, false);
        return baseMapper.selectList(queryWrapper);
    }
}
