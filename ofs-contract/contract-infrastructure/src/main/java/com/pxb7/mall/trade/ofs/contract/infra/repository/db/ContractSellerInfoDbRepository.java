package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractBuyerInfo;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSellerInfo;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractSellerInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * 卖家资料快照信息(ContractSellerInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:08
 */
@Slf4j
@Repository
public class ContractSellerInfoDbRepository extends ServiceImpl<ContractSellerInfoMapper, ContractSellerInfo>
    implements ContractSellerInfoRepository {
    @Override
    public boolean saveSellerInformation(ContractSellerInfo entity) {
        return save(entity);
    }

    @Override
    public ContractSellerInfo queryLatestSignatoryWithOrder(String orderItemId, String userId, String userName) {
        LambdaQueryWrapper<ContractSellerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractSellerInfo::getOrderItemId, orderItemId).eq(ContractSellerInfo::getUserId, userId);
        if (StrUtil.isNotEmpty(userName)) {
            wrapper.eq(ContractSellerInfo::getUserName, userName);
        }
        wrapper.orderByDesc(ContractSellerInfo::getCreateTime);
        wrapper.last("limit 1");
        return this.getOneOpt(wrapper).orElse(null);
    }

    @Override
    public ContractSellerInfo queryLatestSignatoryWithUser(String userId, String userName) {
        return this.lambdaQuery().eq(ContractSellerInfo::getUserId, userId)
            .eq(StrUtil.isNotBlank(userName), ContractSellerInfo::getUserName, userName)
            .orderByDesc(ContractSellerInfo::getCreateTime).last("limit 1").one();

    }

    @Override
    public Boolean checkSellerInfo(String orderItemId, String sellerId, Integer rand) {
        LambdaQueryWrapper<ContractSellerInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractSellerInfo::getOrderItemId, orderItemId).eq(ContractSellerInfo::getUserId, sellerId);
        queryWrapper.eq(ObjUtil.isNotEmpty(rand), ContractSellerInfo::getRand, rand);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public ContractSellerInfo querySellerInfo(String orderItemId, String sellerId) {
        return this.lambdaQuery().eq(ContractSellerInfo::getUserId, sellerId)
            .eq(ContractSellerInfo::getOrderItemId, orderItemId).orderByDesc(ContractSellerInfo::getCreateTime)
            .last("limit 1").one();
    }

    /**
     * 获取卖家信息
     * @param orderItemId
     * @param sellerId
     * @return
     */
    public ContractSellerInfo getSellerInfo(String orderItemId, String sellerId) {
        LambdaQueryWrapper<ContractSellerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractSellerInfo::getOrderItemId, orderItemId).eq(ContractSellerInfo::getUserId, sellerId);
        return this.getOne(wrapper);

    }

    @Override
    public void delByOrderItemId(String orderItemId) {
        if (StringUtils.isBlank(orderItemId)) {
            return;
        }
        this.lambdaUpdate().eq(ContractSellerInfo::getOrderItemId, orderItemId).set(ContractSellerInfo::getDeleted, true)
            .update();
    }
}
