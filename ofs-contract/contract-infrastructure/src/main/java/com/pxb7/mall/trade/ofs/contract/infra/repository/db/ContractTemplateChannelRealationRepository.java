package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractTemplateChannelRealation;

import java.util.List;

/**
 * 合同模板和电子签渠道关联表(ContractTemplateChannelRealation)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-03 17:24:39
 */
public interface ContractTemplateChannelRealationRepository extends IService<ContractTemplateChannelRealation> {

    List<String> getTplIdByChannelId(String signChannelId);
}

