package com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.util.DubboResultAssert;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Im 发送消息
 */
@Service
@Slf4j
public class ImMessageGateway {

    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;

    private Function<Throwable, Boolean> exceptionFun = (e) -> {
        log.error("[发送信息] exception:{}", e.getMessage());
        return false;
    };


    public boolean sendTextMessage(Supplier<SendTextMsgReqDTO> supplier) {
        return Try.of(() -> {
            SingleResponse<Boolean> response = sendMsgServiceI.sendTextMsg(supplier.get());
            if (response.isSuccess()) {
                return response.getData();
            }
            return false;
        }).getOrElseGet(exceptionFun);
    }

    public boolean sendReminderMsg(Supplier<SendReminderMsgReqDTO> supplier) {

        return Try.of(() -> {
            SingleResponse<Boolean> response = sendMsgServiceI.sendRemindMsg(supplier.get());
            if (response.isSuccess()) {
                return response.getData();
            }
            return false;
        }).getOrElseGet(exceptionFun);
    }

    public void sendIMMessage(String fromUserId, String targetId, List<String> targetUserIds, String title,
                              String content) {
        log.info("sendIMMessage.fromUserId:{}, targetId:{}, targetUserIds:{}, title:{}, content:{}", fromUserId,
            targetId, JSON.toJSONString(targetUserIds) , title, content);
        try {
            SendRichTextMsgReqDTO sendRichTextMsgReqDTO = new SendRichTextMsgReqDTO()
                // 谁发的卡片
                .setFromUserId(fromUserId)
                // 房间
                .setTargetId(targetId)
                // 发送给谁
                .setTargetUserIds(targetUserIds)
                .setTitle(title)
                .setContent(List.of(new RichTextContent(content)));
            SingleResponse<String> stringSingleResponse = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(sendRichTextMsgReqDTO), ErrorCode.RPC_ERROR);
            log.info("sendIMMessage.sendRichTextMsg request:{} , response:{}", JSONObject.toJSONString(sendRichTextMsgReqDTO),
                JSONObject.toJSONString(stringSingleResponse));
        } catch (Exception e) {
            log.warn("[sendIMMessage] error", e);
        }
    }
}
