package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractTemplateGameRealation;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractTemplateGameRealationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 合同模板游戏关联表(ContractTemplateGameRealation)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:06:14
 */
@Slf4j
@Repository
public class ContractTemplateGameRealationDbRepository extends ServiceImpl<ContractTemplateGameRealationMapper, ContractTemplateGameRealation> implements ContractTemplateGameRealationRepository {

    @Override
    public ContractTemplateGameRealation getContratTleWithGameAndId(String templateId, String gameId) {
        LambdaQueryWrapper<ContractTemplateGameRealation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractTemplateGameRealation::getTemplateId, templateId)
            .eq(ContractTemplateGameRealation::getGameId, gameId)
            .eq(ContractTemplateGameRealation::getDeleted, false);
        return baseMapper.selectOne(queryWrapper);
    }
}
