package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.CheckContract;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractStatusEnum;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.SysUserType;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractDataMapper;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.ContractDeliverySearchReq;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.MerchantContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.UserContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.BuyerIndemnityContractRespPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request.ContractDataReqPO;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 合同数据(ContractData)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:06
 */
@Slf4j
@Repository
public class ContractDataDbRepository extends ServiceImpl<ContractDataMapper, ContractData> implements ContractDataRepository {

    public ContractData getValidContract(String orderItemId) {
        LambdaQueryWrapper<ContractData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractData::getOrderItemId, orderItemId);
        wrapper.ne(ContractData::getStatus, 4);
        return this.getOne(wrapper);
    }

    @Override
    public ContractData getContractDataWithOrder(String orderItemId) {
        LambdaQueryWrapper<ContractData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractData::getOrderItemId, orderItemId);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public ContractData findById(String id) {
        return lambdaQuery().and(w -> w.eq(ContractData::getContractDataId, id)
            .or()
            .eq(ContractData::getOrderItemId, id)).one();
    }

    @Override
    public Page<ContractData> selectList(ContractDataReqPO reqPO) {
        Page<ContractData> page = new Page<>(reqPO.getPageIndex(), reqPO.getPageSize());
        LambdaQueryWrapper<ContractData> query = new LambdaQueryWrapper<>();
        //查询合同状态
        Set<String> allContractIds = reqPO.getContractIds();
        if (reqPO.getStatus() != null) {
            ContractStatusEnum contractStatusEnum = ContractStatusEnum.getStatusEnumByCode(reqPO.getStatus());
            switch (contractStatusEnum) {
                case PREPARING, CANCELED -> query.in(ContractData::getContractDataId, allContractIds)
                    .eq(ContractData::getStatus, reqPO.getStatus());
                case SIGNING ->
                    query.in(ContractData::getContractDataId, allContractIds).in(ContractData::getStatus, 1, 2);
                case FINISHED -> {
                    //分情况，纯包赔证明；买家合同；双方合同；包赔证明+卖家合同(前三者和数据库状态一致，最后一种情况会存在不一致的状态)
                    if (CollUtil.isNotEmpty(reqPO.getIndemnityRelatedCrtactStatusMap())) {
                        Set<String> indemnityRelatedContractIds = reqPO.getIndemnityRelatedCrtactStatusMap().keySet();
                        reqPO.getIndemnityRelatedCrtactStatusMap().forEach((contractDataId, status) -> {
                            query.or(w -> w.eq(ContractData::getContractDataId, contractDataId)
                                .eq(ContractData::getStatus, status));
                        });
                        allContractIds.removeAll(indemnityRelatedContractIds);
                        if (CollUtil.isNotEmpty(allContractIds)) {
                            query.or(q -> q.in(ContractData::getContractDataId, allContractIds)
                                .eq(ContractData::getStatus, 3));
                        }
                    } else {
                        query.in(ContractData::getContractDataId, allContractIds)
                            .eq(ContractData::getStatus, reqPO.getStatus());
                    }
                }
            }
        } else {
            query.in(ContractData::getContractDataId, allContractIds);
        }

        // 查询游戏
        if (StringUtils.isNotBlank(reqPO.getGameId())) {
            query.eq(ContractData::getGameId, reqPO.getGameId());
        }

        SysUserType sysUserType = SysUserType.fromCode(reqPO.getUserType());
        switch (sysUserType) {
            case RETAIL -> {
                // 匹配搜索项(游戏账号、订单号、商品编号)
                if (StringUtils.isNotBlank(reqPO.getSearchItem())) {
                    query.and(w -> w.eq(ContractData::getOrderItemId, reqPO.getSearchItem())
                        .or()
                        .eq(ContractData::getPassesNo, reqPO.getSearchItem())
                        .or()
                        .eq(ContractData::getProductUniqueNo, reqPO.getSearchItem()));
                }
                // 号商主账号或者散户查询
                switch (reqPO.getIdentityType()) {
                    case 1 -> query.in(ContractData::getBuyerId, reqPO.getUserIds());
                    case 2 -> query.in(ContractData::getSellerId, reqPO.getUserIds());
                }
            }
            case MERCHANT -> {
                if (StringUtils.isNotBlank(reqPO.getProductUniqueNo())) {
                    query.eq(ContractData::getProductUniqueNo, reqPO.getProductUniqueNo());
                }
                if (StringUtils.isNotBlank(reqPO.getGameAccount())) {
                    query.eq(ContractData::getPassesNo, reqPO.getGameAccount());
                }
                if (reqPO.getCreateStartTime() != null && reqPO.getCreateEndTime() != null) {
                    query.between(ContractData::getCreateTime, reqPO.getCreateStartTime(), reqPO.getCreateEndTime());
                }

                //号商子账号查询
                if (StringUtils.isNotBlank(reqPO.getSubAccountUserId())) {
                    switch (reqPO.getIdentityType()) {
                        case 0 -> query.and(w -> w.eq(ContractData::getBuyerId, reqPO.getSubAccountUserId())
                            .or()
                            .eq(ContractData::getSellerId, reqPO.getSubAccountUserId()));
                        case 1 -> query.eq(ContractData::getBuyerId, reqPO.getSubAccountUserId());
                        case 2 -> query.eq(ContractData::getSellerId, reqPO.getSubAccountUserId());
                    }
                } else {
                    switch (reqPO.getIdentityType()) {
                        case 1 -> query.in(ContractData::getBuyerId, reqPO.getUserIds());
                        case 2 -> query.in(ContractData::getSellerId, reqPO.getUserIds());
                    }
                }

            }
        }

        query.orderByDesc(ContractData::getCreateTime);
        return baseMapper.selectPage(page, query);
    }

    @Override
    public List<BuyerIndemnityContractRespPO> getOwnerIndemnityContractList(ContractDataReqPO contractDataReqPO) {
        return baseMapper.getOwnerIndemnityContractList(contractDataReqPO);
    }

    @Override
    public List<ContractData> queryContractDeliveryData(ContractDeliverySearchReq contractDeliverySearchReq) {
        LambdaQueryWrapper<ContractData> query = new LambdaQueryWrapper<>();
        switch (contractDeliverySearchReq.getSellerIdentity()) {
            case 1 -> query.eq(ContractData::getBuyerUserType, 1)
                .eq(ContractData::getBuyerPhone, contractDeliverySearchReq.getPhone());
            case 2 -> query.eq(ContractData::getBuyerUserType, 2)
                .eq(ContractData::getBuyerMerchantId, contractDeliverySearchReq.getSellerMerchantId());
        }
        query.eq(ContractData::getGameId, contractDeliverySearchReq.getGameId())
            .eq(ContractData::getPassesNo, contractDeliverySearchReq.getPassesNo())
            .eq(ContractData::getFreeze, false)
            .in(ContractData::getStatus, 1, 2, 3)
            .orderByDesc(ContractData::getStartTime);
        return baseMapper.selectList(query);
    }

    @Override
    public Boolean checkContractExist(CheckContract checkContract) {
        LambdaQueryWrapper<ContractData> query = new LambdaQueryWrapper<>();
        query.eq(ContractData::getGameId, checkContract.getGameId())
            .eq(ContractData::getPassesNo, checkContract.getGameAccount())
            .eq(ContractData::getSellerPhone, checkContract.getPhone());
        return baseMapper.exists(query);
    }

    @Override
    public List<ContractData> queryWithInPeriodUserContract(LocalDateTime startDate, String sellerId) {
        LambdaQueryWrapper<ContractData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(w -> w.eq(ContractData::getSellerId, sellerId).or().eq(ContractData::getBuyerId, sellerId))
            .eq(ContractData::getFreeze, false)
            .gt(ContractData::getStartTime, startDate)
            .ge(ContractData::getStartTime, startDate)
            .in(ContractData::getStatus, 1, 2, 3);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ContractData> querySignatoryContractWithInPeriod(List<String> contractDataIds,
                                                                 LocalDateTime startDate) {
        LambdaQueryWrapper<ContractData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractData::getFreeze, false)
            .ge(ContractData::getStartTime, startDate)
            .in(ContractData::getStatus, 1, 2, 3)
            .in(ContractData::getContractDataId, contractDataIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ContractData> queryContractByUserIdAndGameIds(String gameId,
                                                              List<String> makerGameIds,
                                                              String passesNo,
                                                              String sellerUserId,
                                                              String sellerMerchantId) {
        LambdaQueryWrapper<ContractData> queryWrapper = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(makerGameIds)) {
            queryWrapper.in(ContractData::getGameId, makerGameIds);
        } else {
            queryWrapper.eq(ContractData::getGameId, gameId);
        }
        if (StringUtils.isNotBlank(sellerMerchantId)) {
            queryWrapper.eq(ContractData::getSellerMerchantId, sellerMerchantId);
        } else {
            queryWrapper.eq(ContractData::getSellerId, sellerUserId);
        }
        queryWrapper.eq(ContractData::getPassesNo, passesNo)
            .eq(ContractData::getFreeze, false)
            .in(ContractData::getStatus, 1, 2, 3);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ContractData> queryContractUserAndGameId(String gameId,
                                                         String passesNo,
                                                         String sellerUserId,
                                                         String sellerMerchantId) {
        LambdaQueryWrapper<ContractData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractData::getGameId, gameId)
            .eq(ContractData::getPassesNo, passesNo)
            .in(ContractData::getStatus, 1, 2);
        if (StringUtils.isNotBlank(sellerMerchantId)) {
            queryWrapper.eq(ContractData::getBuyerMerchantId, sellerMerchantId);
        } else {
            queryWrapper.eq(ContractData::getBuyerId, sellerUserId);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public ContractData queryEffectiveContract(String orderItemId) {
        LambdaQueryWrapper<ContractData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractData::getOrderItemId, orderItemId).in(ContractData::getStatus, 1, 2, 3);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public ContractData queryLatestContractInfo(UserContractReqPO userContractReqPO) {
        return lambdaQuery().eq(ContractData::getBuyerId, userContractReqPO.getUserId())
            .eq(ContractData::getGameId, userContractReqPO.getGameId())
            .eq(ContractData::getPassesNo, userContractReqPO.getGameAccount())
            .in(ContractData::getStatus, 2, 3)
            .orderByDesc(ContractData::getCreateTime)
            .last("limit 1")
            .oneOpt()
            .orElse(null);
    }

    @Override
    public List<ContractData> queryLatestMerchantContractInfo(MerchantContractReqPO merchantContractReqPO) {
        return lambdaQuery().eq(ContractData::getBuyerMerchantId, merchantContractReqPO.getMerchantId())
            .eq(ContractData::getGameId, merchantContractReqPO.getGameId())
            .in(CollUtil.isNotEmpty(merchantContractReqPO.getGameAccountList()), ContractData::getPassesNo, merchantContractReqPO.getGameAccountList())
            .in(ContractData::getStatus, 2, 3)
            .orderByDesc(ContractData::getCreateTime)
            .list();
    }

    @Override
    public List<ContractData> gameOptionList(String userId) {
        return lambdaQuery().and(queryWrapper -> queryWrapper.eq(ContractData::getBuyerId, userId)
                .or()
                .eq(ContractData::getSellerId, userId))
            .select(ContractData::getContractDataId, ContractData::getOrderItemId, ContractData::getGameId, ContractData::getGameName)
            .list();
    }

    @Override
    public List<ContractData> queryOwnerContractList(String userId) {
        return lambdaQuery().and(queryWrapper -> queryWrapper.eq(ContractData::getBuyerId, userId)
                .or()
                .eq(ContractData::getSellerId, userId))
            .in(ContractData::getStatus, 1, 2)
            .select(ContractData::getContractDataId)
            .list();
    }

    @Override
    public List<ContractData> findContractByIds(List<String> contractIds) {
        return lambdaQuery().in(ContractData::getContractDataId, contractIds)
            .orderByAsc(ContractData::getCreateTime)
            .list();
    }

    @Override
    public ContractData getIndemnityInfoWithContractId(String contractId) {
        return this.baseMapper.getIndemnityInfo(contractId);
    }

    @Override
    public List<ContractData> queryContractByGameIdAndGameAccount(String gameId,
                                                                  List<String> makerGameIds,
                                                                  String passesNo) {
        LambdaQueryWrapper<ContractData> queryWrapper = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(makerGameIds)) {
            queryWrapper.in(ContractData::getGameId, makerGameIds);
        } else {
            queryWrapper.eq(ContractData::getGameId, gameId);
        }
        queryWrapper.eq(ContractData::getPassesNo, passesNo)
            .eq(ContractData::getFreeze, false)
            .in(ContractData::getStatus, 1, 2, 3);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ContractData> findByOrderItemIds(List<String> needToSignOrderItemIdList) {
        return lambdaQuery().in(ContractData::getOrderItemId, needToSignOrderItemIdList)
            .select(ContractData::getContractDataId, ContractData::getOrderItemId, ContractData::getStatus, ContractData::getOrderType, ContractData::getBusinessType, ContractData::getProductId, ContractData::getProductUniqueNo, ContractData::getGameId, ContractData::getPassesNo)
            .list();
    }

    @Override
    public void updateFreezeByContractDataId(String contractDataId, boolean execRes) {
        lambdaUpdate().set(ContractData::getFreeze, execRes)
            .set(ContractData::getStatus, ContractStatusEnum.PREPARING.getCode())
            .eq(ContractData::getContractDataId, contractDataId)
            .update();
    }

    @Override
    public void changeIndemnityIds(String contractDataId, List<String> changeAfterIndemnityIds) {
        lambdaUpdate().set(ContractData::getIndemnityIds, JSON.toJSONString(changeAfterIndemnityIds))
            .eq(ContractData::getContractDataId, contractDataId)
            .update();
    }

    @Override
    public ContractData findOneByContractDataIdAndOrderItemId(String contractDataId, String orderItemId) {
        if (StringUtils.isBlank(contractDataId) || StringUtils.isBlank(orderItemId)) {
            return null;
        }
        return this.lambdaQuery().eq(ContractData::getContractDataId, contractDataId)
            .eq(ContractData::getOrderItemId, orderItemId).oneOpt().orElse(null);
    }

    @Override
    public Boolean delByContractDataIdAndOrderItemId(String contractDataId, String orderItemId, String userId) {
        if (StringUtils.isBlank(contractDataId) || StringUtils.isBlank(orderItemId) || StringUtils.isBlank(userId)) {
            return Boolean.FALSE;
        }
        return this.lambdaUpdate()
            .setSql("is_deleted = id")
            .set(ContractData::getUpdateUserId, userId)
            .set(ContractData::getDeletedTime, LocalDateTime.now())
            .eq(ContractData::getContractDataId, contractDataId)
            .eq(ContractData::getOrderItemId, orderItemId)
            .eq(ContractData::getDeleted, 0)
            .update();
    }
}
