package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSellerInfo;

/**
 * 卖家资料快照信息(ContractSellerInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:08
 */
public interface ContractSellerInfoRepository extends IService<ContractSellerInfo> {

    boolean saveSellerInformation(ContractSellerInfo entity);

    ContractSellerInfo queryLatestSignatoryWithOrder(String orderItemId, String userId, String userName);

    ContractSellerInfo queryLatestSignatoryWithUser(String userId, String userName);

    Boolean checkSellerInfo(String orderItemId, String sellerId, Integer rand);

    ContractSellerInfo querySellerInfo(String orderItemId, String sellerId);

    void delByOrderItemId(String orderItemId);
}
