package com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper;

import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同文件(ContractFile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:07
 */
@Mapper
public interface ContractFileMapper extends BaseMapper<ContractFile> {
    /**
     * 批量新增数据
     *
     * @param entities List<ContractFile> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ContractFile> entities);

}

