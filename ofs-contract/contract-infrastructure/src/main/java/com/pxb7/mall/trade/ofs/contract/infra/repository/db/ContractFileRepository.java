package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;

import java.util.List;

/**
 * 合同文件(ContractFile)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:08
 */
public interface ContractFileRepository extends IService<ContractFile> {

    List<ContractFile> getContractFilesByContractId(String contractId, Integer contractType);

    List<ContractFile> getContractFilesByContractIds(List<String> contractIds);

    boolean updateViewUrlById(String contractFileId, String viewUrl);

    boolean updateVendorAgreementFile(ContractFile contractFile);

    List<ContractFile> getSellerTradeFilesByContractId(String contractId, List<Integer> sellerTradeTypeList);

    void batchDeleteContractFile(List<String> fileIdList);
    List<ContractFile> queryFileListByContractDataId(String contractDataId);
}

