package com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.fastjson2.annotation.JSONField;
import com.pxb7.mall.trade.ofs.common.config.util.MsgDigestUtil;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CompanyVerifyUrlParams extends AbstractApiParams {

    @JSONField(name = "customer_id")
    private String customerId;

    @JSONField(name = "page_modify")
    private String pageModify;

    @Override
    public String joinContentStr() {
        var str = JSON.toJSONString(this);
        return MsgDigestUtil.sortString(JSON.parseObject(str, new TypeReference<>() {}));
    }
}
