package com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.BuyerIndemnityContractRespPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request.ContractDataReqPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同数据(ContractData)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:06
 */
@Mapper
public interface ContractDataMapper extends BaseMapper<ContractData> {
    /**
     * 批量新增数据
     *
     * @param entities List<ContractData> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ContractData> entities);

    /**
     * 获取买家包赔证明相关的合同列表
     *
     * @param reqPO
     * @return
     */
    List<BuyerIndemnityContractRespPO> getOwnerIndemnityContractList(@Param("reqPO") ContractDataReqPO reqPO);

    /**
     * 查询合同包赔信息
     *
     * @param contractDataId
     * @return
     */
    ContractData getIndemnityInfo(@Param("contractDataId") String contractDataId);
}

