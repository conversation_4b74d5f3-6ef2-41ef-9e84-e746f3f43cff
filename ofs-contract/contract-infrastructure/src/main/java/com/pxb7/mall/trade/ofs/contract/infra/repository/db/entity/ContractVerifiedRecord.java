package com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 法大大实名认证记录(ContractVerifiedRecord)实体类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "contract_verified_record")
public class ContractVerifiedRecord implements Serializable {
    private static final long serialVersionUID = -14955452757833301L;
    /**
     * 主键id
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 认证记录业务id
     */
    @TableId(value = "verified_record_id", type = IdType.INPUT)
    private String verifiedRecordId;
    /**
     * 螃蟹平台签约人id
     */
    @TableField(value = "signatory_user_id")
    private String signatoryUserId;
    /**
     * 法大大的客户编号
     */
    @TableField(value = "customer_id")
    private String customerId;
    /**
     * 法大大实名认证交易号
     */
    @TableField(value = "transaction_no")
    private String transactionNo;
    /**
     * 实名地址
     */
    @TableField(value = "auth_url")
    private String authUrl;
    /**
     * 认证方式 0腾讯云认证 1三要素认证 2手势照认证 3四要素认证 4蚂蚁金服认证
     */
    @TableField(value = "verified_way")
    private Integer verifiedWay;
    /**
     * 认证状态0:未激活；1:未认证； 2:审核通过； 3:已提交待审核； 4:审核不通过
     */
    @TableField(value = "verified_status")
    private Integer verifiedStatus;
    /**
     * 认证的手机号
     */
    @TableField(value = "verified_phone")
    private String verifiedPhone;
    /**
     * 银行卡号
     */
    @TableField(value = "bank_card_no")
    private String bankCardNo;
    /**
     * 审核通过时间
     */
    @TableField(value = "audit_time")
    private LocalDateTime auditTime;
    /**
     * 不通过原因
     */
    @TableField(value = "reject_reason")
    private String rejectReason;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
