package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.CheckContract;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.ContractDeliverySearchReq;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.MerchantContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.UserContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.BuyerIndemnityContractRespPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request.ContractDataReqPO;

/**
 * 合同数据(ContractData)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:06
 */
public interface ContractDataRepository extends IService<ContractData> {

    ContractData getContractDataWithOrder(String orderItemId);

    ContractData findById(String id);

    Page<ContractData> selectList(ContractDataReqPO reqPO);

    /**
     * 合同交付数据查询
     *
     * @param contractDeliverySearchReq 合同交付查询参数
     * @return 合同列表
     */
    List<ContractData> queryContractDeliveryData(ContractDeliverySearchReq contractDeliverySearchReq);

    Boolean checkContractExist(CheckContract checkContract);

    List<ContractData> queryWithInPeriodUserContract(LocalDateTime startDate, String sellerId);

    List<ContractData> querySignatoryContractWithInPeriod(List<String> contractDataIds, LocalDateTime startDate);

    List<ContractData> queryContractByUserIdAndGameIds(String gameId,
                                                       List<String> makerGameIds,
                                                       String passesNo,
                                                       String sellerUserId,
                                                       String sellerMerchantId);

    List<ContractData> queryContractUserAndGameId(String gameId,
                                                  String passesNo,
                                                  String sellerUserId,
                                                  String sellerMerchantId);

    /**
     * 查询有效的合同数据
     *
     * @param orderItemId 订单id
     * @return
     */
    ContractData queryEffectiveContract(String orderItemId);

    /**
     * 查询当前游戏下账号最新的合同数据
     *
     * @param userContractReqPO
     * @return
     */
    ContractData queryLatestContractInfo(UserContractReqPO userContractReqPO);

    /**
     * 查询商家作为买家最新的合同数据
     *
     * @param merchantContractReqPO
     * @return
     */
    List<ContractData> queryLatestMerchantContractInfo(MerchantContractReqPO merchantContractReqPO);

    List<BuyerIndemnityContractRespPO> getOwnerIndemnityContractList(ContractDataReqPO contractDataReqPO);

    /**
     * 查询用户合同所属的游戏列表
     *
     * @param userId
     * @return
     */
    List<ContractData> gameOptionList(String userId);

    /**
     * 查询属于自己的合同列表
     *
     * @param userId
     * @return
     */
    List<ContractData> queryOwnerContractList(String userId);

    /**
     * 风控详情合同信息
     *
     * @param contractIds 合同ID列表
     * @return
     */
    List<ContractData> findContractByIds(List<String> contractIds);

    ContractData getIndemnityInfoWithContractId(String contractId);

    /**
     * 一号多卖 查对应合同数据
     *
     * @param gameId       游戏ID
     * @param makerGameIds 一个系列的游戏ID 如 腾讯系
     * @param passesNo     游戏账号
     * @return 合同
     */
    List<ContractData> queryContractByGameIdAndGameAccount(String gameId, List<String> makerGameIds, String passesNo);

    /**
     * 查询需要签署的合同列表
     *
     * @param needToSignOrderItemIdList
     * @return
     */
    List<ContractData> findByOrderItemIds(List<String> needToSignOrderItemIdList);

    /**
     * 更新合同冻结状态
     *
     * @param contractDataId 合同ID
     * @param execRes        冻结 true  未冻结 false
     */
    void updateFreezeByContractDataId(String contractDataId, boolean execRes);

    /**
     * 变更包赔
     *
     * @param contractDataId
     * @param changeAfterIndemnityIds
     */
    void changeIndemnityIds(String contractDataId, List<String> changeAfterIndemnityIds);

    ContractData  findOneByContractDataIdAndOrderItemId(String contractDataId,String orderItemId);

    Boolean delByContractDataIdAndOrderItemId(String contractDataId,String orderItemId,String userId);
}

