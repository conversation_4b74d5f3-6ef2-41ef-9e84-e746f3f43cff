package com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: MerchantContractReqPO.java
 * @description:
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/11/7 10:23
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class MerchantContractReqPO implements Serializable {
    /**
     * 号商id
     */
    private String merchantId;

    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 游戏账号列表
     */
    private List<String> gameAccountList;
}
