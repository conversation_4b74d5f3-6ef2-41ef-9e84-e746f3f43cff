package com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper;

import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSellerInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 卖家资料快照信息(ContractSellerInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:08
 */
@Mapper
public interface ContractSellerInfoMapper extends BaseMapper<ContractSellerInfo> {
    /**
     * 批量新增数据
     *
     * @param entities List<ContractSellerInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ContractSellerInfo> entities);

}

