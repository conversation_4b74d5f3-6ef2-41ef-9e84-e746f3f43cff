package com.pxb7.mall.trade.ofs.contract.infra.repository.es.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 放款单列表查询类
 * 
 * <AUTHOR>
 * @since 2024-07-25 15:02:12
 */
@Data
public class ExtPaymentListEs implements Serializable {
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 订单编号
     */
    private String orderItemId;
    /**
     * 放款单编号
     */
    private String extPaymentId;
    /**
     * 游戏id
     */
    private String gameId;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品类型/订单类型
     */
    private Integer productType;
    /**
     * 收款类型
     */
    private Integer extType;
    /**
     * 手机号
     */
    private String userTelphone;

    /**
     * 操作人员
     */
    private String operationUserId;
    /**
     * 数据来源
     */
    private Integer dataSource;

    private Integer pageNum;
    private Integer pageSize;

}
