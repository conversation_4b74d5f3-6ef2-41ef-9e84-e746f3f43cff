package com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractTemplate;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 合同模板(ContractTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-29 15:06:13
 */
@Mapper
public interface ContractTemplateMapper extends BaseMapper<ContractTemplate> {

    List<ContractTemplate> getTemplateList(String signChannelId,
                                           String gameId,
                                           Integer contractType,
                                           List<String> indemnityTypeCodes);

}
