package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractVerifiedRecord;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractVerifiedRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 法大大实名认证记录(ContractVerifiedRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:10
 */
@Slf4j
@Repository
public class ContractVerifiedRecordDbRepository extends ServiceImpl<ContractVerifiedRecordMapper, ContractVerifiedRecord> implements ContractVerifiedRecordRepository {
    @Override
    public List<ContractVerifiedRecord> getVerifiedRecordListBySignatoryId(String signatoryUserId) {
        return lambdaQuery().eq(ContractVerifiedRecord::getSignatoryUserId, signatoryUserId).list();
    }

    @Override
    public ContractVerifiedRecord queryVerifiedRecord(String signatoryUserId, String customerId) {
        return lambdaQuery().eq(ContractVerifiedRecord::getSignatoryUserId, signatoryUserId)
            .eq(ContractVerifiedRecord::getCustomerId, customerId)
            .eq(ContractVerifiedRecord::getDeleted, false)
            .orderByDesc(ContractVerifiedRecord::getUpdateTime)
            .last("limit 1")
            .one();
    }
}
