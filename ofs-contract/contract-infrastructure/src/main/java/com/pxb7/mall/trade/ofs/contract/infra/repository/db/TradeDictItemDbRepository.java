package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.TradeDictItem;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.TradeDictItemMapper;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.ContractCollectUrlRespPO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典数据
 *
 * <AUTHOR>
 * @since: 2024-09-02 16:43
 **/
@Service
public class TradeDictItemDbRepository extends ServiceImpl<TradeDictItemMapper, TradeDictItem> implements TradeDictItemRepository {
    @Override
    public String getContractLimitTime() {
        LambdaQueryWrapper<TradeDictItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TradeDictItem::getDictType, "contract");
        queryWrapper.eq(TradeDictItem::getDictKey, "dead_line");
        TradeDictItem dictItem = this.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(dictItem)) {
            throw new BusinessException(ErrorCode.CONTRACT_LIMIT_TIME_NOTFOUND);
        }
        return dictItem.getDictValue();
    }

    @Override
    public String getCustomerName(Integer busChannel) {
        return lambdaQuery().eq(TradeDictItem::getDictType, "customerName")
            .eq(TradeDictItem::getDictKey, busChannel.toString())
            .oneOpt()
            .orElseThrow(() -> new BusinessException(ErrorCode.CONTRACT_SIGNATORY_ENTITY_NOT_CONFIGURED))
            .getDictValue();
    }

    public List<ContractCollectUrlRespPO> getContractCollectionUrl(String type) {
        return lambdaQuery().eq(TradeDictItem::getDictType, type)
            .list()
            .stream()
            .map(item -> new ContractCollectUrlRespPO().setClientType(Integer.parseInt(item.getDictKey()))
                .setUrl(item.getDictValue()))
            .toList();

    }
}
