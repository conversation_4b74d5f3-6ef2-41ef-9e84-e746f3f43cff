package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 合同签署信息(ContractSignInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:09
 */
public interface ContractSignInfoRepository extends IService<ContractSignInfo> {

    /**
     * 获取当前签约人的合同IDs
     *
     * @param userId
     * @param currentUser
     * @return
     */
    List<ContractSignInfo> getSignInfoByCurrentUserId(String userId, Boolean currentUser);

    ContractSignInfo queryContractSignInfoByUserId(String userId, String contractId);

    List<ContractSignInfo> queryContractSignInfos(String userId, List<String> contractIds);

    List<ContractSignInfo> queryContractSignInfo(String signatoryUserId);

    List<ContractSignInfo> getOwnerContractSignList(Set<String> userIds,
                                                    Boolean isCurrentUser,
                                                    LocalDateTime createStartTime,
                                                    LocalDateTime createEndTime);

    Boolean querySignInfoAsBuyer(String userId, String contractDataId);

    Long contractCount(String userId, List<String> contractIdList);

    List<ContractSignInfo> querySignInfoByContractId(String contractDataId);

    List<ContractSignInfo> querySignInfoByFileId(String contractFileId);

    List<ContractSignInfo> queryContractSignInfoByContractDataId(List<String> contractDataIds);

    ContractSignInfo getContractSignInfoByUserId(String userId);

    boolean updateVendorSignedInfo(ContractSignInfo vendorSignInfo);

    ContractSignInfo getLatestVendorSignInfo(ContractSignInfo vendorSignInfo);

    ContractSignInfo getLatestVendorSignInfo(String loginUserId, String contractDataId, String contractFileId);

    List<ContractSignInfo> getSignInfoListByFileId(String contractFileId);


}

