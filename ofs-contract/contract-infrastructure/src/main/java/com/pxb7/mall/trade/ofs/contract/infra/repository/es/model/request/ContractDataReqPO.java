package com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request;

import com.pxb7.mall.trade.ofs.contract.client.PageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractDataReqDTO.java
 * @description: 合同列表请求参数
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/8/27 14:46
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContractDataReqPO extends PageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4211632830226811675L;
    /**
     * 搜索项（游戏账号、订单号、商品编号）
     */
    private String searchItem;

    /**
     * 合同状态 0准备中 1待签署 2签署中(我方待签署、对方待签署) 3已完成 4已取消
     */
    private Integer status;

    /**
     * 是否是当前用户(status=2时必须传该字段)
     */
    private Boolean currentUser;

    /**
     * 游戏名称
     */
    private String gameId;
    /**
     * 身份类型 0全部 1买家 2卖家
     */
    private Integer identityType;
    /**
     * 业务类型 1：成品号 2：充值 3：金币 4：装备 5：初始号
     */
    private String businessType;
    /**
     * 当前用户ID
     */
    private Set<String> userIds;
    /**
     * 筛选后的合同id列表
     */
    private Set<String> contractIds;

    /**
     * 商家子账号userId
     */
    private String subAccountUserId;

    /**
     * 商品编码(商家中心)
     */
    private String productUniqueNo;
    /**
     * 游戏账号(商家中心)
     */
    private String gameAccount;
    /**
     * 用户类型 1:个人 2:商家
     */
    private Integer userType = 1;

    /**
     * 包赔关联的合同状态
     */
    private Map<String /**contractId**/, Integer /**contractStatus**/> indemnityRelatedCrtactStatusMap = new HashMap<>();

    /**
     * 开始时间(商家中心)
     */
    private LocalDateTime createStartTime;
    /**
     * 结束时间(商家中心)
     */
    private LocalDateTime createEndTime;

    public Integer getIdentityType() {
        return identityType == null ? 0 : identityType;
    }
}
