package com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 交付-合同处理记录(ContractDeliveryHandleLog)实体类
 *
 * <AUTHOR>
 * @since 2025-03-05 17:14:02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "contract_delivery_handle_log")
public class ContractDeliveryHandleLog implements Serializable {
    private static final long serialVersionUID = 727702538060826936L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 交付id
     */
    @TableField(value = "delivery_id")
    private String deliveryId;
    /**
     * 订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 处理状态 0:未处理 1:处理中 2:处理完成 3:执行异常
     */
    @TableField(value = "handle_status")
    private Integer handleStatus;
    /**
     * 异常原因
     */
    @TableField(value = "exception_cause")
    private String exceptionCause;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Integer isDeleted;

}

