package com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class MergeContractParams extends AbstractApiParams {

    /**
     * 合同编号
     */
    @JSONField(name = "contract_id")
    private String contractId;

    /**
     * 文档标题
     */
    @JSONField(name = "doc_title")
    private String docTitle;

    /**
     * 需要合并的合同编号
     */
    @JSONField(name = "contract_ids")
    private String contractIds;

    @Override
    public String joinContentStr() {
        return this.contractId + this.contractIds + this.docTitle;
    }
}
