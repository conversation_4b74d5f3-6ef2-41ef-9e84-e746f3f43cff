package com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同签署信息(ContractSignInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "contract_sign_info")
public class ContractSignInfo implements Serializable {
    private static final long serialVersionUID = 793436690408865602L;
    /**
     * 主键id
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 合同签署id
     */
    @TableId(value = "contract_sign_id", type = IdType.INPUT)
    private String contractSignId;
    /**
     * 合同文件id
     */
    @TableField(value = "contract_file_id")
    private String contractFileId;

    /**
     * 合同id
     */
    @TableField(value = "contract_data_id")
    private String contractDataId;

    /**
     * 螃蟹平台签约人id
     */
    @TableField(value = "signatory_user_id")
    private String signatoryUserId;

    /**
     * 螃蟹平台签约人id
     */
    @TableField(value = "user_id")
    private String userId;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 交易号
     */
    @TableField(value = "transaction_id")
    private String transactionId;
    /**
     * 客户编号
     */
    @TableField(value = "customer_id")
    private String customerId;
    /**
     * 合同签署方 1买家 2卖家 3螃蟹博淳 4江苏珍珍淼(支付宝)--【3、4为自动签】
     */
    @TableField(value = "signatory_type")
    private Integer signatoryType;
    /**
     * 合同签署链接
     */
    @TableField(value = "sign_url")
    private String signUrl;
    /**
     * 签署状态 0待签署 1签署完成
     */
    @TableField(value = "signed_status")
    private Integer signedStatus;

    /**
     * 对方签署状态 1已签署 0未签署
     */
    @TableField(value = "is_peer_sign_status")
    private Boolean peerSignStatus;
    /**
     * 签署完成时间
     */
    @TableField(value = "signed_finish_time")
    private LocalDateTime signedFinishTime;

    /**
     * IM(融云)卡片消息ID
     */
    @TableField(value = "msg_uid")
    private String msgUid;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
