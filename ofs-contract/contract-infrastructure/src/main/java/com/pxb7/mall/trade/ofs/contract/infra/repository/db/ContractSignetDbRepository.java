package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractSignetMapper;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignet;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractSignetRepository;

/**
 * 印章(ContractSignet)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:06:13
 */
@Slf4j
@Repository
public class ContractSignetDbRepository extends ServiceImpl<ContractSignetMapper, ContractSignet> implements ContractSignetRepository {

}
