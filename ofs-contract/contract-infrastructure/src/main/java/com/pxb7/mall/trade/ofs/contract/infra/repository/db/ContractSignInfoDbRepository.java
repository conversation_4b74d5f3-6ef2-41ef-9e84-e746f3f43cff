package com.pxb7.mall.trade.ofs.contract.infra.repository.db;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.pxb7.mall.trade.ofs.contract.client.enums.ContractUserType;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.SignedStatus;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignInfo;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractSignInfoMapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 合同签署信息(ContractSignInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-29 15:05:09
 */
@Slf4j
@Repository
public class ContractSignInfoDbRepository extends ServiceImpl<ContractSignInfoMapper, ContractSignInfo> implements ContractSignInfoRepository {

    @Override
    public List<ContractSignInfo> getSignInfoByCurrentUserId(String userId, Boolean currentUser) {
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<ContractSignInfo>();
        queryWrapper.eq(ContractSignInfo::getUserId, userId)
            .and(currentUser != null && currentUser, q -> q.eq(ContractSignInfo::getSignedStatus, false))
            .and(!currentUser, q -> q.eq(ContractSignInfo::getPeerSignStatus, false));
        return this.list(queryWrapper);
    }

    @Override
    public ContractSignInfo queryContractSignInfoByUserId(String userId, String contractId) {
        if (StrUtil.isEmpty(userId)) {
            return null;
        }
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<ContractSignInfo>();
        queryWrapper.eq(StrUtil.isNotBlank(userId), ContractSignInfo::getUserId, userId)
            .eq(ContractSignInfo::getContractDataId, contractId);
        queryWrapper.orderByDesc(ContractSignInfo::getCreateTime);
        queryWrapper.last("limit 1");
        return this.getOneOpt(queryWrapper).orElse(null);
    }

    @Override
    public List<ContractSignInfo> queryContractSignInfos(String userId, List<String> contractIds) {
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<ContractSignInfo>();
        queryWrapper.eq(ContractSignInfo::getUserId, userId)
            .eq(ContractSignInfo::getSignatoryType, 1)
            .in(ContractSignInfo::getContractDataId, contractIds);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean querySignInfoAsBuyer(String userId, String contractDataId) {
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<ContractSignInfo>();
        queryWrapper.eq(ContractSignInfo::getUserId, userId)
            .eq(ContractSignInfo::getContractDataId, contractDataId)
            .eq(ContractSignInfo::getSignatoryType, 1)
            .eq(ContractSignInfo::getSignedStatus, 1);
        return this.exists(queryWrapper);
    }

    @Override
    public List<ContractSignInfo> queryContractSignInfo(String signatoryUserId) {
        return lambdaQuery().eq(ContractSignInfo::getSignatoryUserId, signatoryUserId).list();
    }

    @Override
    public List<ContractSignInfo> getOwnerContractSignList(Set<String> userIds,
                                                           Boolean isCurrentUser,
                                                           LocalDateTime createStartTime,
                                                           LocalDateTime createEndTime) {
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ContractSignInfo::getUserId, userIds);
        if (isCurrentUser != null) { //contractStatus=2【签署中】(需要区分当前用户是否签署)
            if (isCurrentUser) {
                // 当前用户(我方)待签署
                queryWrapper.eq(ContractSignInfo::getSignedStatus, 0);
            } else {
                // 对方待签署(我方已签署)
                queryWrapper.eq(ContractSignInfo::getSignedStatus, 1);
            }
        }
        if (createStartTime != null && createEndTime != null) {
            queryWrapper.between(ContractSignInfo::getCreateTime, createStartTime, createEndTime);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Long contractCount(String userId, List<String> contractIdList) {
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractSignInfo::getUserId, userId)
            .eq(ContractSignInfo::getSignedStatus, 0)
            .in(ContractSignInfo::getContractDataId, contractIdList);
        return count(queryWrapper);
    }

    @Override
    public List<ContractSignInfo> querySignInfoByContractId(String contractDataId) {
        return lambdaQuery().eq(ContractSignInfo::getContractDataId, contractDataId).list();
    }

    @Override
    public List<ContractSignInfo> querySignInfoByFileId(String contractFileId) {
        return lambdaQuery().eq(ContractSignInfo::getContractFileId, contractFileId).list();
    }

    @Override
    public List<ContractSignInfo> queryContractSignInfoByContractDataId(List<String> contractDataIds) {
        if (CollUtil.isEmpty(contractDataIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ContractSignInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ContractSignInfo::getContractDataId, contractDataIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public ContractSignInfo getContractSignInfoByUserId(String userId) {
        return lambdaQuery().eq(ContractSignInfo::getUserId, userId)
            .eq(ContractSignInfo::getSignedStatus, SignedStatus.FINISH.getCode())
            .orderByDesc(ContractSignInfo::getCreateTime)
            .last(" limit 1")
            .one();
    }

    @Override
    public boolean updateVendorSignedInfo(ContractSignInfo vendorSignInfo) {
        return lambdaUpdate().eq(ContractSignInfo::getOrderItemId, vendorSignInfo.getOrderItemId())
            .eq(ContractSignInfo::getContractDataId, vendorSignInfo.getContractDataId())
            .eq(ContractSignInfo::getUserId, vendorSignInfo.getUserId())
            .eq(ContractSignInfo::getSignatoryType, ContractUserType.SELLER.getCode())
            .set(ContractSignInfo::getContractSignId, vendorSignInfo.getContractSignId())
            .set(ContractSignInfo::getContractFileId, vendorSignInfo.getContractFileId())
            .set(ContractSignInfo::getSignatoryUserId, vendorSignInfo.getSignatoryUserId())
            .set(ContractSignInfo::getTransactionId, vendorSignInfo.getTransactionId())
            .set(ContractSignInfo::getCustomerId, vendorSignInfo.getCustomerId())
            .set(ContractSignInfo::getSignUrl, vendorSignInfo.getSignUrl())
            .set(ContractSignInfo::getSignedStatus, vendorSignInfo.getSignedStatus())
            .set(ContractSignInfo::getPeerSignStatus, vendorSignInfo.getPeerSignStatus())
            .set(ContractSignInfo::getUpdateUserId, vendorSignInfo.getUpdateUserId())
            .update();
    }

    @Override
    public ContractSignInfo getLatestVendorSignInfo(ContractSignInfo vendorSignInfo) {
        return lambdaQuery().eq(ContractSignInfo::getOrderItemId, vendorSignInfo.getOrderItemId())
            .eq(ContractSignInfo::getContractDataId, vendorSignInfo.getContractDataId())
            .eq(ContractSignInfo::getUserId, vendorSignInfo.getUserId())
            .eq(ContractSignInfo::getSignatoryType, ContractUserType.SELLER.getCode())
            .orderByDesc(ContractSignInfo::getCreateTime)
            .last(" limit 1")
            .one();
    }

    @Override
    public ContractSignInfo getLatestVendorSignInfo(String loginUserId, String contractDataId, String contractFileId) {
        if (StrUtil.isBlank(loginUserId) || StrUtil.isBlank(contractDataId) || StrUtil.isBlank(contractFileId)) {
            return null;
        }
        return lambdaQuery().eq(ContractSignInfo::getUserId, loginUserId)
            .eq(ContractSignInfo::getContractDataId, contractDataId)
            .eq(ContractSignInfo::getContractFileId, contractFileId)
            .eq(ContractSignInfo::getSignatoryType, ContractUserType.SELLER.getCode())
            .orderByDesc(ContractSignInfo::getCreateTime)
            .last(" limit 1")
            .one();
    }

    @Override
    public List<ContractSignInfo> getSignInfoListByFileId(String contractFileId) {
        return lambdaQuery().eq(ContractSignInfo::getContractFileId, contractFileId).list();
    }
}
