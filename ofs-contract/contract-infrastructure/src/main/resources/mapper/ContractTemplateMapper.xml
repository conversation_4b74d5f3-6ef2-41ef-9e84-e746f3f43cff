<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractTemplateMapper">
    <resultMap id="contractTemplateMap" type="com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractTemplate">
        <result column="indemnity_type" property="indemnityType" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="indemnity_type_code" property="indemnityTypeCode" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
    </resultMap>
    <select id="getTemplateList" resultMap="contractTemplateMap">
        select t.template_id,t.bus_channel,t.bus_scenario,t.template_type,t.external_name,t.internal_name,t.game_range,
            t.is_need_sign as needSign,t.variable,t.doc_url,t.contract_type,t.indemnity_type,t.indemnity_type_code,
            t.is_support_splice as supportSplice,t.splice_contract_type,c.app_id,c.app_secret,c.sign_channel_id
        from contract_template t
            inner join contract_template_channel_realation cr on cr.template_id = t.template_id
            left join contract_template_game_realation gr on gr.template_id = cr.template_id and gr.game_id = #{gameId}
            inner join contract_sign_channel c on c.sign_channel_id = cr.sign_channel_id
        where cr.sign_channel_id = #{signChannelId} and t.is_deleted = 0 and c.status = 1
            and (t.game_range = 0 or (t.game_range = 1 and gr.game_id = #{gameId} and gr.is_deleted=0))
            and ((t.template_type = 1
            <if test="indemnityTypeCodes != null and indemnityTypeCodes.size() > 0">
                and (
                <foreach item="item" collection="indemnityTypeCodes" open="(" separator="or" close=")">
                    json_contains(t.indemnity_type_code, '"${item}"')
                </foreach>)
            </if>
            ) or (t.template_type = 2 and t.contract_type = #{contractType}))
        group by t.template_id
    </select>
</mapper>

