<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractByGoodsLogMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractByGoodsLog" >
     <result  column="id" jdbcType="BIGINT" property="id" />
     <result  column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
     <result  column="product_id" jdbcType="VARCHAR" property="productId" />
     <result  column="game_id" jdbcType="VARCHAR" property="gameId" />
     <result  column="game_name" jdbcType="VARCHAR" property="gameName" />
     <result  column="user_type" jdbcType="VARCHAR" property="userType" />
     <result  column="phone" jdbcType="VARCHAR" property="phone" />
     <result  column="user_merchant_id" jdbcType="VARCHAR" property="userMerchantId" />
     <result  column="passes_no" jdbcType="VARCHAR" property="passesNo" />
     <result  column="result" jdbcType="VARCHAR" property="result" />
     <result  column="create_time" jdbcType="TIMESTAMP" property="createTime" />
     <result  column="sys_user_id" jdbcType="VARCHAR" property="sysUserId" />
     <result  column="sys_user_name" jdbcType="VARCHAR" property="sysUserName" />
     <result  column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
    </resultMap>
        
    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into contract_by_goods_log(order_item_id,product_id,game_id,game_name,user_type,phone,user_merchant_id,passes_no,result,create_time,sys_user_id,sys_user_name,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.orderItemId},#{entity.productId},#{entity.gameId},#{entity.gameName},#{entity.userType},#{entity.phone},#{entity.userMerchantId},#{entity.passesNo},#{entity.result},#{entity.createTime},#{entity.sysUserId},#{entity.sysUserName},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

