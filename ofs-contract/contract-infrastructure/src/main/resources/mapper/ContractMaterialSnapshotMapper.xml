<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractMaterialSnapshotMapper">

    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractMaterialSnapshot">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="msg_uid" jdbcType="VARCHAR" property="msgUid"/>
        <result column="delivery_room_id" jdbcType="VARCHAR" property="deliveryRoomId"/>
        <result column="rand" jdbcType="INTEGER" property="rand"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        contract_material_snapshot(order_item_id,user_id,user_type,msg_uid,delivery_room_id,rand,customer_id,phone,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderItemId},#{entity.userId},#{entity.userType},#{entity.msgUid},#{entity.deliveryRoomId},#{entity.rand},#{entity.customerId},#{entity.phone},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.isDeleted})
        </foreach>
    </insert>
</mapper>

