<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ofs.contract.infra.repository.db.mapper.ContractSignatoryUserMapper">
    <resultMap id="getUserInfoResultMap"
               type="com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.GetUserInfoResultRespPO">
        <result column="user_name" property="userName"/>
        <result column="phone" property="phone"/>
        <result column="verified_status" property="verifiedStatus"/>
        <result column="cert_no" property="certNo"/>
    </resultMap>
    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignatoryUser">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="signatory_user_id" jdbcType="VARCHAR" property="signatoryUserId"/>
        <result column="sign_channel_id" jdbcType="VARCHAR" property="signChannelId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="cert_type" jdbcType="SMALLINT" property="certType"/>
        <result column="cert_no" jdbcType="VARCHAR" property="certNo"/>
        <result column="cert_front_img" jdbcType="VARCHAR" property="certFrontImg"/>
        <result column="cert_back_img" jdbcType="VARCHAR" property="certBackImg"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="transaction_no" jdbcType="VARCHAR" property="transactionNo"/>
        <result column="is_mark" jdbcType="BOOLEAN" property="mark"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into contract_signatory_user(signatory_user_id, sign_channel_id, user_name, phone,
        cert_type, cert_no, cert_front_img, cert_back_img, customer_id,
        transaction_no, is_mark, create_user_id, create_time, update_user_id,
        update_time, is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.signatoryUserId}, #{entity.signChannelId}, #{entity.userName}, #{entity.phone},
            #{entity.certType}, #{entity.certNo}, #{entity.certFrontImg}, #{entity.certBackImg},
            #{entity.customerId}, #{entity.transactionNo}, #{entity.mark}, #{entity.createUserId},
            #{entity.createTime}, #{entity.updateUserId}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
    </insert>

    <select id="getUserInfo" resultMap="getUserInfoResultMap">
        select csu.user_name, csu.phone, csu.cert_no, cvr.verified_status
        from contract_signatory_user csu
                 left join contract_verified_record cvr on csu.customer_id = cvr.customer_id
        where csu.is_deleted = 0
          and csu.signatory_user_id = #{signatoryUserId}
        order by cvr.create_time desc limit 1
    </select>
</mapper>

