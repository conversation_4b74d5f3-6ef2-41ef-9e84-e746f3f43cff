package com.pxb7.mall.trade.ofs.adapter.utils;

import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.auth.dto.ImUserDTO;
import com.pxb7.mall.auth.dto.LoginUserDTO;
import com.pxb7.mall.auth.dto.MerchantUserDTO;
import com.pxb7.mall.trade.ofs.common.config.models.UserBaseInfo;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/24 20:00
 **/
public class UserUtil {
    /**
     * 获取用户信息
     */
    public static UserBaseInfo getUserInfo() {
        if (ImUserUtil.isLogin()) {
            ImUserDTO imUser = ImUserUtil.getImUser();
            return new UserBaseInfo().setUserId(Objects.isNull(imUser) ? "" : imUser.getUserId())
                .setUserName(Objects.isNull(imUser) ? "" : imUser.getUserName()).setIsMerchant(Boolean.FALSE);
        } else if (LoginUserUtil.isLogin()) {
            LoginUserDTO loginUser = LoginUserUtil.getLoginUser();
            return new UserBaseInfo().setUserId(Objects.isNull(loginUser) ? "" : loginUser.getUserId())
                .setUserName(Objects.isNull(loginUser) ? "" : loginUser.getUserName()).setIsMerchant(Boolean.FALSE);
        } else if (MerchantUserUtil.isLogin()) {
            MerchantUserDTO merchantUser = MerchantUserUtil.getMerchantUser();
            return new UserBaseInfo().setUserId(Objects.isNull(merchantUser) ? "" : merchantUser.getUserId())
                .setUserName(Objects.isNull(merchantUser) ? "" : merchantUser.getUserName())
                .setIsMerchant(Boolean.TRUE);
        } else if (AdminUserUtil.isLogin()) {
            AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
            return new UserBaseInfo().setUserId(Objects.isNull(adminUser) ? "" : adminUser.getUserId())
                .setUserName(Objects.isNull(adminUser) ? "" : adminUser.getUserName()).setIsMerchant(Boolean.FALSE);
        }
        return new UserBaseInfo();
    }
}
