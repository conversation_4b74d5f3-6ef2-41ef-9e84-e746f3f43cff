package com.pxb7.mall.trade.ofs.adapter.consumer;

import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.messaging.MQProducer;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Flow;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ofs.app.config.AssertIdeaCondition;
import com.pxb7.mall.trade.ofs.app.delivery.automation.ExchangeBindingAppService;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.ExchangeBindingNextNodeDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.FlowNextReqDTO;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.ExchangeBindingDelayMessage;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.ExchangeBindingDelayOperationEnum;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = ConsumerConstants.EXCHANGE_BINDING_DELAY_MESSAGE_CONSUMER,
    topic = MqConstants.INTELLIGENT_DELIVERY_DELAY, tag = "*")
public class ExchangeBindingDelayListener implements RocketMQListenerExt<ExchangeBindingDelayMessage> {

    @Resource
    private ExchangeBindingAppService exchangeBindingAppService;
    @Resource
    private FlowDomainService flowDomainService;

    @Resource
    private MQProducer mqProducer;

    @Value("${delivery.delay.maxTime:24}")
    private int delayMaxTime;

    @Override
    public ConsumeResult consume(MessageView messageView, ExchangeBindingDelayMessage payload) {
        try {
            String key = RedisConstants.FLOW_IP_WAIT.concat(payload.getOperation()).concat(payload.getOrderItemId());
            if (StringUtils.isNotBlank(payload.getBindingFlowNodeId())) {
                key = key.concat(payload.getBindingFlowNodeId());
            }
            boolean notConsume = RedissonUtils.setIfAbsent(key, "0", 3, ChronoUnit.SECONDS);
            if (!notConsume) {
                return ConsumeResult.SUCCESS;
            }

            String orderItemId = payload.getOrderItemId();
            Flow flow = flowDomainService.getFlow(orderItemId);
            if (FlowNodeEnum.TRADE_TERMINATION.getNode().equals(flow.getFlowNode())) {
                return ConsumeResult.SUCCESS;
            }

            log.info("接收到了换绑延时消息 msgId:{}，payload:{}", messageView.getMessageId(), payload);

            // 先判断延时消息是否到了时间
            LocalDateTime now = LocalDateTime.now();
            if (Objects.nonNull(payload.getDelayTime()) && now.isBefore(payload.getDelayTime())) {
                // 未到时间再次发送
                long between = ChronoUnit.HOURS.between(now, payload.getDelayTime());
                int waitTime = Math.min((int)between, delayMaxTime);
                if (waitTime > 0) {
                    // 大于等于1小时的继续发送延时消息
                    mqProducer.sendDelay(MqConstants.INTELLIGENT_DELIVERY_DELAY, payload, Duration.ofHours(waitTime));
                    return ConsumeResult.SUCCESS;
                }
                // 小于1小时再延时对应的秒数
                long seconds = ChronoUnit.SECONDS.between(now, payload.getDelayTime());
                if (seconds > 0) {
                    mqProducer.sendDelay(MqConstants.INTELLIGENT_DELIVERY_DELAY, payload, Duration.ofSeconds(seconds));
                    return ConsumeResult.SUCCESS;
                }
                // 小于1s的消息直接消费
            }
            ExchangeBindingDelayOperationEnum operation =
                ExchangeBindingDelayOperationEnum.getByOperation(payload.getOperation());
            if (operation == null) {
                return ConsumeResult.SUCCESS;
            }
            switch (operation) {
                case EXCHANGE_BINDING_NEXT_NODE -> {
                    // 查询节点是否已经被放行
                    boolean pass = exchangeBindingAppService.nodePass(orderItemId, payload.getBindingFlowNodeId());
                    if (pass) {
                        return ConsumeResult.SUCCESS;
                    }
                    ExchangeBindingNextNodeDTO nextNodeDTO = new ExchangeBindingNextNodeDTO();
                    nextNodeDTO.setOrderItemId(payload.getOrderItemId());
                    exchangeBindingAppService.exchangeBindingNextNode(nextNodeDTO);
                }
                case SUPPORT_EXCHANGE_BINDING_AUDIT_PASS -> {
                    FlowNextReqDTO flowNextReqDTO = new FlowNextReqDTO();
                    flowNextReqDTO.setOrderItemId(payload.getOrderItemId());
                    // 审核节点是否通过
                    boolean pass = exchangeBindingAppService.auditPass(payload.getOrderItemId());
                    if (pass) {
                        return ConsumeResult.SUCCESS;
                    }
                    exchangeBindingAppService.supportExchangeBindingAuditPass(flowNextReqDTO);
                }
            }
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("Failed to consume the message from {} topic ,messageId:{} payload:{}",
                MqConstants.INTELLIGENT_DELIVERY_DELAY, messageView.getMessageId(), payload, e);
            return ConsumeResult.SUCCESS;
        }
    }
}
