package com.pxb7.mall.trade.ofs.adapter.web.contract;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.cola.dto.MultiResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.app.contract.dto.BuyerInfoDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.SellerInfoDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.*;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.*;
import com.pxb7.mall.trade.ofs.app.contract.mapping.ContractDataAppMapping;
import com.pxb7.mall.trade.ofs.app.contract.service.*;
import com.pxb7.mall.trade.ofs.app.contract.v3.service.ImContractProcessAppService;
import com.pxb7.mall.trade.ofs.common.config.util.PageResponseUtil;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractRiskReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractGameRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractTypeRespDTO;

import io.vavr.Tuple2;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 合同-web端
 *
 * @fileName: ContractController.java
 * @description: 合同web控制器
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/8/27 11:42
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@RestController
@RequestMapping("/web/contract")
public class ContractWebController {
    @Resource
    private InfoCardPushAppService infoCardPushAppService;
    @Resource
    private MainContractAppService mainContractAppService;
    @Resource
    private SignatoryAppService signatoryAppService;
    @Resource
    private RiskControlAppService riskControlAppService;
    @Resource
    private InformationCollectAppService informationCollectAppService;
    @Resource
    private ImContractProcessAppService imContractProcessAppService;

    /**
     * 客服发起买家资料收集卡片
     */
    @PostMapping("/send/card/buyer/info")
    public PxResponse<Boolean> sendBuyerInfoCard(@RequestHeader("client_type") Integer clientType,
                                                 @RequestBody @Valid CollectMaterialReqDTO collectMaterialReqDTO) {
        collectMaterialReqDTO.setClientType(clientType);

        return PxResponse.ok(infoCardPushAppService.sendBuyerInfoCard(collectMaterialReqDTO));
    }

    /**
     * 客服发起卖家资料收集卡片
     */
    @PostMapping("/send/card/seller/info")
    public PxResponse<Boolean> sendSellerInfoCard(@RequestHeader("client_type") Integer clientType,
                                                  @RequestBody @Valid CollectMaterialReqDTO collectMaterialReqDTO) {
        collectMaterialReqDTO.setClientType(clientType);
        return PxResponse.ok(infoCardPushAppService.sendSellerInfoCard(collectMaterialReqDTO));
    }

    /**
     * 获取签约人信息
     *
     * @param signatoryReqDTO 签约人请求对象
     * @return signatoryRespDTO 签约人
     */
    @PostMapping("/get/signatory")
    public PxResponse<SignatoryRespDTO> getSignatory(@RequestBody @Validated SignatoryReqDTO signatoryReqDTO) {
        return PxResponse.ok(signatoryAppService.getSignatory(signatoryReqDTO));
    }

    /**
     * 买家提交资料
     *
     * @param buyerInfoReqDTO 买家资料数据传输对象
     * @return Boolean
     */
    @PostMapping("/buyer/info/submit")
    public PxResponse<String> submitBuyerInfo(@RequestHeader("client_type") Integer clientType,
                                              @RequestBody @Validated BuyerInfoReqDTO buyerInfoReqDTO) {
        buyerInfoReqDTO.setClientType(clientType);
        return PxResponse.ok(informationCollectAppService.collectBuyerInformation(buyerInfoReqDTO));
    }

    /**
     * 卖家提交资料
     *
     * @param sellerInfoReqDTO 卖家提交资料数据传输对象
     * @return Boolean
     */
    @PostMapping("/seller/info/submit")
    public PxResponse<String> submitSellerInfo(@RequestHeader("client_type") Integer clientType,
                                               @RequestBody @Validated SellerInfoReqDTO sellerInfoReqDTO) {
        sellerInfoReqDTO.setClientType(clientType);
        return PxResponse.ok(informationCollectAppService.collectSellerInformation(sellerInfoReqDTO));
    }

    /**
     * 买家资料详情
     *
     * @param orderItemId 订单id
     * @return BuyerMaterialInfoRespDTO
     */
    @GetMapping("/get/buyer/materialInfo")
    public PxResponse<BuyerMaterialInfoRespDTO> getBuyerMaterialInfo(@RequestParam("orderItemId") String orderItemId,
                                                                     @RequestParam("gameId") String gameId) {
        return PxResponse.ok(infoCardPushAppService.getBuyerMaterialInfo(orderItemId, gameId));
    }

    /**
     * 卖家资料详情
     *
     * @param orderItemId 订单id
     * @return SellerMaterialInfoRespDTO
     */
    @GetMapping("/get/seller/materialInfo")
    public PxResponse<SellerMaterialInfoRespDTO> getSellerMaterialInfo(@RequestParam("orderItemId") String orderItemId) {
        return PxResponse.ok(infoCardPushAppService.getSellerMaterialInfo(orderItemId));
    }

    /**
     * 创建合同
     *
     * @param contractInitiateReqDTO 合同发起数据传输对象
     * @return String
     */
    @PostMapping("/create")
    public PxResponse<String> createContract(@RequestHeader("client_type") Integer clientType,
                                             @RequestBody @Validated ContractInitiateReqDTO contractInitiateReqDTO) {
        contractInitiateReqDTO.setClientType(clientType);
        return PxResponse.ok(mainContractAppService.initiateContract(contractInitiateReqDTO));
    }

    /**
     * 合同风控
     *
     * @param contractInitiateReqDTO 合同发起数据传输对象
     * @return String
     */
    @PostMapping("/risk")
    public PxResponse<Boolean> risk(@RequestBody ContractRiskReqDTO contractInitiateReqDTO) {
        ContractInitiateReqDTO contractInitiateReq = ContractDataAppMapping.INSTANCE.toContractInitiateReqDTO(contractInitiateReqDTO);
        Tuple2<BuyerInfoDTO, SellerInfoDTO> certInfoRes = mainContractAppService.checkUserCertInfo(contractInitiateReq);
        return PxResponse.ok(riskControlAppService.contractRisk(contractInitiateReqDTO, certInfoRes));
    }

    /**
     * im交易和智能交付商品模块查询合同数据
     *
     * @param automationContractReqDTO im交易和智能交付商品模块查询合同数据请求对象
     * @return AutomationContractRespDTO
     */
    @PostMapping("/query/by/goods")
    public MultiResponse<AutomationContractRespDTO> queryContractData(@RequestBody @Validated AutomationContractReqDTO automationContractReqDTO) {
        return MultiResponse.of(mainContractAppService.searchContractDataByOrderGoods(automationContractReqDTO));
    }

    /**
     * 查询卖家信息-反显订单卖家信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/user/info")
    public PxResponse<UserInfoDTO> userInfo(@RequestParam("userId") String userId) {
        return PxResponse.ok(mainContractAppService.findUserInfoByUserId(userId));
    }

    /**
     * 合同列表
     *
     * @param contractDataReqDTO 合同列表数据请求对象
     * @return PageResponse<ContractDataRespDTO>
     */
    @PostMapping("/list")
    public PxPageResponse<ContractListRespDTO> contractList(@RequestBody ContractDataReqDTO contractDataReqDTO) {
        Page<ContractListRespDTO> page = mainContractAppService.pageQueryContractData(contractDataReqDTO);
        return PageResponseUtil.pageData(page);
    }

    /**
     * 合同详情
     *
     * @param contractDataId 合同id
     * @return ContractInfoRespDTO
     */
    @GetMapping("/detail")
    public PxResponse<ContractDetailRespDTO> contractDetail(@RequestParam("contractDataId") String contractDataId) {
        return PxResponse.ok(mainContractAppService.contractDetail(contractDataId));
    }

    /**
     * 合同详情By订单号
     */
    @GetMapping("/detailByOrderItemId")
    public PxResponse<ContractDetailRespDTO> detailByOrderItemId(@RequestParam("orderItemId") String orderItemId) {
        return PxResponse.ok(mainContractAppService.imContractDetail(orderItemId));
    }

    /**
     * 商家下拉框数据
     *
     * @return List<MerchantListRespDTO>
     */
    @GetMapping("/merchant/baseInfoList")
    public PxResponse<List<MerchantListRespDTO>> merchantList() {
        return PxResponse.ok(mainContractAppService.merchantList());
    }

    /**
     * 发送签署短信
     */
    @PostMapping("/send/sms")
    public PxResponse<Boolean> sendSms(@Validated @RequestBody ContractSendSmsReqDTO reqDTO) {
        return PxResponse.ok(mainContractAppService.sendSms(reqDTO));
    }

    /**
     * 查看示例
     */
    @PostMapping("/view/graph")
    public PxResponse<String> viewGraph(@Validated @RequestBody CheckContractReqDTO reqDTO) {
        return PxResponse.ok(mainContractAppService.viewGraph(reqDTO));
    }

    /**
     * 获取订单信息
     *
     * @param orderItemId 订单id
     * @return
     */
    @GetMapping("/orderInfo")
    public PxResponse<ContractOrderItemDTO> getOrderItemInfo(@RequestParam("orderItemId") String orderItemId) {
        return PxResponse.ok(mainContractAppService.getOrderItemInfo(orderItemId));
    }

    /**
     * 获取交付场景中的合同类型
     *
     * @param contractTypeReqDTO
     * @return
     */
    @PostMapping("/typeInfo")
    public PxResponse<ContractTypeRespDTO> getContractTypeInfo(@Validated @RequestBody ContractTypeReqDTO contractTypeReqDTO) {
        return PxResponse.ok(imContractProcessAppService.getAgreementTypeInfo(contractTypeReqDTO));
    }

    /**
     * 查询我方待签署的合同数量
     *
     * @return
     */
    @GetMapping("/count")
    public PxResponse<Long> contractCount() {
        return PxResponse.ok(mainContractAppService.contractCount());
    }

    /**
     * 获取游戏下拉框数据
     *
     * @return
     */
    @GetMapping("/game/option/list")
    public PxResponse<List<ContractGameRespDTO>> gameOptionList() {
        return PxResponse.ok(mainContractAppService.gameOptionList());
    }

    /**
     * im交易和智能交付合同模块查询订单信息
     *
     * @param orderItemId 订单id
     * @return
     */
    @GetMapping("/imRoomOrderInfo")
    public PxResponse<ImRoomOrderInfoRespDTO> getImRoomOrderInfo(@RequestParam("orderItemId") String orderItemId) {
        return PxResponse.ok(mainContractAppService.getImRoomOrderInfo(orderItemId));
    }
    
    /**
     * 校验合同删除权限
     *
     */
    @PostMapping("/checkDelPerm")
    public PxResponse<Boolean> checkDelPerm(@Validated @RequestBody ContractDelReqDTO param) {
        return PxResponse.ok(mainContractAppService.checkDelPerm(param));
    }

    /**
     * 删除合同
     *
     */
    @PostMapping("/del")
    public PxResponse<Boolean> deleteContract(@Validated @RequestBody ContractDelReqDTO param) {
        return PxResponse.ok(mainContractAppService.deleteContract(param));
    }
 }
