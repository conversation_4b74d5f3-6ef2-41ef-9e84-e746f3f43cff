package com.pxb7.mall.trade.ofs.adapter.dubbo.delivery;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ofs.app.delivery.service.DeliveryFlowAppService;
import com.pxb7.mall.trade.ofs.delivery.client.api.OrderAssistServiceI;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.ChangeRoomReqDTO;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

@DubboService
public class OrderAssistService implements OrderAssistServiceI {

    @Resource
    private DeliveryFlowAppService deliveryFlowAppService;

    //交付流程更换房间
    public SingleResponse<Boolean> changeRoom(ChangeRoomReqDTO changeRoomReqDTO){
        return  deliveryFlowAppService.changeRoom(changeRoomReqDTO);
    }
}
