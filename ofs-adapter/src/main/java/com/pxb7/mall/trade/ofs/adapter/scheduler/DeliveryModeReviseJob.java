package com.pxb7.mall.trade.ofs.adapter.scheduler;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ofs.app.delivery.automation.DeliveryJobAppService;
import com.pxb7.mall.trade.ofs.common.config.util.ActiveUtil;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.OrderItemDeliveryDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.OrderItemDelivery;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.order.OrderInfoGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class DeliveryModeReviseJob implements BasicProcessor {


    @Value("${spring.profiles.active}")
    private String springActive;


    private static final List<String> DATABASE_INSTANCES =
        Arrays.asList("db0", "db1", "db2", "db3", "db4", "db5", "db6", "db7", "db8", "db9", "db10", "db11", "db12",
            "db13", "db14", "db15");

    @Resource
    private DeliveryInfoDbRepository deliveryInfoDbRepository;

    @Resource
    private OrderItemDeliveryDbRepository orderItemDeliveryRepository;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        // 检查当前小时是否在凌晨1点之前 (即0点)
//        // 如果当前时间是0点 (00:00-00:59) 并且是生产/UAT环境，则任务跳过，不在这个时间段执行。
//        // 目的是让任务在凌晨1点之后执行, 8点之前。
//        if (ActiveUtil.isPrdAndUat(springActive)) {
//            if (LocalDateTime.now().getHour() < 1 || LocalDateTime.now().getHour() > 8) {
//                log.info("[DeliveryModeReviseJob] 当前小时: {}, 且环境为生产/UAT. 本次任务跳过.", LocalDateTime.now().getHour());
//                return new ProcessResult();
//            }
//        }
//
//
//        String jobParams = context.getJobParams();
//        log.info("[DeliveryModeReviseJob params]{}", jobParams);
//        JSONObject params = JSONObject.parseObject(jobParams);
//        List<String> orderItemIds = JSONArray.parseArray(params.getString("orderItemId"), String.class);
//
//        if (CollectionUtils.isNotEmpty(orderItemIds)) {
//            LambdaQueryWrapper<DeliveryInfo> qw = new LambdaQueryWrapper<>();
//            qw.in(DeliveryInfo::getOrderItemId, orderItemIds);
//            List<DeliveryInfo> list = deliveryInfoDbRepository.list(qw);
//            if (CollectionUtils.isNotEmpty(list)) {
//                for (DeliveryInfo deliveryInfo : list) {
//                    OrderItemDelivery orderItemDelivery = new OrderItemDelivery();
//                    orderItemDelivery.setDeliveryType(deliveryInfo.getDeliveryType());
//                    orderItemDelivery.setOrderItemId(deliveryInfo.getOrderItemId());
//                    orderItemDeliveryRepository.save(orderItemDelivery);
//                }
//            }
//
//            return new ProcessResult(true);
//        }
//
//
//        LocalDateTime lastReviseTime = LocalDateTime.parse(params.getString("lastReviseTime"), formatter);
//        Integer beforeDays = params.getInteger("beforeDays");
//        LocalDateTime startTime = lastReviseTime.minusDays(beforeDays);
//
//        try {
//            for (String databaseName : DATABASE_INSTANCES) {
//                Long lastId = null;
//                try (HintManager hintManager = HintManager.getInstance()) {
//                    hintManager.setDataSourceName(databaseName);
//                    LambdaQueryWrapper<DeliveryInfo> qw = new LambdaQueryWrapper<>();
//                    qw.between(DeliveryInfo::getCreateTime, startTime, lastReviseTime);
//                    long count = deliveryInfoDbRepository.count(qw);
//                    long executeCount = 0;
//                    while (count > 0 && executeCount < count) {
//                        qw.select(DeliveryInfo::getId, DeliveryInfo::getOrderItemId,DeliveryInfo::getDeliveryType).orderByAsc(DeliveryInfo::getId)
//                            .last("limit 200");
//                        if (lastId != null) {
//                            qw.gt(DeliveryInfo::getId, lastId);
//                        }
//                        List<DeliveryInfo> deliveryInfoList = deliveryInfoDbRepository.list(qw);
//
//                        if (CollectionUtils.isEmpty(deliveryInfoList)) {
//                            break;
//                        }
//
//                        List<OrderItemDelivery> entityList = new ArrayList<>();
//                        for (DeliveryInfo deliveryInfo : deliveryInfoList) {
//                            OrderItemDelivery orderItemDelivery = new OrderItemDelivery();
//                            orderItemDelivery.setDeliveryType(deliveryInfo.getDeliveryType());
//                            orderItemDelivery.setOrderItemId(deliveryInfo.getOrderItemId());
//                            entityList.add(orderItemDelivery);
//                        }
//                        orderItemDeliveryRepository.insertBatch(entityList);
//
//                        lastId = deliveryInfoList.get(deliveryInfoList.size() - 1).getId();
//                        executeCount += deliveryInfoList.size();
//                    }
//
//                }
//                log.info("[数据库执行完成] db:{} lastId:{}", databaseName, lastId);
//            }
//        } catch (Exception e) {
//            log.error("[DeliveryModeReviseJob] error", e);
//            return new ProcessResult(false);
//        }

        return new ProcessResult(true);

    }

}
