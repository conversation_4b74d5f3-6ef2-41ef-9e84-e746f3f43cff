package com.pxb7.mall.trade.ofs.adapter.web.delivery.form;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.adapter.utils.UserUtil;
import com.pxb7.mall.trade.ofs.app.delivery.form.FormViewAppService;
import com.pxb7.mall.trade.ofs.app.delivery.form.models.FormViewReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.model.FormViewRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.submit.form.FormSubmitAppService;
import com.pxb7.mall.trade.ofs.app.delivery.submit.form.models.FormSubmitReqDto;
import com.pxb7.mall.trade.ofs.common.config.models.UserBaseInfo;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 智能交付3.0-c端-表单接口
 */
@Slf4j
@RestController
@RequestMapping("/web/form")
@Validated
public class FormController {

    @Resource
    private FormSubmitAppService formSubmitAppService;


    /**
     * 表单提交
     */
    @PostMapping("/submit")
    public PxResponse<Boolean> submitForm(@RequestHeader("client_type") Integer clientType,
                                       @RequestBody @Valid FormSubmitReqDto param) {

        UserBaseInfo userInfo = UserUtil.getUserInfo();
        param.setLoginUserInfo(userInfo);
        return PxResponse.ok(formSubmitAppService.submitForm(param));
    }

}
