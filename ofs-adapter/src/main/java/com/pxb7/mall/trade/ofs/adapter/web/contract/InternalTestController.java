package com.pxb7.mall.trade.ofs.adapter.web.contract;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractDesensitizationDTO;
import com.pxb7.mall.trade.ofs.app.contract.service.ContractFlowAppService;
import com.pxb7.mall.trade.ofs.app.contract.service.MainContractAppService;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractSignCardReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.InfoSubmitStatusReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.MerchantContractReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.UserContractReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractInfoRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractUserInfoRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.InfoSubmitStatusRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.MerchantContractRespDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/internal/test")
public class InternalTestController {

    @Resource
    private ContractFlowAppService contractFlowAppService;
    @Resource
    private MainContractAppService mainContractAppService;

    @GetMapping("/getContractUserInfoList")
    public PxResponse<ContractUserInfoRespDTO> getContractUserInfoList(@RequestParam String orderItemId,
                                                                       @RequestParam String gameId) {
        ContractUserInfoRespDTO contractUserInfoRespDTO = mainContractAppService.contractUserInfoList(orderItemId, gameId);
        return PxResponse.ok(contractUserInfoRespDTO);
    }

    @GetMapping("/getContractInfo")
    public PxResponse<ContractInfoRespDTO> getContractInfo(@RequestParam String orderItemId) {
        return PxResponse.ok(mainContractAppService.getContractInfo(orderItemId));
    }

    @PostMapping("/sendSignCard")
    public PxResponse<String> sendSignCard(@RequestBody @Validated ContractSignCardReqDTO cardReqDTO) {
        return PxResponse.ok(mainContractAppService.sendContractSignCard(cardReqDTO));
    }

    @PostMapping("/getSignedContractIdAsBuyer")
    public PxResponse<String> getSignedContractIdAsBuyer(@RequestBody @Validated UserContractReqDTO userContractReqDTO) {
        return PxResponse.ok(mainContractAppService.getSignedContractIdAsBuyer(userContractReqDTO));
    }

    @PostMapping("/getLatestCTIdWithMerchantAsBuyer")
    public PxResponse<List<MerchantContractRespDTO>> getLatestCTIdWithMerchantAsBuyer(@RequestBody @Validated MerchantContractReqDTO merchantContractReqDTO) {
        return PxResponse.ok(mainContractAppService.getLatestCTIdWithMerchantAsBuyer(merchantContractReqDTO));
    }

    @PostMapping("/getInfoSubmitStatus")
    public PxResponse<InfoSubmitStatusRespDTO> getInfoSubmitStatus(@RequestBody @Validated InfoSubmitStatusReqDTO infoSubmitStatusReqDTO) {
        return PxResponse.ok(mainContractAppService.notifyInfoSubmitStatus(infoSubmitStatusReqDTO));
    }

    @PostMapping("/contract/desensitization")
    public PxResponse<Boolean> contractDesensitization(@RequestBody @Valid ContractDesensitizationDTO contractDesensitizationDTO) {
        return PxResponse.ok(contractFlowAppService.contractDesensitization(contractDesensitizationDTO));
    }
}
