package com.pxb7.mall.trade.ofs.adapter.wap.delivery;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.adapter.utils.UserUtil;
import com.pxb7.mall.trade.ofs.adapter.web.delivery.card.CardController;
import com.pxb7.mall.trade.ofs.app.delivery.card.CardConfigAppService;
import com.pxb7.mall.trade.ofs.app.delivery.model.CardDropdownRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.model.CombineCardSubmitAndFormDisplayReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.model.CombineCardSubmitAndFormDisplayRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.service.CombineCardAndFormAppService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能交付3.0-卡片列表
 */
@RestController
@RequestMapping("/h5/delivery/card/")
public class CardWapController extends CardController {

}
