package com.pxb7.mall.trade.ofs.adapter.consumer;

import java.util.Objects;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.config.OfsDynamicConfig;
import com.pxb7.mall.trade.ofs.common.config.util.DubboResultAssert;
import com.pxb7.mall.trade.ofs.common.config.util.OptionalUtil;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.enums.order.OrderServiceModeEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ofs.app.contract.v3.service.AgreementAutomationAppService;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.contract.client.dto.model.AutoCreateAgreementDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryStatusChangeDTO;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryTypeEnum;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: DeliveryCompleteNotifyListener.java
 * @description: 交付完成监听
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/3 14:50
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = MqConstants.DELIVERY_STATUS_CHANGE_CONTRACT,
    topic = MqConstants.DELIVERY_STATUS_CHANGE_TOPIC, tag = "*")
public class DeliveryCompleteNotifyListener implements RocketMQListenerExt<DeliveryStatusChangeDTO> {
    
    @Resource
    private OfsDynamicConfig ofsDynamicConfig;

    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    @Resource
    private AgreementAutomationAppService agreementAutomationAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, DeliveryStatusChangeDTO deliveryStatusChangeDTO) {
        log.info("[交付状态变更消息].msgId:{},messageBody:{}", messageView.getMessageId(),
            JSON.toJSONString(deliveryStatusChangeDTO));
        Integer status = deliveryStatusChangeDTO.getStatus();
        if (!Objects.equals(1, status) && !Objects.equals(5, status)) {
            return ConsumeResult.SUCCESS;
        }

        // 交付完成或交付完结
        DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.getByCode(deliveryStatusChangeDTO.getVersion());
        try {
            Integer serviceMode = OptionalUtil.ofBlank(deliveryStatusChangeDTO.getOrderItemId())
                .map(o -> DubboResultAssert.wrapException(() -> orderInfoDubboServiceI.getOrderInfo(deliveryStatusChangeDTO.getOrderItemId()), ErrorCode.RPC_ERROR))
                .map(SingleResponse::getData)
                .map(OrderInfoDubboRespDTO::getServiceMode)
                .orElse(0);
            log.info("[交付状态变更消息].msgId:{},deliveryTypeEnum:{},contractAutoFlag:{},serviceMode:{}", messageView.getMessageId(), JSON.toJSONString(deliveryTypeEnum), ofsDynamicConfig.getContractAutoFlag(), serviceMode);
            if (!Objects.equals(OrderServiceModeEnum.AGENT_SALE.getValue(), serviceMode)) {
                //非代售业务 手动
                agreementAutomationAppService.notifyUpdateImFlowNode(deliveryStatusChangeDTO.getOrderItemId());
            } else {
                if (deliveryTypeEnum == DeliveryTypeEnum.ORDINARY) {
                    // 普通交付 手动
                    agreementAutomationAppService.notifyUpdateImFlowNode(deliveryStatusChangeDTO.getOrderItemId());
                } else if (deliveryTypeEnum == DeliveryTypeEnum.AUTOMATION || deliveryTypeEnum == DeliveryTypeEnum.AUTOMATION_V3) {
                    // 智能交付2.0 或 3.0
                    if (Objects.equals(0, ofsDynamicConfig.getContractAutoFlag())) {
                        // 手动
                        agreementAutomationAppService.notifyUpdateImFlowNode(deliveryStatusChangeDTO.getOrderItemId());
                    } else {
                        // 自动
                        AutoCreateAgreementDTO autoCreateAgreementDTO = AutoCreateAgreementDTO.builder()
                            .orderItemId(deliveryStatusChangeDTO.getOrderItemId())
                            .operatorId(deliveryStatusChangeDTO.getOperatorId())
                            .build();
                        agreementAutomationAppService.autoCreateAgreement(autoCreateAgreementDTO);
                    }
                } else {
                    // 手动
                    agreementAutomationAppService.notifyUpdateImFlowNode(deliveryStatusChangeDTO.getOrderItemId());
                }
            }
            return ConsumeResult.SUCCESS;
        } catch (BusinessException e) {
            log.error("delivery complete notify contract exception, message:{} ",
                JSON.toJSONString(deliveryStatusChangeDTO), e);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("delivery complete notify contract exception, message:{} ",
                JSON.toJSONString(deliveryStatusChangeDTO), e);
            return ConsumeResult.FAILURE;
        }
    }

    // @Override
    // public ConsumeResult consume(MessageView messageView, DeliveryCompleteNotifyMessage message) {
    // log.info("Received a message that a contract will be automatically created upon completion of delivery.
    // messageBody: {}, msgId: {}", message, Optional.ofNullable(messageView)
    // .map(MessageView::getMessageId)
    // .map(MessageId::toString)
    // .orElse(""));
    //
    // if (StringUtils.isBlank(message.getOrderItemId())) {
    // log.error("Received a message that a contract will be automatically created upon completion of delivery. input
    // parameter: {} missing! ", message);
    // return ConsumeResult.SUCCESS;
    // }
    // try {
    // agreementAutomationAppService.notifyUpdateImFlowNode(message.getOrderItemId());
    // } catch (BizException e) {
    // log.error("notify update im room data exception, orderItemId:{}", message.getOrderItemId(), e);
    // return ConsumeResult.SUCCESS;
    // }
    // return ConsumeResult.SUCCESS;
    //
    // AutoCreateAgreementDTO autoCreateAgreementDTO = AutoCreateAgreementDTO.builder()
    // .orderItemId(message.getOrderItemId())
    // .operatorId(message.getOperatorId())
    // .flowId(message.getFlowId())
    // .label(message.getLabel())
    // .build();
    //
    // try {
    // agreementAutomationAppService.autoCreateAgreement(autoCreateAgreementDTO);
    // return ConsumeResult.SUCCESS;
    // } catch (BusinessException e) {
    // log.error("delivery complete notify contract exception, message:{} ", message, e);
    // return ConsumeResult.SUCCESS;
    // } catch (Exception e) {
    // log.error("delivery complete notify contract exception, message:{} ", message, e);
    // return ConsumeResult.FAILURE;
    // }
    // }
}
