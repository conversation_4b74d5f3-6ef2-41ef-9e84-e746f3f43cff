package com.pxb7.mall.trade.ofs.adapter.inner;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.InitDeliveryDataReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/inner/deliveryData")
@Validated
public class DeliveryDataInnerController {

    @Resource
    private DeliveryDataDomainService deliveryDataDomainService;

    @PostMapping("/pressure")
    public PxResponse<Boolean> pressure(@RequestBody InitDeliveryDataReqBO reqBO) {
        if (Objects.isNull(reqBO) || StringUtils.isBlank(reqBO.getOrderItemId()) || StringUtils.isBlank(
            reqBO.getGameId()) || StringUtils.isBlank(reqBO.getBuyerUserId()) || StringUtils.isBlank(
            reqBO.getSellerUserId()) || StringUtils.isBlank(reqBO.getProductId())) {
            return PxResponse.fail("缺少参数");
        }
        // 初始化数据字典
        deliveryDataDomainService.initDeliveryData(reqBO);
        return PxResponse.ok(true);
    }
}
