package com.pxb7.mall.trade.ofs.adapter.web.delivery.form;


import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.app.delivery.service.FormLogAppService;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.FormLogInfoRespDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.FormLogInfoReqDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能交付3.0-c端-表单接口
 */
@Slf4j
@RestController
@RequestMapping("/web/form/log")
@Validated
public class FormLogController {

    @Resource
    private FormLogAppService formLogAppService;


    /**
     * 表单日志获取
     */
    @PostMapping("/get")
    public PxResponse<FormLogInfoRespDTO> get(@RequestBody @Valid FormLogInfoReqDTO param) {
        return PxResponse.ok(formLogAppService.findByIdAndFormConfigId(param));
    }

}
