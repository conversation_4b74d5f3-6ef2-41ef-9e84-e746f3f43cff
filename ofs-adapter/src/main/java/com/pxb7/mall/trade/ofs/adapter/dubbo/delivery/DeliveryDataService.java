package com.pxb7.mall.trade.ofs.adapter.dubbo.delivery;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.trade.ofs.app.delivery.service.DeliveryDataAppService;
import com.pxb7.mall.trade.ofs.delivery.client.api.DeliveryDataServiceI;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.DeliveryDataReqDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.FaqDeliveryDataReqDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.response.DeliveryDataDetailDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Collections;
import java.util.Objects;

@DubboService
public class DeliveryDataService implements DeliveryDataServiceI {

    @Resource
    private DeliveryDataAppService deliveryDataAppService;

    @Override
    public MultiResponse<DeliveryDataDetailDTO> queryFaqDeliveryData(FaqDeliveryDataReqDTO reqDTO) {
        if (Objects.isNull(reqDTO) || StringUtils.isBlank(reqDTO.getOrderItemId())) {
            return MultiResponse.of(Collections.emptyList());
        }
        return deliveryDataAppService.queryFaqDeliveryData(reqDTO);
    }

    @Override
    public MultiResponse<DeliveryDataDetailDTO> queryDeliveryDataByCodes(DeliveryDataReqDTO reqDTO) {
        if (Objects.isNull(reqDTO) || StringUtils.isBlank(reqDTO.getOrderItemId())) {
            return MultiResponse.of(Collections.emptyList());
        }
        return deliveryDataAppService.queryDeliveryDataByCodes(reqDTO);
    }
}
