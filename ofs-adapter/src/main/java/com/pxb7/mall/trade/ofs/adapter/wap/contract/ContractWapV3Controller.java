package com.pxb7.mall.trade.ofs.adapter.wap.contract;

import com.pxb7.mall.trade.ofs.adapter.web.contract.ContractWebV3Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 合同v3-h5
 *
 * @fileName: ContractWapV3Controller.java
 * @description: 合同h5 新版本http请求入口
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/2/17 10:15
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@RestController
@RequestMapping("/h5/contract/v3/")
public class ContractWapV3Controller extends ContractWebV3Controller {

}
