package com.pxb7.mall.trade.ofs.adapter.wap.delivery;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.app.delivery.automation.v2.ProcessInsAppService;
import com.pxb7.mall.trade.ofs.delivery.client.dto.response.DeliveryNodeDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.response.DeliveryTreeDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * wap-交付
 */
@RestController
@RequestMapping("/h5/delivery")
public class DeliveryH5Controller {

    @Resource
    private ProcessInsAppService processInsAppService;

    /**
     * 用户端-交付流程节点树
     *
     * @param orderItemId 订单ID
     * @return
     */
    @GetMapping("/node/tree")
    public PxResponse<DeliveryTreeDTO> getNodeTree(@RequestParam("orderItemId") String orderItemId) {
        return PxResponse.ok(processInsAppService.getProcessInsNode(orderItemId));
    }

}
