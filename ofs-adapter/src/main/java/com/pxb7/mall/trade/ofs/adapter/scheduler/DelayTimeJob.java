package com.pxb7.mall.trade.ofs.adapter.scheduler;

import org.springframework.stereotype.Component;

import com.pxb7.mall.trade.ofs.app.delivery.service.CardDelayTimeLogAppService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

/**
 * 延迟任务
 *
 * <AUTHOR>
 * @date 2025/04/09 16:50
 **/
@Slf4j
@Component
public class DelayTimeJob implements BasicProcessor {

    @Resource
    private CardDelayTimeLogAppService cardDelayTimeLogAppService;

    @Override
    public ProcessResult process(TaskContext context) {
        log.info("DelayTimeJob start");
        // 定时任务每30min 执行一次 如果需要修改时间，请修改定时任务配置及sql
        try {
            cardDelayTimeLogAppService.executeDelayTime();
            log.info("DelayTimeJob end");
            return new ProcessResult(true, "定时任务执行成功");
        } catch (Exception e) {
            log.error("定时任务DelayTimeJob执行失败", e);
            return new ProcessResult(false, "定时任务执行失败" + e.getMessage());
        }
    }
}
