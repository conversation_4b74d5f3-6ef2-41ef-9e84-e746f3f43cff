package com.pxb7.mall.trade.ofs.common.config.enums;

/**
 * 法大大api
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
public enum FddApiEnums {
    REGISTER_ACCOUNT("account_register.api", "注册账号"), GET_PERSON_VERIFY_URL("get_person_verify_url.api", "获取个人实名地址"),
    GET_COMPANY_VERIFY_URL("get_company_verify_url.api", "获取企业实名认证地址"),
    FIND_PERSON_CERT_INFO("find_personCertInfo.api", "查询个人实名认证信息"),
    FIND_COMPANY_CERT_INFO("find_companyCertInfo.api", "查询企业实名认证信息"), APPLY_CERT("apply_cert.api", "绑定实名信息"),
    GENERATE_CONTRACT("generate_contract.api", "模板填充"), UPLOAD_TEMPLATE("uploadtemplate.api", "模板上传"),MERGE_CONTRACT("merge_contract.api", "合并合同"),
    UPLOAD_DOCS("uploaddocs.api", "合同上传"), FILL_PAGE("fill_page.api", "合同填充页面接口"), EXT_SIGN("extsign.api", "手动签署"),
    PERSON_VERIFY_SIGN("person_verify_sign.api", "快捷签署接口（个人）"), BEFORE_AUTH_SIGN("before_authsign.api", "获取授权自动签页面接口"),
    GET_AUTH_STATUS("get_auth_status.api", "查询授权自动签状态接口"), PUSH_SHORT_URL_SMS("push_short_url_sms.api", "自定义短信发送短链接口"),
    CANCEL_EXT_SIGN_AUTO_PAGE("cancel_extsign_auto_page.api", "取消授权签协议接口"), EXT_SIGN_AUTO("extsign_auto.api", "自动签署"),
    CONTRACT_FILING("contractFiling.api", "合同归档"), VIEW_CONTRACT("viewContract.api", "合同查看"),
    DOWNLOAD_CONTRACT("downLoadContract.api", "合同下载"), GET_URL("geturl.api", "获取查看下载地址");

    private String api;
    private String desc;

    FddApiEnums(String api, String desc) {
        this.api = api;
        this.desc = desc;
    }

    public String getApi() {
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
