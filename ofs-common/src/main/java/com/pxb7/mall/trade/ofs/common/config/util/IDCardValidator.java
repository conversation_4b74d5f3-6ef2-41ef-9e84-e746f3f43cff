package com.pxb7.mall.trade.ofs.common.config.util;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: IDCardValidator.java
 * @description: 身份证信息验证
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/12/16 10:18
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
public class IDCardValidator {
    public static boolean isAdult(String idCard) {
        // 检查身份证号码长度是否为18位
        if (StrUtil.isBlank(idCard)) {
            throw new BusinessException(ErrorCode.ID_CARD_IS_EMPTY);
        }
        if (idCard.length() != 18) {
            throw new BusinessException(ErrorCode.ID_CARD_LEN_LESS_THAN_18);
        }
        // 提取出生日期部分
        String birthDateStr = idCard.substring(6, 14);
        // 使用DateTimeFormatter解析出生日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        try {
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate currentDate = LocalDate.now();
            // 计算年龄
            long years = ChronoUnit.YEARS.between(birthDate, currentDate);
            // 判断是否成年
            return years >= 18;
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.ID_CARD_PARSE_FAIL);
        }
    }
}
