package com.pxb7.mall.trade.ofs.common.config.exception.ensure.extensions;

import com.pxb7.mall.auth.c.util.NumberUtil;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.exception.ExceptionFactory;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:10
 **/
public class EnsureParamIntegerExtension extends EnsureParamObjectExtension<Integer> {

    private final Integer condition;

    public EnsureParamIntegerExtension(Integer condition) {
        super(condition);
        this.condition = condition;
    }

    /**
     * 断言数字 >0
     */
    public EnsureParamIntegerExtension isPositive(ErrorCode errorCode) {
        if (NumberUtil.isNotPositive(this.condition)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrCode());
        } else {
            return this;
        }
    }

    /**
     * 断言数字为 null || <=0
     */
    public EnsureParamIntegerExtension isNotPositive(ErrorCode errorCode) {
        if (NumberUtil.isPositive(this.condition)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

}
