package com.pxb7.mall.trade.ofs.common.config.exception.ensure.extensions;

import org.apache.commons.lang3.StringUtils;

import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.exception.ExceptionFactory;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:12
 **/
public class EnsureParamStringExtension extends EnsureParamObjectExtension<String> {
    private final String string;

    public EnsureParamStringExtension(String string) {
        super(string);
        this.string = string;
    }

    public EnsureParamStringExtension isNotBlank(ErrorCode errorCode) {
        if (StringUtils.isBlank(this.string)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

    public EnsureParamStringExtension isBlank(ErrorCode errorCode) {
        if (StringUtils.isNotBlank(this.string)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }
}
