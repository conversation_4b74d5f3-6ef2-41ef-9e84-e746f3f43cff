package com.pxb7.mall.trade.ofs.common.config.handler;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: BusinessException.java
 * @description: 业务异常统一处理
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/8/31 13:43
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
public class BusinessException extends BizException {
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getErrCode(), errorCode.getErrDesc());
    }
    public BusinessException(String msg) {
        super("error", msg);
    }
}
