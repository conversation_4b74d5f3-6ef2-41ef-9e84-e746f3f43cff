package com.pxb7.mall.trade.ofs.common.config.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025/06/26 16:41
 **/
@Component
@Slf4j
public class OfsDynamicConfig extends AbstractAppConfig {

    private static final String DATA_ID = "ofs-dynamic.yaml";
    private static final String GROUP_ID = "DEFAULT_GROUP";
    private Properties properties = new Properties();
    @Override
    protected String getDataId() {
        return DATA_ID;
    }

    @Override
    protected String getGroupId() {
        return GROUP_ID;
    }

    @Override
    protected void loadProperties(Properties properties) {
        log.info("DATA_ID:{},GROUP_ID:{},Nacos config changed，config is: {}", DATA_ID, GROUP_ID, properties);
        this.properties = properties;
    }

    public Integer getContractAutoFlag() {
        try {
            String property = this.properties.getProperty("contract.auto.flag");
            log.info("contract.auto.flag:{}", property);
            if (StrUtil.isBlank(property)) {
                return 0;
            }
            return Integer.parseInt(property);
        } catch (Exception e) {
            return 0;
        }
    }

}
