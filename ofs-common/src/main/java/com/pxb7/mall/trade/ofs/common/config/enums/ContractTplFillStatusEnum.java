package com.pxb7.mall.trade.ofs.common.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractTplFillStatusEnum.java
 * @description: 合同模板填充返回状枚举
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/2 20:34
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@AllArgsConstructor
@Getter
public enum ContractTplFillStatusEnum {

    SUCCESS("1000", "操作成功"),

    ILLEGAL_PARAMETER("2001", "参数缺失或者不合法"),

    BUSINESS_EXCEPTION("2002", "业务异常,失败原因见msg"), OTHER_ERROR("2003", "其他错误，请联系法大大"),
    ;
    private final String code;
    private final String msg;

}
