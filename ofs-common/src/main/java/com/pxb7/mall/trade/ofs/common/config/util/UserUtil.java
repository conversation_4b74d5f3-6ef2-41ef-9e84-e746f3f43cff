package com.pxb7.mall.trade.ofs.common.config.util;

import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.auth.dto.ImUserDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网 用户工具类
 *
 * <AUTHOR>
 * @date 2025/2/19 下午4:32
 */
public class UserUtil {

    /**
     * 获取登录用户id
     *
     * @return 用户id
     */
    public static String getUserId() {
        String loginUserId = LoginUserUtil.getUserId();
        if (StringUtils.isNotBlank(loginUserId)) {
            return loginUserId;
        }
        String merchantUserId = MerchantUserUtil.getUserId();
        if (StringUtils.isNotBlank(merchantUserId)) {
            return merchantUserId;
        }
        String imUserId = ImUserUtil.getUserId();
        if (StringUtils.isNotBlank(imUserId)) {
            return imUserId;
        }
        return "";
        //        throw NotLoginException.newInstance(StpUserUtil.getLoginType(), NOT_TOKEN, NOT_TOKEN_MESSAGE, null)
        //            .setCode(SaErrorCode.CODE_11011);
    }

    public static String getUserName() {
        String loginUserName = LoginUserUtil.getUserName();
        if (StringUtils.isNotBlank(loginUserName)) {
            return loginUserName;
        }
        String merchantUserName = MerchantUserUtil.getUserName();
        if (StringUtils.isNotBlank(merchantUserName)) {
            return merchantUserName;
        }
        return ImUserUtil.getUserName();
    }

    /**
     * 是否是客服 (不区分 官方/号商客服)
     *
     * @return
     */
    public static Boolean isCustomerCareByToken() {
        ImUserDTO imUser = ImUserUtil.getImUser();
        if (Objects.isNull(imUser)) {
            return false;
        }
        if (imUser.getUserType() != 1) {
            return false;
        }
        // 不区分 官方/号商 客服
        return true;
    }

    /**
     * 获取 客服昵称 - 优先获取昵称，若昵称没有则获取用户名
     */
    public static String getImUserNickName() {
        String name = ImUserUtil.getNickName();
        if (StringUtils.isNotBlank(name)) {
            return name;
        }
        return ImUserUtil.getUserName();
    }

    public static Boolean isMerchant() {
        return StringUtils.isNotBlank(MerchantUserUtil.getMerchantId());
    }

    public static String getMerchantId() {
        return MerchantUserUtil.getMerchantId();
    }

}
