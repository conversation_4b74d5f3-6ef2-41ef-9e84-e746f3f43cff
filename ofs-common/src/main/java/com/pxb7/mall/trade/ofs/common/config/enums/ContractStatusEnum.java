package com.pxb7.mall.trade.ofs.common.config.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractStatusEnum.java
 * @description: 合同状态枚举
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/10/15 16:20
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum {

    PREPARING(0, "准备中"),
    WAIT_SIGN(1, "待签署"),
    SIGNING(2, "签署中"),
    FINISHED(3, "已完成"),
    CANCELED(4, "已取消"),
    TO_BE_INITIATED(5, "待发起"),
    FROZEN(6, "冻结"),
    ;
    private final Integer code;
    private final String label;

    public static ContractStatusEnum getStatusEnumByCode(Integer code) {
        for (ContractStatusEnum statusEnum : ContractStatusEnum.values()) {
            if (statusEnum.code.equals(code)) {
                return statusEnum;
            }
        }
        return ContractStatusEnum.PREPARING;
    }
}
