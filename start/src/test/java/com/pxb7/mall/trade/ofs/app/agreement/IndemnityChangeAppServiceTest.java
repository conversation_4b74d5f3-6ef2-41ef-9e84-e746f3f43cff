package com.pxb7.mall.trade.ofs.app.agreement;

import com.pxb7.mall.trade.ofs.Application;
import com.pxb7.mall.trade.ofs.app.contract.v3.service.AgreementAutomationAppService;
import com.pxb7.mall.trade.ofs.app.contract.v3.service.IndemnityChangeAppService;
import com.pxb7.mall.trade.ofs.contract.client.dto.model.AutoCreateAgreementDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: IndemnityChangeAppServiceTest.java
 * @description: 包赔变更测试
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/5/13 14:30
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Slf4j
@SpringBootTest(classes = Application.class)
public class IndemnityChangeAppServiceTest {

    @Resource
    private IndemnityChangeAppService indemnityChangeAppService;
    @Resource
    private AgreementAutomationAppService agreementAutomationAppService;

    @Test
    public void generateNewIndemnityFile() {
        indemnityChangeAppService.generateNewIndemnityFile("ZH15069238272000020061", "141846571819102");
    }

    @Test
    public void notifyUpdateImFlowNode(){
        AutoCreateAgreementDTO auto = AutoCreateAgreementDTO.builder()
            .orderItemId("ZH15906590202271824193")
            .operatorId("0")
            .build();
        agreementAutomationAppService.autoCreateAgreement(auto);
    }
}
