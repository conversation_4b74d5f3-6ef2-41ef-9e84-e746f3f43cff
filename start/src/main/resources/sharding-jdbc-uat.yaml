dataSources:
  trade_db:
    url: **********************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 300
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  trade_db_slave:
    url: ***************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 100
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db0:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave0:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db1:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave1:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db2:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave2:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db3:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave3:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db4:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave4:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db5:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave5:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db6:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave6:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db7:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave7:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db8:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave8:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db9:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave9:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db10:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave10:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db11:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave11:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db12:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave12:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db13:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave13:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db14:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave14:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db15:
    url: *************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db_slave15:
    url: ******************************************************************************************************************************************************************************************
    username: pxb7_trade
    password: pxb7_test#cLtxR##m
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000


mode:
  type: Standalone

rules:
  - !SINGLE
    tables:
      - "trade_db.*"
  - !SHARDING
    tables:
      # 普通交付
      delivery_ordinary:
        actualDataNodes: db$->{0..15}.delivery_ordinary
      # 智能交付流程
      delivery_automation_flow:
        actualDataNodes: db$->{0..15}.delivery_automation_flow
        databaseStrategy:
          standard:
            shardingColumn: order_item_id
            shardingAlgorithmName: delivery_flow_inline
      # 智能交付流程日志
      delivery_automation_flow_node_log:
        actualDataNodes: db$->{0..15}.delivery_automation_flow_node_log
        databaseStrategy:
          standard:
            shardingColumn: order_item_id
            shardingAlgorithmName: delivery_flow_inline
      # 交付信息表
      delivery_info:
        actualDataNodes: db$->{0..15}.delivery_info
        databaseStrategy:
          standard:
            shardingColumn: order_item_id
            shardingAlgorithmName: order_item_inline
      # order_item
      order_item:
        actualDataNodes: db$->{0..15}.order_item
        databaseStrategy:
          standard:
            shardingColumn: order_item_id
            shardingAlgorithmName: order_item_inline
      order_item_delivery:
        actualDataNodes: db$->{0..15}.order_item_delivery
        databaseStrategy:
          standard:
            shardingColumn: order_item_id
            shardingAlgorithmName: order_item_inline
    defaultDatabaseStrategy:
      standard: #  standard 标准、complex 复合、HINT
        shardingColumn: order_item_id
        shardingAlgorithmName: order_item_inline
    defaultAuditStrategy: # 默认审计策略
      auditorNames:
        - sharding_key_required_auditor
      allowHintDisable: true
    auditors:
      sharding_key_required_auditor:
        type: CUSTOM_SHARDING_AUDIT_ALGORITHM
    #    bindingTables:
    #      - order_item, ext_voucher,ext_voucher_detail, order_item_extend, order_item_fee, order_item_indemnity, order_item_promotion, order_item_sincerity_sell_service,order_deliver_node,order_item_payment
    #      - main_order,receipt_voucher,receipt_voucher_detail
    #      - payment, pay_log, pay_transfer
    shardingAlgorithms:
      order_item_inline:
        type: INLINE
        props:
          algorithm-expression: db$->{Long.parseLong(order_item_id[-4..-1]) % 16} # 分片算法表达式
          allow-range-query-with-inline-sharding: true # 允许范围查询
      delivery_flow_inline:
        type: INLINE
        props:
          algorithm-expression: db$->{Long.parseLong(order_item_id[-4..-1]) % 16} # 分片算法表达式
          allow-range-query-with-inline-sharding: true # 允许范围查询

# 文档: 用户手册, 通用配置, 属性配置
props:
  sql-show: false