ALTER TABLE `contract_buyer_info`
    ADD `msg_uid` varchar(64) DEFAULT '' NOT NULL COMMENT 'IM(融云)卡片消息ID' AFTER `cert_no`;
ALTER TABLE `contract_seller_info`
    ADD `msg_uid` varchar(64) DEFAULT '' NOT NULL COMMENT 'IM(融云)卡片消息ID' AFTER `cert_back_img`;


DROP TABLE IF EXISTS `contract_material_snapshot`;
CREATE TABLE `contract_material_snapshot`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_item_id`    varchar(64) NOT NULL DEFAULT '' COMMENT '子订单id',
    `user_id`          varchar(64) NOT NULL DEFAULT '' COMMENT '用户id(买家｜卖家)',
    `user_type`        smallint unsigned NOT NULL DEFAULT '0' COMMENT '用户类型 1：买家 2：卖家',
    `msg_uid`          varchar(64) NOT NULL DEFAULT '' COMMENT 'IM(融云)卡片消息ID',
    `delivery_room_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '交付群聊房间id(智能交付是新群交付, 中介订单是原群交付)',
    `rand`             int unsigned NOT NULL DEFAULT '0' COMMENT '随机数',
    `customer_id`      varchar(64) NOT NULL DEFAULT '' COMMENT '客服id',
    `phone`            varchar(32) NOT NULL DEFAULT '' COMMENT '资料收集方手机号',
    `create_user_id`   varchar(64) NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`      datetime(3) DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`   varchar(64)          DEFAULT '' COMMENT '更新人id',
    `update_time`      datetime(3) DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`),
    KEY                `idx_order_item_id` (`order_item_id`) USING BTREE,
    KEY                `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB COMMENT='合同资料预提交快照信息';