package com.pxb7.mall.trade.ofs.app.contract.mapping;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.common.client.request.risk.riskLog.ReqSaveRiskLogDTO;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantBaseInfoRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractDesensitizationDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractFileDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractIndemnityDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractSignInfoDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.AutomationContractReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.CollectMaterialReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.ContractDataReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.ContractInitiateReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.ContractTypeReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.AutomationContractRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.ContractDataRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.ContractDetailRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.ContractListRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.MerchantListRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.UserInfoDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.CollectInformationReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractDeleteCardReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractFinishCardReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractFreezeCardReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractRiskReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractSignCardReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.ContractUrgeMsgReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.DeliveryContractTypeReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.MerchantContractReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.UrgeSignCardReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.UserContractReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractInfoRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractRiskRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractUserInfoRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.InfoSubmitStatusRespDTO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContactExtendInfoBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractDeleteCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractDesensitizationBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractFinishCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractFreezeCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractIndemnityBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractInitiateBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractInitiateReqBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractMainInfoBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractSignCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractUrgeCardBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractUrgeMsgBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractUserInfoBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.InfoSubmitStatusBO;
import com.pxb7.mall.trade.ofs.contract.domain.model.SearchContractParamBO;
import com.pxb7.mall.trade.ofs.contract.infra.model.ContractByGoodsLogReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractFile;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractSignInfo;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.MerchantContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.UserContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request.ContractDataReqPO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.DeliveryInfoReqDTO;
import com.pxb7.mall.trade.order.client.dto.response.indemnity.ContractIndemnityInfoDTO;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractDataAppMapping.java
 * @description: 合同数据映射
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/8/30 19:29
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Mapper
public interface ContractDataAppMapping {
    ContractDataAppMapping INSTANCE = Mappers.getMapper(ContractDataAppMapping.class);

    @Mapping(source = "accountUserId", target = "subAccountUserId")
    ContractDataReqPO transformContractDataReqDTO(ContractDataReqDTO contractDataReqDTO);

    @Mapping(source = "productUniqueNo", target = "productId")
    ContactExtendInfoBO transformContractExtendInfo(ContractInitiateReqDTO contractInitiateReqDTO);

    List<ContractIndemnityBO> transformContractIndemnityList(List<ContractIndemnityDTO> contractInitiateReqDTO);

    ContractInitiateBO transformContractInitiateReqDTO(ContractInitiateReqDTO contractInitiateReqDTO);

    @Mapping(target = "gameAccount", source = "passesNo")
    ContractDataRespDTO toRespDTO(ContractData entity);

    @Mapping(source = "createTime", target = "startTime")
    ContractFileDTO toFileDto(ContractFile file);

    ContractSignInfoDTO toSignInfoDto(ContractSignInfo signInfo);

    SearchContractParamBO transSearchContractParam(AutomationContractReqDTO automationContractReqDTO);

    List<AutomationContractRespDTO> transContractDeliveryData(List<ContractData> list);

    ContractByGoodsLogReqPO.AddPO transSearchContractLog(AutomationContractReqDTO automationContractReqDTO);

    ReqSaveRiskLogDTO transSearchRisk(ContractRiskReqDTO reqDTO);

    ContractUserInfoRespDTO toUserInfoRespDTO(ContractUserInfoBO contractUserInfoBO);

    UserInfoDTO RpcToUserInfoRespDTO(UserShortInfoDTO contractUserInfoBOS);

    @Mapping(source = "productUniqueNo", target = "productUniqueNo", qualifiedByName = "trimString")
    @Mapping(source = "orderItemId", target = "orderItemId", qualifiedByName = "trimString")
    @Mapping(source = "passesNo", target = "passesNo", qualifiedByName = "trimString")
    ContractInitiateReqBO toTransmitDto(ContractInitiateReqDTO contractInitiateReqDTO);

    ContractFinishCardBO toReqDTO(ContractFinishCardReqDTO reqDTO);

    ContractDeleteCardBO toDeleteBO(ContractDeleteCardReqDTO reqDTO);

    @Mapping(source = "deliveryRoomId", target = "roomId")
    ContractSignCardBO toSignBO(ContractSignCardReqDTO reqDTO);

    ContractFreezeCardBO toFreezeBO(ContractFreezeCardReqDTO reqDTO);

    @Mapping(source = "contractDataId", target = "contractId")
    @Mapping(source = "status", target = "contractStatus")
    ContractInfoRespDTO toContractInfoRespDTO(ContractData contractData);

    @Mapping(source = "productUniqueNo", target = "productUniqueNo", qualifiedByName = "trimString")
    @Mapping(source = "orderItemId", target = "orderItemId", qualifiedByName = "trimString")
    @Mapping(source = "passesNo", target = "passesNo", qualifiedByName = "trimString")
    ContractRiskReqDTO transformContractRiskReqDTO(ContractInitiateReqDTO contractInitiateReqDTO);

    CollectMaterialReqDTO toCollectMaterialReqDTO(CollectInformationReqDTO collectInformationReqDTO);

    UserContractReqPO toUserContractReqPO(UserContractReqDTO userContractReqDTO);

    ContractTypeReqDTO toContractTypeReqDTO(DeliveryContractTypeReqDTO deliveryContractTypeReqDTO);

    @Mapping(source = "customerUserId", target = "customerId")
    @Mapping(source = "productType", target = "businessType")
    @Mapping(source = "buyerType", target = "buyerUserType")
    @Mapping(source = "sellerType", target = "sellerUserType")
    @Mapping(source = "productUniqueNo", target = "productUniqueNo", qualifiedByName = "trimString")
    @Mapping(source = "orderItemId", target = "orderItemId", qualifiedByName = "trimString")
    @Mapping(source = "passesNo", target = "passesNo", qualifiedByName = "trimString")
    ContractMainInfoBO toMainBO(ContractInitiateReqDTO contractInitiateReqDTO);

    @Mapping(source = "passesNo", target = "gameAccount")
    ContractListRespDTO toContractListRespDTO(ContractData contractData);

    @Mapping(source = "passesNo", target = "gameAccount")
    ContractDetailRespDTO toContractDetailRespDTO(ContractData contractData);

    List<MerchantListRespDTO> toMerchantListData(List<MerchantBaseInfoRespDTO> merchantBaseInfoRespDTOList);

    ContractUrgeCardBO toUrgeBO(UrgeSignCardReqDTO reqDTO);

    InfoSubmitStatusRespDTO toInfoSubmitStatusDTO(InfoSubmitStatusBO infoSubmitStatusBO);

    MerchantContractReqPO toMerchantContractReqPO(MerchantContractReqDTO merchantContractReqDTO);

    List<ContractRiskRespDTO> toContractRisk(List<ContractData> contractData);

    ContractUrgeMsgBO toUrgeMsgBO(ContractUrgeMsgReqDTO reqDTO);

    @Mapping(source = "passesNo", target = "accountUid", qualifiedByName = "trimString")
    @Mapping(source = "guarantee", target = "repackage")
    @Mapping(source = "orderItemId", target = "orderItemId", qualifiedByName = "trimString")
    DeliveryInfoReqDTO transformDeliveryInfoReqDTO(ContractInitiateReqDTO contractInitiateReqDTO);

    ContractInitiateReqDTO toContractInitiateReqDTO(ContractRiskReqDTO contractInitiateReqDTO);

    ContractIndemnityBO toContractIndemnityBO(ContractIndemnityDTO normalIndemnity);

    ContractIndemnityDTO toContractIndemnityDTO(ContractIndemnityInfoDTO item);

    @Named("trimString")
    default String trimString(String str) {
        return StrUtil.isNotBlank(str) ? str.trim() : "";
    }

    ContractDesensitizationBO transformContractDesensitizationDTO(ContractDesensitizationDTO contractDesensitizationDTO);
}
