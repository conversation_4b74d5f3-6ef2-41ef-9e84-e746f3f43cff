package com.pxb7.mall.trade.ofs.app.delivery.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 组件表定义(DeliveryComponentsConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:53:16
 */
public class DeliveryComponentsConfigRespDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private String componentName;
        private Integer componentTypeLevel1;
        private Integer componentTypeLevel2;
        private String componentDesc;
        private String componentStyleJson;
        private String dataConfig;
        private Integer componentUseCase;
        private Integer sortValue;
    }
}
