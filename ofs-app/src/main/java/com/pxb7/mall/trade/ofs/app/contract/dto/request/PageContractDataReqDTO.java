package com.pxb7.mall.trade.ofs.app.contract.dto.request;

import com.pxb7.mall.trade.ofs.contract.client.PageDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同-合同数据(ContractData)实体类
 *
 * <AUTHOR>
 * @since 2024-07-09 10:03:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageContractDataReqDTO extends PageDTO {

    /**
     * 游戏账号||订单编号
     */
    private String searchNum;

    /**
     * 合同状态1我方待签署2对方待签署3准备中4已完成5已取消
     */
    private Integer status;
    /**
     * 游戏ID
     */
    private String gameId;

    /**
     * 身份类型1买家2卖家
     */
    private Integer identityType;

}
