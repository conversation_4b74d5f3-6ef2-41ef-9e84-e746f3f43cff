package com.pxb7.mall.trade.ofs.app.delivery.dto.response;

import com.pxb7.mall.trade.ofs.delivery.domain.model.request.SaveFlowNodeLogBO;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeEnum;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class FlowNodeLogRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4212279419924296743L;

    /**
     * 节点值
     */
    private String flowNode;

    /**
     * 节点名称（）
     */
    private String nodeName;

    /**
     *  节点状态，1：已完成，2，拒绝或未通过，3；待执行
     *  大节点是否完成判断逻辑：
     *
      */
    private Integer status;

    /**
     * 上级节点
     */
    private String pid;

    //是否大节点
    private Boolean parent;

    private List<FlowNodeLogRespDTO> child =new ArrayList<>();

    public FlowNodeLogRespDTO() {}

    public FlowNodeLogRespDTO(String flowNode, String nodeName) {
        this.flowNode = flowNode;
        this.nodeName = nodeName;
    }

    public FlowNodeLogRespDTO(String flowNode, String nodeName, Integer status) {
        this.flowNode = flowNode;
        this.nodeName = nodeName;
        this.status = status;
    }
    public FlowNodeLogRespDTO(String flowNode, String nodeName, Integer status,String pid,boolean parent) {
        this.flowNode = flowNode;
        this.nodeName = nodeName;
        this.status = status;
        this.pid = pid;
        this.parent = parent;
    }

    public FlowNodeLogRespDTO(FlowNodeEnum flowNodeEnum) {
        this.flowNode = flowNodeEnum.value();
        this.nodeName = flowNodeEnum.nodeName();
        this.status = flowNodeEnum.status();
        this.pid = flowNodeEnum.pid();
        this.parent = flowNodeEnum.parent();
    }

    public FlowNodeLogRespDTO(SaveFlowNodeLogBO saveFlowNodeLogBO) {
        this.flowNode = saveFlowNodeLogBO.getFlowNode();
        this.nodeName = saveFlowNodeLogBO.getNodeName();
        this.status = saveFlowNodeLogBO.getStatus();
        this.pid = saveFlowNodeLogBO.getPid();
        this.parent = saveFlowNodeLogBO.getParent();
    }

}
