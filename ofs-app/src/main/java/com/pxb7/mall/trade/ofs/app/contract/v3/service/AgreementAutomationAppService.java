package com.pxb7.mall.trade.ofs.app.contract.v3.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ofs.app.delivery.mapping.DeliveryInfoAppMapping;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.contract.client.dto.model.AutoCreateAgreementDTO;
import com.pxb7.mall.trade.ofs.contract.domain.model.DeliveryInfoBO;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.message.ExtVoucherCreateMessage;
import com.pxb7.mall.trade.ofs.contract.domain.v3.service.CrtAutomationProcessDomainService;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractDataRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryInfoDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.messaging.MQProducer;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.OrderDeliveryInfoRespDTO;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AgreementAutomationAppService.java
 * @description: 自动化合同流程
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/3 14:56
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class AgreementAutomationAppService {
    @Resource
    private MQProducer mqProducer;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;
    @Resource
    private ContractDataRepository contractDataRepository;
    @Resource
    private DeliveryInfoDomainService deliveryInfoDomainService;
    @Resource
    private CrtAutomationProcessDomainService crtAutomationProcessDomainService;

    public void autoCreateAgreement(AutoCreateAgreementDTO autoCreateAgreementDTO) {
        Tuple2<String, String> tuple = Tuple.of(autoCreateAgreementDTO.getOrderItemId(), autoCreateAgreementDTO.getOperatorId());

        // 当前订单是否存在合同
        ContractData contractData = contractDataRepository.getContractDataWithOrder(tuple._1);
        if (ObjectUtil.isNotEmpty(contractData)) {
            log.warn("A contract already exists for the current order {}, so there is no need to create one.", tuple._1);
            return;
        }

        List<OrderItemIndemnityDubboDTO> effectiveIndemnityList = crtAutomationProcessDomainService.getOrderIndemnityList(tuple._1);
        if (CollUtil.isEmpty(effectiveIndemnityList)) {
            log.warn("The current order {} does not exist effective package compensation, so there is no need to create one.", tuple._1);
            ExtVoucherCreateMessage extVoucherCreateMessage = new ExtVoucherCreateMessage().setOrderItemId(tuple._1);
            if (StrUtil.isNotBlank(tuple._2)) {
                extVoucherCreateMessage.setCreateUserId(tuple._2);
            }
            String result = mqProducer.send(MqConstants.NOTIFY_EXT_VOUCHER_CREATE_TOPIC, MqConstants.NOTIFY_EXT_VOUCHER_CREATE_TAG, extVoucherCreateMessage);
            log.warn("send ext_voucher_create_topic message to mq, orderItemId: {}, createUserId:{}, msgId: {}", tuple._1, tuple._2, result);
            return;
        }
        //获取交付账号信息兼容2.0及3.0
        DeliveryInfoBO deliveryInfo = DeliveryInfoAppMapping.INSTANCE.deliveryInfoDTO2BO(deliveryInfoDomainService.getDeliveryInfo(tuple._1));
        String lockKey = String.format(RedisConstants.AUTOMATION_CREATE_AGREEMENT_LOCK_PREFIX, tuple._1);
        //防止同一个订单重复发起合同
        RLockUtils.of(lockKey, () -> crtAutomationProcessDomainService.automationCreateAgreement(tuple._1, tuple._2, effectiveIndemnityList,deliveryInfo))
            .withWaitTime(8)
            .withTimeUnit(TimeUnit.SECONDS)
            .orElseThrow(() -> new BusinessException(ErrorCode.REPEAT_OPERATION));
    }

    public void notifyUpdateImFlowNode(String orderItemId) {
        OrderDeliveryInfoRespDTO orderDeliveryInfo = orderItemDubboGateway.getOrderDeliveryInfo(orderItemId);
        if (ObjectUtil.isNotEmpty(orderDeliveryInfo)) {
            contractDomainService.notifyContractFlowNode(orderItemId, orderDeliveryInfo.getDeliveryRoomId(), orderDeliveryInfo.getDeliveryCustomerId(), Boolean.FALSE);
        }
    }
}
