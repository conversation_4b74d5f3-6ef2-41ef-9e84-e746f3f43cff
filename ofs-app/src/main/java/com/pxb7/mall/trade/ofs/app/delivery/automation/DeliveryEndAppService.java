package com.pxb7.mall.trade.ofs.app.delivery.automation;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeLogRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.mapping.FlowNodeLogAppMapping;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryStatusChangeDTO;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.DeliveryDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.SaveFlowNodeLogBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.FlowNodeLogRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.WakeUpBO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryOptLogDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryProcessLogDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.service.GroupInfoDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.v2.ProcessInsDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.v2.WakeUpDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.DeliveryResultEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowUserEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.ProcessStatusEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.messaging.MQProducer;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryResultDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessLog;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryResult;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Flow;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.sdk.ProcessInsGateway;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DeliveryEndAppService {

    @Resource
    private FlowDomainService flowDomainService;
    @Resource
    private DeliveryResultDbRepository deliveryResultRepository;
    @Resource
    private DeliveryDomainService deliveryDomainService;
    @Resource
    private DeliveryProcessLogDomainService deliveryProcessLogDomainService;
    @Resource
    private DeliveryOptLogDomainService deliveryOptLogDomainService;
    @Resource
    private GroupInfoDomainService groupInfoDomainService;
    @Resource
    private ProcessInsDomainService processInsDomainService;
    @Resource
    private WakeUpDomainService wakeUpDomainService;
    @Resource
    private ProcessInsGateway processInsGateway;
    @Resource
    private MQProducer mqProducer;


    /**
     * 智能交付3.0 交付完成
     *
     * @param orderItemId
     */
    public void finish(String orderItemId) {

        LambdaUpdateWrapper<DeliveryResult> uw = new LambdaUpdateWrapper<>();
        uw.eq(DeliveryResult::getOrderItemId, orderItemId);
        uw.set(DeliveryResult::getResult, DeliveryResultEnum.FINISH.getValue());
        deliveryResultRepository.update(uw);

        // 交付状态变更
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO().setOrderItemId(orderItemId)
            .setVersion(DeliveryTypeEnum.AUTOMATION_V3.getCode()).setStatus(DeliveryResultEnum.FINISH.getValue())
            .setOperatorId("0");
        String msgId = mqProducer.send(MqConstants.DELIVERY_STATUS_CHANGE_TOPIC, deliveryStatusChangeDTO);
        log.info("[流程状态变更消息发送成功] msg:{} msgId:{}",deliveryStatusChangeDTO, msgId);

    }

    /**
     * 智能交付2.0 交付完成
     *
     * @param flow
     */
    public void finish(Flow flow) {
        SaveFlowNodeLogBO deliveryFinishNodeLogBO =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                .flowNodeEnum(FlowNodeEnum.DELIVERY_FINISH).flowUserEnum(FlowUserEnum.ADMIN).build();
        deliveryFinishNodeLogBO.setStatus(1);
        flowDomainService.saveNodeLog(deliveryFinishNodeLogBO);

        // 保存节点快照
        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
        String snapshotJson = JSONObject.toJSONString(new FlowNodeRespDTO(dtos));
        flow.setNodeSnapshoot(snapshotJson);
        flow.setFlowNode(FlowNodeEnum.DELIVERY_FINISH.getNode());
        flowDomainService.updateNode(flow);

        LambdaUpdateWrapper<DeliveryResult> uw = new LambdaUpdateWrapper<>();
        uw.eq(DeliveryResult::getOrderItemId, flow.getOrderItemId());
        uw.set(DeliveryResult::getResult, DeliveryResultEnum.FINISH.getValue());
        deliveryResultRepository.update(uw);

        String orderItemId = flow.getOrderItemId();
        // 交付状态变更
        DeliveryStatusChangeDTO deliveryStatusChangeDTO =
            new DeliveryStatusChangeDTO().setOrderItemId(orderItemId).setStatus(DeliveryResultEnum.FINISH.getValue())
                .setVersion(DeliveryTypeEnum.AUTOMATION.getCode()).setOperatorId("0");

        String msgId = mqProducer.send(MqConstants.DELIVERY_STATUS_CHANGE_TOPIC, deliveryStatusChangeDTO);
        log.info("[流程状态变更消息发送成功] msg:{} msgId:{}",deliveryStatusChangeDTO, msgId);

    }

    /**
     * 流程完结
     *
     * @param orderItemId
     */
    public void completed(String orderItemId, String customerId, String customerName) {
        DeliveryProcessLog processInsLog = deliveryProcessLogDomainService.getProcessInsLog(orderItemId);
        if (processInsLog != null) {
            deliveryProcessLogDomainService.updateProcessStatus(processInsLog.getProcessInsId(),
                ProcessStatusEnum.COMPLETED.getValue());
            processInsDomainService.publishSwitchProcess(processInsLog.getProcessInsId(), ProcessStatusEnum.ABORT);

            // 终止唤醒
            wakeUpDomainService.abortProcessWakeUp(processInsLog.getProcessInsId());
            // 更新节点
            groupInfoDomainService.sendUpdateNodeNotify(orderItemId);
            // 终止流程
            processInsGateway.terminateProcess(processInsLog.getProcessInsId());
            // 记录日志
            deliveryOptLogDomainService.finish(processInsLog.getProcessInsId(), customerId, customerName);
            return;
        }

        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow == null) {
            return;
        }
        SaveFlowNodeLogBO deliveryFinishNodeLogBO =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                .flowNodeEnum(FlowNodeEnum.DELIVERY_COMPLETED).flowUserEnum(FlowUserEnum.SERVICE).build();
        flowDomainService.saveNodeLog(deliveryFinishNodeLogBO);
        // 保存节点快照
        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
        String snapshotJson = JSONObject.toJSONString(new FlowNodeRespDTO(dtos));
        flow.setNodeSnapshoot(snapshotJson);
        flow.setFlowNode(FlowNodeEnum.DELIVERY_COMPLETED.getNode());

        // 终止触达任务
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
        flowDomainService.updateNode(flow);
    }
}
