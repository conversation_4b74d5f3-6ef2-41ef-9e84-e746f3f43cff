package com.pxb7.mall.trade.ofs.app.contract.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@Data
public class CheckContractReqDTO {

    /**
     * 合同类型1包赔证明 2买家 3卖家 4双方(2、3、4为交易合同)
     */
    @NotNull(message = "合同类型不能为空")
    @Range(min = 1, max = 4, message = "合同类型错误")
    private Integer contractType;

}
