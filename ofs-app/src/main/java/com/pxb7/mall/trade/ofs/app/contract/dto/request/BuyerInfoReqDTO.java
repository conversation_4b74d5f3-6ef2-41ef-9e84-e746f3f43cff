package com.pxb7.mall.trade.ofs.app.contract.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: BuyerInfoReqDTO.java
 * @description: 买家资料请求参数
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/8/27 13:33
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class BuyerInfoReqDTO implements Serializable {

    /**
     * 订单行id
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderItemId;

    /**
     * 保障类型 1 包赔证明 2 交易合同
     */
    @NotNull(message = "保障类型不能为空")
    private Integer guaranteeType;

    @NotBlank(message = "姓名不能为空")
    private String userName;

    /**
     * 证件类型 1:身份证 2港澳通行证 3台湾通行证 4护照
     */
    private Integer certType = 1;

    @NotBlank(message = "身份证号不能为空")
    private String certNo;

    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 用户类型 1:个人 2:商家
     */
    @NotNull(message = "用户类型不能为空")
    private Integer userType;

    /**
     * 账号来源  0自己注册 1螃蟹平台 2其他平台
     */
    private Integer accountSource;

    /**
     * 业务渠道1主站2支付宝
     */
    @NotNull(message = "业务渠道不能为空")
    private Integer busChannel;

    /**
     * (10000, 99999)之间随机数 ，同一个资料填写链接，限制只能提交一次
     */
    private Integer rand;

    /**
     * 客户端 0:pc, 1:android, 2:wap, 3:ios, 4:wechat小程序, 5:alipay小程序, 6:闲鱼, 7：鸿蒙, 8：客服端
     */
    private Integer clientType;

    /**
     * 资料提交卡片消息ID
     */
    private String msgId;
    /**
     * 群聊ID
     */
    private String deliveryRoomId;
    /**
     * 客服ID
     */
    private String customerId;
    /**
     * 买家用户ID
     */
    @NotBlank(message = "买家用户ID不能为空")
    private String buyerUserId;
    /**
     * 卖家用户ID
     */
    @NotBlank(message = "卖家用户ID不能为空")
    private String sellerUserId;

}
