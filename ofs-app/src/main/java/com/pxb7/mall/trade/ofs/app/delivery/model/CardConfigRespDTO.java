package com.pxb7.mall.trade.ofs.app.delivery.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 卡片配置定义(CardConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 10:56:23
 */
public class CardConfigRespDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private Integer cardType;
        private String cardDesc;
        private Long titleConfigId;
        private String cardStyleJson;
    }
}
