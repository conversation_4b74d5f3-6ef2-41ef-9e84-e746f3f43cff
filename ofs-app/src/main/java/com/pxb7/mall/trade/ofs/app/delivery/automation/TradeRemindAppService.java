package com.pxb7.mall.trade.ofs.app.delivery.automation;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonConUrl;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientAndroid;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientPC;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientWap;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeAPI;
import com.pxb7.mall.im.client.dto.request.card.richtext.BaseRichTextMsgContent;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichImgContent;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYTG;
import com.pxb7.mall.product.client.dto.response.product.ProductRespDTO;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.ConfirmationReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.FlowNextReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.TradeAlertNotarizeReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeLogRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.mapping.FlowNodeLogAppMapping;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.util.IntelligentDeliveryThreadPoolExecutorUtil;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.delivery.domain.DeliveryDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.GameTradeAnnouncementsDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.GetTradeAlertBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.SaveFlowNodeLogBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.FlowNodeLogRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.WakeUpBO;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.*;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Flow;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.TradeAlert;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.im.ImMsgGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.product.ProductGateway;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.OrderDeliverReqDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 交易提醒节点
 */
@Service
@Slf4j
public class TradeRemindAppService {

    @Resource
    private CardAppService cardAppService;

    @Resource
    private DeliveryDomainService deliveryDomainService;

    @Resource
    private FlowDomainService flowDomainService;

    @Resource
    private ImMsgGateway imMsgGateway;
    @Resource
    private ProductGateway productGateway;

    @Resource
    private GameTradeAnnouncementsDomainService gameTradeAnnouncementsDomainService;

    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    /**
     * 客服发送交易提醒
     *
     * @param dto
     * @return
     */
    public PxResponse<Boolean> tradeAlertSupport(FlowNextReqDTO dto) {
        String orderItemId = dto.getOrderItemId();
        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.TRADE_REMIND.concat(orderItemId), "0", 3, ChronoUnit.SECONDS);
        if (!b) {
            return PxResponse.ok();
        }
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);
        // 添加放行节点
        List<String> permitNodes =
            ListUtil.of(FlowNodeEnum.TRADE_ALERT.value(), FlowNodeEnum.SELLER_TRADE_ALERT.value(),
                FlowNodeEnum.BUYER_TRADE_ALERT.value(), FlowNodeEnum.BUYER_TRADE_NOT_ALERT.value(),
                FlowNodeEnum.SELLER_TRADE_NOT_ALERT.value());

        // 判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }

        // 向双方发送交易提醒确认卡片
        GetTradeAlertBO getTradeAlertBO =
            GetTradeAlertBO.builder().itemIds(flow.getItemIds()).gameId(flow.getGameId()).build();

        // 获取商品关联的提醒模板
        TradeAlert tradeAlert = gameTradeAnnouncementsDomainService.getTradeAlert(getTradeAlertBO);
        if (ObjectUtil.isEmpty(tradeAlert)) {
            throw new BizException(ErrorCode.GAME_TRADE_ALERT_NOTFOUND_ERROR.getErrCode(),
                ErrorCode.GAME_TRADE_ALERT_NOTFOUND_ERROR.getErrDesc());
        }

        String flowNode = flow.getFlowNode();

        ButtonConUrl buyerPc = new ButtonConUrl().setTargetType(new TargetTypeAPI()).setClientType(new ClientPC())
            .setUrl(FlowButtonEnum.BUYER_CONFIRMATION.pcUrl());
        ButtonConUrl buyerAndroid =
            new ButtonConUrl().setTargetType(new TargetTypeAPI()).setClientType(new ClientAndroid())
                .setUrl(FlowButtonEnum.BUYER_CONFIRMATION.androidUrl());
        ButtonConUrl buyerH5 = new ButtonConUrl().setTargetType(new TargetTypeAPI()).setClientType(new ClientWap())
            .setUrl(FlowButtonEnum.BUYER_CONFIRMATION.wapUrl());

        //买家已确认，不再向买家发送卡片
        if (!FlowNodeEnum.BUYER_TRADE_ALERT.value().equals(flowNode)) {
            // 向买家发送交易提醒卡片
            String buyerRemind = tradeAlert.getBuyerRemind();
            Integer templateType = tradeAlert.getTemplateType();
            ArrayList<BaseRichTextMsgContent> contents = new ArrayList<>();
            if (templateType == 1) {
                contents.add(new RichTextContent(buyerRemind));
            } else {
                contents.add(new RichImgContent(buyerRemind));
            }

            // 卡片内按钮
            ButtonCon buyerButton = new ButtonCon().setParam(new ConfirmationReqDTO(flow.getOrderItemId(), 1))
                .setLabel(FlowButtonEnum.BUYER_CONFIRMATION.title())
                .setButtonUrl(List.of(buyerPc, buyerAndroid, buyerH5));

            boolean sendSuccess = imMsgGateway.sendRichTextMsg(
                new SendRichTextMsgReqDTO().setTitle(FlowCardEnum.TRADE_ALERT_BUYER.title())
                    .setMentionedIds(List.of(flow.getBuyerId()))
                    .setOperation(FlowCardEnum.TRADE_ALERT_BUYER.operation()).setTargetId(flow.getRoomId())
                    .setFromUserId(flow.getServiceId())
                    .setTargetUserIds(List.of(flow.getBuyerId(), flow.getServiceId()))
                    .setOperatorUserIds(List.of(flow.getBuyerId())).setButtons(List.of(buyerButton))
                    .setContent(contents));
            if (!sendSuccess) {
                throw new BizException(ErrorCode.SEND_CARD_FAIL_ERROR.getErrCode(),
                    ErrorCode.SEND_CARD_FAIL_ERROR.getErrDesc());
            }
        }

        ButtonConUrl sellerPc = new ButtonConUrl().setTargetType(new TargetTypeAPI()).setClientType(new ClientPC())
            .setUrl(FlowButtonEnum.BUYER_CONFIRMATION.pcUrl());
        ButtonConUrl sellerAndroid =
            new ButtonConUrl().setTargetType(new TargetTypeAPI()).setClientType(new ClientAndroid())
                .setUrl(FlowButtonEnum.BUYER_CONFIRMATION.androidUrl());
        ButtonConUrl sellerH5 = new ButtonConUrl().setTargetType(new TargetTypeAPI()).setClientType(new ClientWap())
            .setUrl(FlowButtonEnum.BUYER_CONFIRMATION.wapUrl());

        //卖家已确认，不再向卖家发送卡片
        if (!FlowNodeEnum.SELLER_TRADE_ALERT.value().equals(flowNode)) {
            // 向卖家发送交易提醒卡片
            String sellerRemind = tradeAlert.getSellerRemind();

            Integer templateType = tradeAlert.getTemplateType();
            ArrayList<BaseRichTextMsgContent> contents = new ArrayList<>();
            if (templateType == 1) {
                contents.add(new RichTextContent(sellerRemind));
            } else {
                contents.add(new RichImgContent(sellerRemind));
            }
            // 卡片内按钮
            ButtonCon sellerButton = new ButtonCon().setParam(new ConfirmationReqDTO(flow.getOrderItemId(), 2))
                .setLabel(FlowButtonEnum.SELLER_CONFIRMATION.title())
                .setButtonUrl(List.of(sellerPc, sellerAndroid, sellerH5));
            boolean sendSuccess = imMsgGateway.sendRichTextMsg(
                new SendRichTextMsgReqDTO().setTitle(FlowCardEnum.TRADE_ALERT_SELLER.title())
                    .setOperation(FlowCardEnum.TRADE_ALERT_SELLER.operation()).setTargetId(flow.getRoomId())
                    .setMentionedIds(List.of(flow.getSellerId())).setFromUserId(flow.getServiceId())
                    .setTargetUserIds(List.of(flow.getSellerId(), flow.getServiceId()))
                    .setOperatorUserIds(List.of(flow.getSellerId())).setButtons(List.of(sellerButton))
                    .setContent(contents));
            if (!sendSuccess) {
                throw new BizException(ErrorCode.SEND_CARD_FAIL_ERROR.getErrCode(),
                    ErrorCode.SEND_CARD_FAIL_ERROR.getErrDesc());
            }
        }

        return PxResponse.ok(true);
    }

    /**
     * 买卖家 确认交易提醒
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PxResponse<FlowNodeRespDTO> tradeAlertNotarize(TradeAlertNotarizeReqDTO dto) {
        String orderItemId = dto.getOrderItemId();

        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.TRADE_REMIND.concat(orderItemId).concat(dto.getType().toString()),
                "0", 1, ChronoUnit.SECONDS);
        if (!b) {
            throw new BizException(ErrorCode.REPETITIVE_OPERATION_ERROR.getErrCode(),
                ErrorCode.REPETITIVE_OPERATION_ERROR.getErrDesc());
        }

        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);

        // 添加放行节点号
        List<String> permitNodes =
            ListUtil.of(FlowNodeEnum.TRADE_ALERT.value(), FlowNodeEnum.SELLER_TRADE_ALERT.value(),
                FlowNodeEnum.BUYER_TRADE_ALERT.value());

        // 判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }

        boolean allConfirm;
        // 根据操作人类型设置流程节点与节点日志数据
        Integer userType = dto.getType();

        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId());
        // 买家
        if (userType == 1) {
            // 终止触达
            List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
            if (wakeUpTaskSnapshot == null) {
                wakeUpTaskSnapshot = new ArrayList<>();
            }
            deliveryDomainService.abortTask(wakeUpTaskSnapshot, FlowNodeEnum.BUYER_TRADE_NOT_ALERT.value(), flow.getBuyerId());
            flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

            saveFlowNodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.BUYER_TRADE_ALERT).flowUserEnum(FlowUserEnum.BUYER);
            // 不允许买家多次点击确认
            if (flow.getFlowNode().equals(FlowNodeEnum.BUYER_TRADE_ALERT.value())) {
                throw new BizException(ErrorCode.REPETITIVE_OPERATION_ERROR.getErrCode(),
                    ErrorCode.REPETITIVE_OPERATION_ERROR.getErrDesc());
            }

            // 判断卖方是否已经确认
            allConfirm =
                flowDomainService.containsNode(flow.getFlowId(), orderItemId, FlowNodeEnum.SELLER_TRADE_ALERT.value());
            // 保存买家确认 节点日志
            boolean saveSuccess = flowDomainService.saveNodeLog(saveFlowNodeLogBOBuilder.build());
            if (!saveSuccess) {
                PxResponse.fail(ErrorCode.SAVE_FLOW_ERROR.getErrCode(), ErrorCode.SAVE_FLOW_ERROR.getErrDesc());
            }

            notarizeAfter(allConfirm, flow, saveFlowNodeLogBOBuilder, FlowNodeEnum.BUYER_TRADE_ALERT,
                FlowNodeEnum.SELLER_TRADE_NOT_ALERT);

            //更改扩展状态
            SetExpansionDTO buyerCard = new SetExpansionDTO().setOperation(FlowCardEnum.TRADE_ALERT_BUYER.operation())
                .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYTG());
            imMsgGateway.asyncUpdateExpansion(buyerCard);
            return PxResponse.ok();
        }
        // 卖家确认提醒
        // 终止触达
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortTask(wakeUpTaskSnapshot, FlowNodeEnum.SELLER_TRADE_NOT_ALERT.value(), flow.getSellerId());
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

        saveFlowNodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.SELLER_TRADE_ALERT).flowUserEnum(FlowUserEnum.SELLER);
        // 不允许卖家多次点击确认
        if (flow.getFlowNode().equals(FlowNodeEnum.SELLER_TRADE_ALERT.value())) {
            throw new BizException(ErrorCode.REPETITIVE_OPERATION_ERROR.getErrCode(),
                ErrorCode.REPETITIVE_OPERATION_ERROR.getErrDesc());
        }
        boolean saveSuccess = flowDomainService.saveNodeLog(saveFlowNodeLogBOBuilder.build());
        if (!saveSuccess) {
            PxResponse.fail(ErrorCode.SAVE_FLOW_ERROR.getErrCode(), ErrorCode.SAVE_FLOW_ERROR.getErrDesc());
        }

        // 判断买方是否已经确认
        allConfirm =
            flowDomainService.containsNode(flow.getFlowId(), orderItemId, FlowNodeEnum.BUYER_TRADE_ALERT.value());

        notarizeAfter(allConfirm, flow, saveFlowNodeLogBOBuilder, FlowNodeEnum.SELLER_TRADE_ALERT,
            FlowNodeEnum.BUYER_TRADE_NOT_ALERT);

        //更改扩展状态
        SetExpansionDTO sellerCard = new SetExpansionDTO().setOperation(FlowCardEnum.TRADE_ALERT_SELLER.operation())
            .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYTG());
        imMsgGateway.asyncUpdateExpansion(sellerCard);
        return PxResponse.ok();

    }

    public PxResponse<FlowNodeRespDTO> notarizeAfter(boolean flag, Flow flow,
        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder, FlowNodeEnum flowNodeEnum,
        FlowNodeEnum virtualNode) {
        // 未能双方确认，直接返回节点数据
        if (!flag) {
            saveFlowNodeLogBOBuilder.flowNodeEnum(virtualNode);
            List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
            List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);

            // 将确认买家或者卖家 添加到快照中
            dtos.add(FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTORespDTO(saveFlowNodeLogBOBuilder.build()));

            // socket 通知前端最新的流程节点
            String snapshotJson =
                JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.TRADE_ALERT.buttons()));

            flow.setFlowNode(flowNodeEnum.value());
            flow.setNodeSnapshoot(snapshotJson);
            boolean saveSuccess = flowDomainService.updateNode(flow);
            if (!saveSuccess) {
                PxResponse.fail(ErrorCode.SAVE_FLOW_ERROR.getErrCode(), ErrorCode.SAVE_FLOW_ERROR.getErrDesc());
            }

            //通过融云通知手机端通过接口更新节点信息
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

            return PxResponse.ok();
        }

        // 确认交易阶段完成
        flowDomainService.finishNodeLog(flow.getFlowId(), flow.getOrderItemId(), FlowNodeEnum.TRADE_ALERT.getNode());

        // 进入卖家提交账号阶段
        SaveFlowNodeLogBO nodeLogBO =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.COLLECT).build();
        flowDomainService.saveNodeLog(nodeLogBO);

        List<FlowNodeLogRespDTO> dtos = new ArrayList<>();
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.TRADE_ALERT));
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SELLER_TRADE_ALERT));
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.BUYER_TRADE_ALERT));
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.COLLECT));
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.COLLECT_SELLER_NOT_COMMIT));

        String snapshotJson = JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.COLLECT.buttons()));

        // 更新流程节点数据 买卖双方确定后，将流程节点设置为收集账号信息
        flow.setFlowNode(FlowNodeEnum.COLLECT.value());
        //  更新已经执行的 节点快照
        flow.setNodeSnapshoot(snapshotJson);

        //发送卖家账号信息收集卡片
        cardAppService.sendSellerCollectCard(flow);

        //向买家发送等待提交信息
        cardAppService.sendBuyerWaitCard(flow);

        ProductRespDTO productRespDTO = productGateway.getProduct(flow.getProductId());
        Map<String, Object> param = new HashMap<>();
        param.put("orderNo", flow.getOrderItemId());
        if (productRespDTO != null && StringUtils.isNotBlank(productRespDTO.getProductUniqueNo())) {
            param.put("productUniqueNo", productRespDTO.getProductUniqueNo());
        } else {
            param.put("productUniqueNo", "");
        }

        WakeUpBO sellerWakeUpBO =
            deliveryDomainService.openNodeWakeUp(FlowNodeEnum.COLLECT_SELLER_NOT_COMMIT.value(), flow.getOrderItemId(),
                flow.getServiceId(), flow.getRoomId(), flow.getSellerId(), param);
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        if (sellerWakeUpBO != null) {
            wakeUpTaskSnapshot.add(sellerWakeUpBO);
        }
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

        flowDomainService.updateNode(flow);

        // 告知订单某个节点完结
        notifyOrder(flow);

        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
        return PxResponse.ok();
    }

    /**
     * 客服手动确认 交易提醒
     *
     * @param orderItemId
     * @return
     */
    public PxResponse<FlowNodeRespDTO> tradeAlertSupportNotarize(String orderItemId) {
        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.TRADE_REMIND.concat(orderItemId), "0", 3, ChronoUnit.SECONDS);
        if (!b) {
            return PxResponse.ok();
        }
        Flow flow = flowDomainService.getFlow(orderItemId);
        flowDomainService.verifyFlowStatus(flow);
        // 添加放行节点
        List<String> permitNodes =
            ListUtil.of(FlowNodeEnum.TRADE_ALERT.value(), FlowNodeEnum.SELLER_TRADE_ALERT.value(),
                FlowNodeEnum.BUYER_TRADE_ALERT.value(), FlowNodeEnum.BUYER_TRADE_NOT_ALERT.value(),
                FlowNodeEnum.SELLER_TRADE_NOT_ALERT.value());

        // 判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }

        //如果卖家没有确认过，手动设置卖家确认节点
        boolean sellerTradeAlert =
            flowDomainService.containsNode(flow.getFlowId(), orderItemId, FlowNodeEnum.SELLER_TRADE_ALERT.value());
        if (!sellerTradeAlert) {
            SaveFlowNodeLogBO nodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                    .flowUserEnum(FlowUserEnum.SERVICE).flowNodeEnum(FlowNodeEnum.SELLER_TRADE_ALERT).build();
            flowDomainService.saveNodeLog(nodeLogBO);

            // 更新卡片为已完成
            SetExpansionDTO sellerCard = new SetExpansionDTO().setOperation(FlowCardEnum.TRADE_ALERT_SELLER.operation())
                .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYTG());
            imMsgGateway.asyncUpdateExpansion(sellerCard);
        }
        //如果买家没有确认过，手动设置买家确认节点
        boolean buyerTradeAlert =
            flowDomainService.containsNode(flow.getFlowId(), orderItemId, FlowNodeEnum.BUYER_TRADE_ALERT.value());
        if (!buyerTradeAlert) {
            SaveFlowNodeLogBO nodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).flowNodeEnum(FlowNodeEnum.BUYER_TRADE_ALERT)
                    .orderItemId(flow.getOrderItemId()).flowUserEnum(FlowUserEnum.SERVICE).build();
            flowDomainService.saveNodeLog(nodeLogBO);
            // 更新卡片为已完成
            SetExpansionDTO buyerCard = new SetExpansionDTO().setOperation(FlowCardEnum.TRADE_ALERT_BUYER.operation())
                .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYTG());
            imMsgGateway.asyncUpdateExpansion(buyerCard);
        }

        // 终止唤醒任务
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            // 历史数据为null情况
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);

        // 确认交易阶段完成
        flowDomainService.finishNodeLog(flow.getFlowId(), flow.getOrderItemId(), FlowNodeEnum.TRADE_ALERT.getNode());

        SaveFlowNodeLogBO collectNodeLogBO =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).flowNodeEnum(FlowNodeEnum.COLLECT)
                .orderItemId(flow.getOrderItemId()).flowUserEnum(FlowUserEnum.SERVICE).build();
        collectNodeLogBO.setStatus(3);
        flowDomainService.saveNodeLog(collectNodeLogBO);

        // 返回流程节点list
        List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);

        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.COLLECT_SELLER_NOT_COMMIT));

        notifyOrder(flow);

        String nodeSnapshotJson =
            JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.COLLECT.buttons()));
        flow.setFlowNode(FlowNodeEnum.COLLECT.value());
        flow.setNodeSnapshoot(nodeSnapshotJson);

        //发送卖家信息收集卡片
        cardAppService.sendSellerCollectCard(flow);

        ProductRespDTO productRespDTO = productGateway.getProduct(flow.getProductId());

        Map<String, Object> param = new HashMap<>();
        param.put("orderNo", orderItemId);
        if (productRespDTO != null && StringUtils.isNotBlank(productRespDTO.getProductUniqueNo())) {
            param.put("productUniqueNo", productRespDTO.getProductUniqueNo());
        } else {
            param.put("productUniqueNo", "");
        }
        WakeUpBO sellerWakeUpBO =
            deliveryDomainService.openNodeWakeUp(FlowNodeEnum.COLLECT_SELLER_NOT_COMMIT.value(), flow.getOrderItemId(),
                flow.getServiceId(), flow.getRoomId(), flow.getSellerId(), param);
        if (sellerWakeUpBO != null) {
            wakeUpTaskSnapshot.add(sellerWakeUpBO);
        }
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

        flowDomainService.updateNode(flow);

        //通过融云通知手机端通过接口更新节点信息
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

        return PxResponse.ok();
    }

    /**
     * 通知订单交易提醒节点完成
     *
     * @param flow
     */
    private void notifyOrder(Flow flow) {
        // 告知订单某个节点完结
        OrderDeliverReqDTO orderDeliverReqDTO = new OrderDeliverReqDTO();
        orderDeliverReqDTO.setNode(FlowNodeEnum.TRADE_ALERT.value());
        orderDeliverReqDTO.setOrderItemId(flow.getOrderItemId());
        orderDeliverReqDTO.setNodeTime(LocalDateTime.now());
        IntelligentDeliveryThreadPoolExecutorUtil.executeTask(
            () -> orderInfoDubboServiceI.addDeliverNode(orderDeliverReqDTO));
    }

}
