package com.pxb7.mall.trade.ofs.app.delivery.submit.card;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.pxb7.mall.trade.ofs.delivery.domain.task.taskHandle.model.UserInfoResult;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.alibaba.cola.exception.SysException;
import com.pxb7.mall.trade.ofs.app.delivery.model.CardButtonConfigRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.node.NodeAppService;
import com.pxb7.mall.trade.ofs.app.delivery.node.models.OptCardResDto;
import com.pxb7.mall.trade.ofs.app.delivery.submit.AbstractSubmitAppService;
import com.pxb7.mall.trade.ofs.app.delivery.submit.card.models.CardSubmitReqDto;
import com.pxb7.mall.trade.ofs.delivery.client.enums.ButtonTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.BatchSetDeliveryDataReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.DeliveryDataSetReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.task.event.DataSubmitEvent;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CardSubmitAppService extends AbstractSubmitAppService {

    @Resource
    private NodeAppService nodeAppService;

    @Resource
    private DataSourceTransactionManager transactionManager;

    @Resource
    private DeliveryDataDomainService deliveryDataDomainService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    public Boolean submit(CardSubmitReqDto dto) {
        log.info("[卡片提交], 流程运行实例id: {}, 节点运行id: {}, cid: {}, 参数: {}", dto.getProcessInsId(), dto.getTaskInsId(), dto.getUniquenessId(), dto);

        checkProcess(dto.getProcessInsId(), dto.getOrderItemId());

        // 获取当前节点对应的 流转卡片
        OptCardResDto optCardResDto = nodeAppService.getNodeOperationCard(dto.getProcessInsId(), dto.getTaskInsId());

        UserInfoResult userInfoResult = validAndGetUserType(optCardResDto.getGroupId(), optCardResDto.getGroupMemberInfos(), optCardResDto.getMentionedEnumList(), dto.getLoginUserInfo());

        checkDelayTimeIsOver(optCardResDto.getOrderItemId(), dto.getProcessInsId(), dto.getTaskInsId(), userInfoResult.getCustomerFlag());

        // 判断卡片类型是不是 确认类型 或者 多按钮
        CardButtonConfigRespDTO.DetailDTO buttonConfig = validAndGetCardButton(optCardResDto.getCardConfigId(), dto.getUniquenessId(), null);

        Map<String, Object> deliveryData = new HashMap<>();

        // 把按钮的数据保存
        if (ButtonTypeEnum.MULTI_BUTTON.eq(buttonConfig.getButtonType())) {

            BatchSetDeliveryDataReqBO batchSetDeliveryDataReqBO = new BatchSetDeliveryDataReqBO().setProcessInsId(dto.getProcessInsId())
                .setOrderItemId(dto.getOrderItemId())
                .setDataBatch(List.of(new DeliveryDataSetReqBO().setCode(buttonConfig.getButtonValueCode())
                    .setValue(buttonConfig.getButtonEnumCode())));

            deliveryData = saveCardButtonToData(dto.getProcessInsId(), dto.getTaskInsId(), batchSetDeliveryDataReqBO);
        }

        // 发送event 通知
        DataSubmitEvent dataSubmitEvent = new DataSubmitEvent().setProcessInsId(dto.getProcessInsId())
                .setTaskInsId(dto.getTaskInsId())
                .setUserInfoResult(userInfoResult)
                .setDeliveryData(deliveryData);

        applicationEventPublisher.publishEvent(dataSubmitEvent);

        log.info("卡片提交, 发送event通知, 流程运行实例id: {}, 节点运行id: {}, event: {}", dto.getProcessInsId(), dto.getTaskInsId(), dataSubmitEvent);

        log.debug("[卡片提交, 按钮置灰], 点击的按钮: {}", dto.getUniquenessId());
        cancelImMessagePinned(dto.getLoginUserInfo().getUserId(), optCardResDto, dto.getUniquenessId());

        return Boolean.TRUE;
    }


    public Map<String, Object> saveCardButtonToData(String processInsId, String taskInsId, BatchSetDeliveryDataReqBO batchSetDeliveryDataReqBO) {

        log.info("卡片进行保存, 流程运行实例id: {}, 节点运行id: {}", processInsId, taskInsId);

        Map<String, Object> resMap;

        boolean result = deliveryDataDomainService.batchVerifyDeliveryData(batchSetDeliveryDataReqBO.getDataBatch());
        if (!result) {
            return new HashMap<String, Object>();
        }
        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(5);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {

            resMap = deliveryDataDomainService.batchVerifyAndSetDeliveryData(batchSetDeliveryDataReqBO);
            transactionManager.commit(status);

        } catch (Throwable e) {
            log.error("卡片保存失败, 流程运行实例id: {}, 节点运行id: {}", processInsId, taskInsId, e);
            transactionManager.rollback(status);
            throw new SysException(ErrorCode.ORDER_CREATE_ERROR.getErrCode(), ErrorCode.ORDER_CREATE_ERROR.getErrDesc());
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }

        return resMap;
    }


}
