package com.pxb7.mall.trade.ofs.app.delivery.automation;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantInfoRespDTO;
import com.pxb7.mall.product.client.dto.response.product.ProductRespDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractIndemnityDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.CollectMaterialReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.ContractInitiateReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.ContractTypeReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.service.InfoCardPushAppService;
import com.pxb7.mall.trade.ofs.app.contract.service.MainContractAppService;
import com.pxb7.mall.trade.ofs.app.contract.v3.service.ImContractProcessAppService;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeLogRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.UserChildFlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.mapping.AutomationCallBackAppMapping;
import com.pxb7.mall.trade.ofs.app.delivery.mapping.FlowNodeLogAppMapping;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.enums.ContractStatusEnum;
import com.pxb7.mall.trade.ofs.common.config.enums.OrderSourceEnum;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.DubboResultAssert;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.InfoSubmitStatusReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractInfoRespDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.ContractTypeRespDTO;
import com.pxb7.mall.trade.ofs.contract.domain.model.InfoSubmitStatusBO;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractDomainService;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.MerchantGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.ContractDataCommitReqDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.OrderItemIdReqDTO;
import com.pxb7.mall.trade.ofs.delivery.domain.DeliveryDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.OrderDeliverNodeDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.SaveFlowNodeLogBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.FlowNodeLogRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.WakeUpBO;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeButtonsEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowUserEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Flow;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.MerchantInfoGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.UserCenterGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.im.ImMsgGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.order.OrderInfoGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.product.ProductGateway;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.order.RoomOrderDetailsRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderItemAmountRespDTO;
import com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.AccountDeliverNodeNewEnum;
import com.pxb7.mall.trade.order.client.enums.order.ProductTypeEnum;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 合同节点
 */
@Service
@Slf4j
public class ContractAppService {

    @Resource
    private CardAppService cardAppService;

    @Resource
    private ContractDomainService contractDomainService;

    @Resource
    private DeliveryInfoDbRepository deliveryInfoRepository;


    @Resource
    private ImContractProcessAppService imContractProcessAppService;

    @Resource
    private DeliveryDomainService deliveryDomainService;

    @Resource
    private ExchangeBindingAppService exchangeBindingAppService;
    @Resource
    private FlowDomainService flowDomainService;
    @Resource
    private InfoCardPushAppService infoCardPushAppService;

    @Resource
    private MainContractAppService mainContractAppService;

    @Resource
    private ImMsgGateway imMsgGateway;
    @Resource
    private MerchantGateway merchantGateway;
    @Resource
    private ProductGateway productGateway;
    @Resource
    private OrderDeliverNodeDomainService orderDeliverNodeDomainService;

    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;
    @Resource
    private OrderInfoGateway orderInfoGateway;
    @Resource
    private UserCenterGateway userCenterGateway;
    @Resource
    private MerchantInfoGateway merchantInfoGateway;

    @Value("${delivery.autoInitContract}")
    private boolean autoInitContract;

    /**
     * 向卖方推送收集合同签署资料卡片任务
     *
     * @param flow
     */
    public void sendCardOfCollectContract(Flow flow) {
        DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(flow.getOrderItemId());
        InfoSubmitStatusReqDTO infoSubmitStatusReqDTO = new InfoSubmitStatusReqDTO();
        infoSubmitStatusReqDTO.setOrderItemId(flow.getOrderItemId());
        infoSubmitStatusReqDTO.setGameId(flow.getGameId());
        String buyerId = flow.getBuyerId();
        infoSubmitStatusReqDTO.setBuyerId(buyerId);
        String sellerId = flow.getSellerId();
        infoSubmitStatusReqDTO.setSellerId(sellerId);
        UserShortInfoDTO sellerInfoDTO = userCenterGateway.getUserInfo(sellerId);
        // 号商查询 有效期
        if (sellerInfoDTO != null && Objects.equals(2, sellerInfoDTO.getUserType())) {
            MerchantInfoRespDTO merchantBusinessInfo =
                merchantInfoGateway.getMerchantBusinessInfo(sellerId, ProductTypeEnum.ACCOUNT.getMerchantValue());
            boolean sellerEffect = merchantBusinessInfo != null && merchantBusinessInfo.isBusinessFlag();
            infoSubmitStatusReqDTO.setSellerEffect(sellerEffect);
            infoSubmitStatusReqDTO.setSellerIdentity(2);
        } else if (sellerInfoDTO != null) {
            infoSubmitStatusReqDTO.setSellerIdentity(1);
            infoSubmitStatusReqDTO.setSellerEffect(false);
        }
        infoSubmitStatusReqDTO.setBuyerEffect(false);
        infoSubmitStatusReqDTO.setBuyerIdentity(1);
        InfoSubmitStatusBO infoSubmitStatusBO = contractDomainService.notifyInfoSubmitStatus(infoSubmitStatusReqDTO);

        if (!infoSubmitStatusBO.getSellerCommitStatus() && !deliveryInfo.isRepackage()) {
            return;
        }

        String orderItemId = flow.getOrderItemId();

        SingleResponse<OrderInfoDubboRespDTO> orderInfo =
            DubboResultAssert.wrapException(() -> orderInfoDubboServiceI.getOrderInfo(orderItemId),
                ErrorCode.RPC_GET_ORDER_INFO_ERROR.getErrCode(), ErrorCode.RPC_GET_ORDER_INFO_ERROR.getErrDesc());

        OrderInfoDubboRespDTO data = orderInfo.getData();
        CollectMaterialReqDTO collectMaterialReqDTO = new CollectMaterialReqDTO();
        collectMaterialReqDTO.setBuyerUserId(flow.getBuyerId());
        collectMaterialReqDTO.setCustomerUserId(flow.getServiceId());
        collectMaterialReqDTO.setSellerUserId(flow.getSellerId());
        collectMaterialReqDTO.setDeliveryRoomId(flow.getRoomId());
        collectMaterialReqDTO.setOrderItemId(orderItemId);
        collectMaterialReqDTO.setAccountSource(deliveryInfo.getAccountSource());

        collectMaterialReqDTO.setOrderSource(data.getOrderSource());
        // 判断卖方是否已经提交合同，如果卖家没提交合同资料，  卖方推送收集合同签署资料卡片任务（虚拟节点）
        collectMaterialReqDTO.setPhone(sellerInfoDTO.getPhone());
        infoCardPushAppService.sendSellerInfoCard(collectMaterialReqDTO);

    }

    @Transactional(rollbackFor = Exception.class)
    public void startContract(String orderItemId) {

        Flow flow = flowDomainService.getFlow(orderItemId);

        // 初始化用户侧子节点数据
        List<UserChildFlowNodeRespDTO> childNodeValue = new ArrayList<>();
        UserChildFlowNodeRespDTO initContractChildNode =
            new UserChildFlowNodeRespDTO(FlowNodeEnum.INIT_CONTRACT_FINISH.getNode(), "发起合同",
                FlowNodeEnum.INIT_CONTRACT_FINISH.getPid());
        childNodeValue.add(initContractChildNode);
        UserChildFlowNodeRespDTO signContractFinish =
            new UserChildFlowNodeRespDTO(FlowNodeEnum.SIGN_CONTRACT_FINISH.getNode(), "签署合同",
                FlowNodeEnum.SIGN_CONTRACT_FINISH.getPid());
        childNodeValue.add(signContractFinish);

        JSONObject contractNodeValue = new JSONObject();
        contractNodeValue.put("childNode", childNodeValue);
        SaveFlowNodeLogBO signContractNodeLog =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                .flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT).flowUserEnum(FlowUserEnum.ADMIN)
                .nodeValue(JSONObject.toJSONString(contractNodeValue)).build();
        signContractNodeLog.setStatus(3);
        flowDomainService.saveNodeLog(signContractNodeLog);
        // 查询有效合同
        ContractData validContract = contractDomainService.getValidContract(flow.getOrderItemId());
        // 存在有效合同 & 非冻结&已完成  合同节点完成
        if (validContract != null && Objects.equals(validContract.getStatus(), 3)) {
            contractFinish(flow);

            // 合同节点完成
            flowDomainService.finishNodeLog(flow.getFlowId(), flow.getOrderItemId(),
                FlowNodeEnum.SIGN_CONTRACT.getNode());
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
            // 进入换绑节点
            exchangeBindingAppService.startExchangeBinding(flow.getOrderItemId());
            return;
        } else if (validContract != null && validContract.getFreeze()) {
            // 存在合同 处于冻结状态
            freezeContract(flow);
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
            return;
        } else if (validContract != null) {
            // 合同待签署
            signingContract(flow);
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
            return;
        }

        InfoSubmitStatusReqDTO infoSubmitStatusReqDTO = new InfoSubmitStatusReqDTO();
        infoSubmitStatusReqDTO.setOrderItemId(flow.getOrderItemId());
        infoSubmitStatusReqDTO.setGameId(flow.getGameId());
        String buyerId = flow.getBuyerId();
        infoSubmitStatusReqDTO.setBuyerId(buyerId);
        String sellerId = flow.getSellerId();
        infoSubmitStatusReqDTO.setSellerId(sellerId);

        List<String> userIds = new ArrayList<>();
        userIds.add(buyerId);
        userIds.add(sellerId);
        Map<String, UserShortInfoDTO> userShortInfoDTOMap = userCenterGateway.getUserInfo(userIds);
        // 卖家
        UserShortInfoDTO sellerInfoDTO = userShortInfoDTOMap.get(sellerId);
        // 号商查询 有效期
        if (sellerInfoDTO != null && Objects.equals(2, sellerInfoDTO.getUserType())) {
            MerchantInfoRespDTO merchantBusinessInfo =
                merchantInfoGateway.getMerchantBusinessInfo(sellerId, ProductTypeEnum.ACCOUNT.getMerchantValue());
            boolean sellerEffect = merchantBusinessInfo != null && merchantBusinessInfo.isBusinessFlag();
            infoSubmitStatusReqDTO.setSellerEffect(sellerEffect);
            infoSubmitStatusReqDTO.setSellerIdentity(2);
        } else if (sellerInfoDTO != null) {
            infoSubmitStatusReqDTO.setSellerIdentity(1);
            infoSubmitStatusReqDTO.setSellerEffect(false);
        }

        // 买家
        UserShortInfoDTO buyerInfo = userShortInfoDTOMap.get(buyerId);
        // 号商查询 有效期
        if (buyerInfo != null && Objects.equals(2, buyerInfo.getUserType())) {
            MerchantInfoRespDTO merchantBusinessInfo =
                merchantInfoGateway.getMerchantBusinessInfo(buyerId, ProductTypeEnum.ACCOUNT.getMerchantValue());
            boolean buyerEffect = merchantBusinessInfo != null && merchantBusinessInfo.isBusinessFlag();
            infoSubmitStatusReqDTO.setBuyerEffect(buyerEffect);
            infoSubmitStatusReqDTO.setBuyerIdentity(2);
        } else if (buyerInfo != null) {
            infoSubmitStatusReqDTO.setBuyerIdentity(1);
            infoSubmitStatusReqDTO.setBuyerEffect(false);
        }
        InfoSubmitStatusBO infoSubmitStatusBO = contractDomainService.notifyInfoSubmitStatus(infoSubmitStatusReqDTO);

        DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(flow.getOrderItemId());
        boolean needCollectSeller = !infoSubmitStatusBO.getSellerCommitStatus() && !deliveryInfo.isRepackage();
        if (needCollectSeller) {
            RoomOrderDetailsRespDTO roomOrderDetails = orderInfoGateway.getRoomOrderDetails(flow.getRoomId());
            // 收集卖家资料
            CollectMaterialReqDTO collectMaterialReqDTO = new CollectMaterialReqDTO();
            collectMaterialReqDTO.setBuyerUserId(flow.getSellerId());
            collectMaterialReqDTO.setCustomerUserId(flow.getServiceId());
            collectMaterialReqDTO.setSellerUserId(flow.getSellerId());
            collectMaterialReqDTO.setDeliveryRoomId(flow.getRoomId());
            collectMaterialReqDTO.setOrderItemId(flow.getOrderItemId());
            collectMaterialReqDTO.setAccountSource(deliveryInfo.getAccountSource());
            if (roomOrderDetails != null) {
                collectMaterialReqDTO.setOrderSource(roomOrderDetails.getOrderSource());
            }
            if (sellerInfoDTO != null) {
                collectMaterialReqDTO.setPhone(sellerInfoDTO.getPhone());
            }
            boolean sendSuccess = cardAppService.sendCollectContractInfoToSeller(collectMaterialReqDTO);
            if (sendSuccess) {
                ProductRespDTO productRespDTO = productGateway.getProduct(flow.getProductId());
                Map<String, Object> param = new HashMap<>();
                param.put("orderNo", orderItemId);
                if (productRespDTO != null && StringUtils.isNotBlank(productRespDTO.getProductUniqueNo())) {
                    param.put("productUniqueNo", productRespDTO.getProductUniqueNo());
                } else {
                    param.put("productUniqueNo", "");
                }
                WakeUpBO sellerWakeUpBO =
                    deliveryDomainService.openNodeWakeUp(FlowNodeEnum.SIGN_CONTRACT_SELLER_NOT_SUBMIT.value(),
                        orderItemId, flow.getServiceId(), flow.getRoomId(), flow.getSellerId(), param);
                List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
                if (wakeUpTaskSnapshot == null) {
                    wakeUpTaskSnapshot = new ArrayList<>();
                }
                if (sellerWakeUpBO != null) {
                    wakeUpTaskSnapshot.add(sellerWakeUpBO);
                }
                flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
            }

        } else {
            SaveFlowNodeLogBO nodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                    .flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(nodeLogBO);
        }
        boolean needCollectBuyer = !infoSubmitStatusBO.getBuyerCommitStatus();
        if (needCollectBuyer) {
            RoomOrderDetailsRespDTO roomOrderDetails = orderInfoGateway.getRoomOrderDetails(flow.getRoomId());
            // 收集买家资料
            CollectMaterialReqDTO collectMaterialReqDTO = new CollectMaterialReqDTO();
            collectMaterialReqDTO.setBuyerUserId(flow.getBuyerId());
            collectMaterialReqDTO.setCustomerUserId(flow.getServiceId());
            collectMaterialReqDTO.setSellerUserId(flow.getSellerId());
            collectMaterialReqDTO.setDeliveryRoomId(flow.getRoomId());
            collectMaterialReqDTO.setOrderItemId(flow.getOrderItemId());
            collectMaterialReqDTO.setAccountSource(deliveryInfo.getAccountSource());
            if (roomOrderDetails != null) {
                collectMaterialReqDTO.setOrderSource(roomOrderDetails.getOrderSource());
            }
            collectMaterialReqDTO.setPhone(buyerInfo.getPhone());
            boolean sendSuccess = cardAppService.sendCollectContractInfoToBuyer(collectMaterialReqDTO);
            if (sendSuccess) {
                ProductRespDTO productRespDTO = productGateway.getProduct(flow.getProductId());
                Map<String, Object> param = new HashMap<>();
                param.put("orderNo", orderItemId);
                if (productRespDTO != null && StringUtils.isNotBlank(productRespDTO.getProductUniqueNo())) {
                    param.put("productUniqueNo", productRespDTO.getProductUniqueNo());
                } else {
                    param.put("productUniqueNo", "");
                }
                WakeUpBO buyerWakeUpBO =
                    deliveryDomainService.openNodeWakeUp(FlowNodeEnum.SIGN_CONTRACT_BUYER_NOT_SUBMIT.value(),
                        orderItemId, flow.getServiceId(), flow.getRoomId(), flow.getBuyerId(), param);
                List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
                if (wakeUpTaskSnapshot == null) {
                    wakeUpTaskSnapshot = new ArrayList<>();
                }
                if (buyerWakeUpBO != null) {
                    wakeUpTaskSnapshot.add(buyerWakeUpBO);

                }
                flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
            }

        } else {
            SaveFlowNodeLogBO nodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                    .flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(nodeLogBO);
        }

        //  都不需要收集 直接发起合同
        if (!needCollectSeller && !needCollectBuyer) {
            List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
            List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
            if (autoInitContract) {
                boolean success = initContract(flow, deliveryInfo);
                // 发起合同失败
                if (!success) {
                    dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_FAIL));
                    flow.setFlowNode(FlowNodeEnum.INIT_CONTRACT_FAIL.getNode());
                    FlowNodeRespDTO flowNodeRespDTO =
                        new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
                    flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
                    flowDomainService.updateNode(flow);
                    imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
                    return;
                }
                dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_WAIT));
                FlowNodeRespDTO flowNodeRespDTO =
                    new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
                flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
            } else {
                dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_WAIT));
                FlowNodeRespDTO flowNodeRespDTO =
                    new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
                flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
            }
            flow.setFlowNode(FlowNodeEnum.INIT_CONTRACT_WAIT.getNode());
            flowDomainService.updateNode(flow);

            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
            imMsgGateway.asyncSendRemindMsg(flow.getRoomId(), flow.getBuyerId());
            return;
        }

        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
        List<String> buttons = new ArrayList<>(FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
        if (!infoSubmitStatusBO.getBuyerCommitStatus()) {
            buttons.addAll(FlowNodeButtonsEnum.COLLECT_BUYER_INFO.buttons());
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_BUYER_NOT_SUBMIT));
        } else {
            buttons.addAll(FlowNodeButtonsEnum.BUYER_INFO_DETAIL.buttons());
        }
        if (!infoSubmitStatusBO.getSellerCommitStatus() && !deliveryInfo.isRepackage()) {
            buttons.addAll(FlowNodeButtonsEnum.COLLECT_SELLER_INFO.buttons());
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_SELLER_NOT_SUBMIT));
        } else {
            buttons.addAll(FlowNodeButtonsEnum.SELLER_INFO_DETAIL.buttons());
        }
        flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT.getNode());
        FlowNodeRespDTO flowNodeRespDTO = new FlowNodeRespDTO(dtos, buttons);
        flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
        flowDomainService.updateNode(flow);
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
    }

    private void signingContract(Flow flow) {
        // 买家资料已提交
        SaveFlowNodeLogBO buyerSubmitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT).build();
        // 合同发起日志
        flowDomainService.saveNodeLog(buyerSubmitNodeLogBO);
        // 卖家资料已提交
        SaveFlowNodeLogBO sellerSubmitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT).build();
        flowDomainService.saveNodeLog(sellerSubmitNodeLogBO);

        SaveFlowNodeLogBO contractInitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.INIT_CONTRACT_FINISH).build();
        // 合同发起成功
        flowDomainService.saveNodeLog(contractInitNodeLogBO);

        // 合同冻结 通过
        SaveFlowNodeLogBO contractFreezePassNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.CONTRACT_FREEZE_PASS).build();
        flowDomainService.saveNodeLog(contractFreezePassNodeLogBO);

        // 合同待签署
        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH));

        // 合同处于冻结
        flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH.getNode());
        String flowNodeSnapshot =
            JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CONTRACT_DETAIL.buttons()));
        flow.setNodeSnapshoot(flowNodeSnapshot);
        flowDomainService.updateNode(flow);

    }

    /**
     * 合同冻结
     *
     * @param flow
     */
    private void freezeContract(Flow flow) {
        // 买家资料已提交
        SaveFlowNodeLogBO buyerSubmitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT).build();
        // 合同发起日志
        flowDomainService.saveNodeLog(buyerSubmitNodeLogBO);
        // 卖家资料已提交
        SaveFlowNodeLogBO sellerSubmitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT).build();
        flowDomainService.saveNodeLog(sellerSubmitNodeLogBO);

        SaveFlowNodeLogBO contractInitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.INIT_CONTRACT_FINISH).build();
        // 合同发起成功
        flowDomainService.saveNodeLog(contractInitNodeLogBO);

        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.CONTRACT_FREEZE_REJECT));

        // 合同处于冻结
        flow.setFlowNode(FlowNodeEnum.CONTRACT_FREEZE_REJECT.getNode());
        String flowNodeSnapshot =
            JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CONTRACT_DETAIL.buttons()));
        flow.setNodeSnapshoot(flowNodeSnapshot);
        flowDomainService.updateNode(flow);

    }

    /**
     * 发起合同
     *
     * @param flow
     * @param deliveryInfo
     * @return
     */
    private boolean initContract(Flow flow, DeliveryInfo deliveryInfo) {
        RoomOrderDetailsRespDTO roomOrderDetails = orderInfoGateway.getRoomOrderDetails(flow.getRoomId());
        if (roomOrderDetails == null) {
            return false;
        }
        Long preDiscountPrice = roomOrderDetails.getProductOriginalPrice();
        Long discountPrice = roomOrderDetails.getProductSalePrice();
        OrderItemAmountRespDTO orderItemAmountRespDTO = orderItemDubboGateway.getDealPrice(flow.getOrderItemId());
        if (ObjectUtil.isNotEmpty(orderItemAmountRespDTO)) {
            preDiscountPrice = orderItemAmountRespDTO.getProductAmount();
            discountPrice = orderItemAmountRespDTO.getProductActualAmount();
        }

        ContractTypeReqDTO contractTypeReqDTO = new ContractTypeReqDTO();
        contractTypeReqDTO.setBuyerId(flow.getBuyerId());
        contractTypeReqDTO.setSellerId(flow.getSellerId());
        contractTypeReqDTO.setOrderItemId(flow.getOrderItemId());
        contractTypeReqDTO.setGameId(flow.getGameId());
        ContractTypeRespDTO contractTypeInfo = imContractProcessAppService.getAgreementTypeInfo(contractTypeReqDTO);

        //双方都已经提交资料，自动创建合同，并记录节点
        ContractInitiateReqDTO contractInitiateReqDTO = new ContractInitiateReqDTO();
        contractInitiateReqDTO.setBuyerId(flow.getBuyerId());
        contractInitiateReqDTO.setSellerId(flow.getSellerId());
        contractInitiateReqDTO.setGameId(flow.getGameId());
        contractInitiateReqDTO.setProductId(flow.getProductId());
        contractInitiateReqDTO.setOrderItemId(flow.getOrderItemId());
        contractInitiateReqDTO.setRoomId(flow.getRoomId());
        contractInitiateReqDTO.setCustomerUserId(flow.getServiceId());
        contractInitiateReqDTO.setBuyerGuaranteeType(contractTypeInfo.getBuyerGuaranteeType());
        contractInitiateReqDTO.setPreDiscountPrice(preDiscountPrice);
        contractInitiateReqDTO.setDiscountPrice(discountPrice);
        contractInitiateReqDTO.setTradeContractType(contractTypeInfo.getTradeContractType());
        contractInitiateReqDTO.setProductUniqueNo(roomOrderDetails.getProductUniqueNo());
        contractInitiateReqDTO.setGameName(roomOrderDetails.getGameName());
        contractInitiateReqDTO.setTradeTime(roomOrderDetails.getCreateTime());
        contractInitiateReqDTO.setProductType(roomOrderDetails.getProductType());

        contractInitiateReqDTO.setAccountSource(deliveryInfo.getAccountSource());
        // 通行证
        String accountUid = deliveryInfo.getAccountUid();
        if (StringUtils.isEmpty(accountUid)) {
            contractInitiateReqDTO.setPassesNo(deliveryInfo.getGameAccount());
        } else {
            contractInitiateReqDTO.setPassesNo(accountUid);
        }

        Integer orderSource = roomOrderDetails.getOrderSource();
        if (OrderSourceEnum.ALIPAY.getLabel().equals(orderSource)) {
            contractInitiateReqDTO.setBusChannel(2);
        } else {
            contractInitiateReqDTO.setBusChannel(1);
        }
        List<RoomOrderDetailsRespDTO.IndemnityDetails> indemnityDetails = roomOrderDetails.getIndemnityDetails();

        List<ContractIndemnityDTO> contractIndemnityDTO =
            AutomationCallBackAppMapping.INSTANCE.toContractIndemnityDTO(indemnityDetails);
        contractInitiateReqDTO.setNormalIndemnity(contractIndemnityDTO.stream()
            .filter(x -> x.getIndemnityTypeLev1().equals(IndemnityTypeEnum.NORMAL_INDEMNITY.getCode())).findFirst()
            .get());
        contractInitiateReqDTO.setEnhancedIndemnityList(contractIndemnityDTO.stream()
            .filter(x -> x.getIndemnityTypeLev1().equals(IndemnityTypeEnum.ADVANCED_INDEMNITY.getCode())).toList());

        MerchantInfoRespDTO buyerInfo =
            merchantGateway.getMerchantInfo(flow.getBuyerId(), roomOrderDetails.getProductType());
        MerchantInfoRespDTO sellerInfo =
            merchantGateway.getMerchantInfo(flow.getSellerId(), roomOrderDetails.getProductType());
        contractInitiateReqDTO.setSellerType(sellerInfo.isMerchant() ? 2 : 1);
        contractInitiateReqDTO.setBuyerType(buyerInfo.isMerchant() ? 2 : 1);

        try {
            log.info("init contract，req:{}", JSONObject.toJSONString(contractInitiateReqDTO));
            mainContractAppService.initiateContract(contractInitiateReqDTO);
            return true;
        } catch (BusinessException businessException) {
            log.warn("Failed to init contract", businessException);
            return false;
        } catch (Exception e) {
            log.error("Failed to automatically create the contract", e);
            return false;
        }
    }

    /**
     * 合同结束
     *
     * @param flow
     */
    private boolean contractFinish(Flow flow) {
        // 买家资料已提交
        SaveFlowNodeLogBO buyerSubmitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT).build();
        // 合同发起日志
        flowDomainService.saveNodeLog(buyerSubmitNodeLogBO);
        // 卖家资料已提交
        SaveFlowNodeLogBO sellerSubmitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT).build();
        flowDomainService.saveNodeLog(sellerSubmitNodeLogBO);

        // 合同发起日志
        SaveFlowNodeLogBO contractInitNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.INIT_CONTRACT_FINISH).build();
        flowDomainService.saveNodeLog(contractInitNodeLogBO);

        // 合同冻结 通过
        SaveFlowNodeLogBO contractFreezePassNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.CONTRACT_FREEZE_PASS).build();
        flowDomainService.saveNodeLog(contractFreezePassNodeLogBO);

        // 合同签署通过
        SaveFlowNodeLogBO contractSignNodeLogBO =
            SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_FINISH).build();
        flowDomainService.saveNodeLog(contractSignNodeLogBO);

        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
        // 签署合同节点完成
        flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_FINISH.getNode());
        String flowNodeSnapshot =
            JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CONTRACT_DETAIL.buttons()));
        flow.setNodeSnapshoot(flowNodeSnapshot);
        return flowDomainService.updateNode(flow);

    }

    /**
     * 买卖双方提交合同资料
     *
     * @param dto
     * @return
     */
    public SingleResponse<Boolean> contractDataCommit(ContractDataCommitReqDTO dto) {
        String orderItemId = dto.getOrderItemId();
        log.info("提交合同资料回调，参数{},{}", orderItemId, dto.getUserType());

        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.CONTRACT_INFO_SUBMIT.concat(orderItemId + dto.getUserType()), "0",
                3, ChronoUnit.SECONDS);
        if (!b) {
            return null;
        }

        //尝试获取交付流程，分为智能交付与普通交付
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (ObjectUtils.isEmpty(flow)) {
            return SingleResponse.of(true);
        }

        if (!flow.getFlowNode().startsWith(FlowNodeEnum.SIGN_CONTRACT.getNode())) {
            return SingleResponse.of(true);
        }
        Integer userType = dto.getUserType();

        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId());

        boolean sellerSubmit = false;
        boolean buyerSubmit = false;

        List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);

        // 买家
        if (Objects.equals(1, userType)) {
            // 终止用户唤醒任务
            List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
            if (wakeUpTaskSnapshot == null) {
                wakeUpTaskSnapshot = new ArrayList<>();
            }
            deliveryDomainService.abortTask(wakeUpTaskSnapshot, FlowNodeEnum.SIGN_CONTRACT_BUYER_NOT_SUBMIT.getNode(),
                flow.getBuyerId());
            flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
            // 是否已提交
            for (FlowNodeLogRespBO bo : logList) {
                if (bo.getFlowNode().equals(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT.getNode())) {
                    buyerSubmit = true;
                }
                if (bo.getFlowNode().equals(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT.getNode())) {
                    sellerSubmit = true;
                }
            }
            // 未提交 保存买家提交资料日志
            if (!buyerSubmit) {
                SaveFlowNodeLogBO buyerSubmitNodeLogBO =
                    saveFlowNodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT)
                        .flowUserEnum(FlowUserEnum.BUYER).build();
                flowDomainService.saveNodeLog(buyerSubmitNodeLogBO);
                buyerSubmit = true;
                flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT.getNode());
            }
        }
        // 卖家
        if (Objects.equals(2, userType)) {
            // 终止用户唤醒任务
            List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
            if (wakeUpTaskSnapshot == null) {
                wakeUpTaskSnapshot = new ArrayList<>();
            }
            deliveryDomainService.abortTask(wakeUpTaskSnapshot, FlowNodeEnum.SIGN_CONTRACT_SELLER_NOT_SUBMIT.getNode(),
                flow.getSellerId());
            flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
            // 是否已提交
            for (FlowNodeLogRespBO bo : logList) {
                if (bo.getFlowNode().equals(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT.getNode())) {
                    sellerSubmit = true;
                }
                if (bo.getFlowNode().equals(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT.getNode())) {
                    buyerSubmit = true;
                }
            }
            if (!sellerSubmit) {
                SaveFlowNodeLogBO sellerSubmitNodeLogBO =
                    saveFlowNodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT)
                        .flowUserEnum(FlowUserEnum.SELLER).build();
                flowDomainService.saveNodeLog(sellerSubmitNodeLogBO);
                sellerSubmit = true;
                flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT.getNode());
            }

        }

        if (sellerSubmit && buyerSubmit) {
            List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
            dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
            DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(flow.getOrderItemId());
            List<String> buttons = new ArrayList<>();
            buttons.addAll(FlowNodeButtonsEnum.BUYER_INFO_DETAIL.buttons());
            buttons.addAll(FlowNodeButtonsEnum.SELLER_INFO_DETAIL.buttons());
            buttons.addAll(FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
            if (autoInitContract) {
                boolean success = initContract(flow, deliveryInfo);
                // 发起合同失败
                if (!success) {
                    dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_FAIL));
                    flow.setFlowNode(FlowNodeEnum.INIT_CONTRACT_FAIL.getNode());
                    FlowNodeRespDTO flowNodeRespDTO = new FlowNodeRespDTO(dtos, buttons);
                    flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
                    flowDomainService.updateNode(flow);
                    imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
                    return SingleResponse.of(null);
                }
                dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_WAIT));
                FlowNodeRespDTO flowNodeRespDTO =
                    new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
                flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
            } else {
                dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_WAIT));
                FlowNodeRespDTO flowNodeRespDTO = new FlowNodeRespDTO(dtos, buttons);
                flow.setNodeSnapshoot(JSONObject.toJSONString(flowNodeRespDTO));
            }
            flow.setFlowNode(FlowNodeEnum.INIT_CONTRACT_WAIT.getNode());
            flowDomainService.updateNode(flow);
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
            imMsgGateway.asyncSendRemindMsg(flow.getRoomId(), flow.getBuyerId());
            return SingleResponse.of(null);
        }
        List<String> buttons = new ArrayList<>();
        if (buyerSubmit) {
            buttons.addAll(FlowNodeButtonsEnum.BUYER_INFO_DETAIL.buttons());
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT));
        } else {
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_BUYER_NOT_SUBMIT));
            buttons.addAll(FlowNodeButtonsEnum.COLLECT_BUYER_INFO.buttons());
        }
        if (sellerSubmit) {
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT));
            buttons.addAll(FlowNodeButtonsEnum.SELLER_INFO_DETAIL.buttons());
        } else {
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_SELLER_NOT_SUBMIT));
            buttons.addAll(FlowNodeButtonsEnum.COLLECT_SELLER_INFO.buttons());
        }
        buttons.addAll(FlowNodeButtonsEnum.INITIATING_CONTRACT.buttons());
        flow.setNodeSnapshoot(JSONObject.toJSONString(new FlowNodeRespDTO(dtos, buttons)));
        flowDomainService.updateNode(flow);
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
        return SingleResponse.of(null);

    }

    /**
     * 合同风控放行
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> contractFreezePass(OrderItemIdReqDTO dto) {
        log.info("call back contract freeze pass,orderItemId:{} ", dto.getOrderItemId());
        //尝试获取交付流程，分为智能交付与普通交付
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());

        if (ObjectUtils.isEmpty(flow)) {
            return SingleResponse.of(true);
        }

        // 判断流程执行是否符合预定流程顺序
        if (!FlowNodeEnum.CONTRACT_FREEZE_REJECT.value().equals(flow.getFlowNode())) {
            return SingleResponse.of(true);
        }

        List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.CONTRACT_FREEZE_PASS));
        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH));
        // socket 通知前端最新的流程节点
        String snapshotJson =
            JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CONTRACT_DETAIL.buttons()));
        flow.setNodeSnapshoot(snapshotJson);

        //保存风控通过节点
        SaveFlowNodeLogBO nodeLogBO =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId())
                .flowNodeEnum(FlowNodeEnum.CONTRACT_FREEZE_PASS).flowUserEnum(FlowUserEnum.ADMIN).build();
        flowDomainService.saveNodeLog(nodeLogBO);
        flow.setFlowNode(FlowNodeEnum.CONTRACT_FREEZE_PASS.getNode());
        flowDomainService.updateNode(flow);

        //通过融云通知手机端通过接口更新节点信息
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

        // 合同是否签署
        contractFinishedDispose(flow);

        return SingleResponse.of(true);
    }

    /**
     * 合同签署状态判断
     *
     * @param flow
     */
    private void contractFinishedDispose(Flow flow) {
        //查看合同当前状态，进行相关处理
        ContractInfoRespDTO contractInfo = mainContractAppService.getContractInfo(flow.getOrderItemId());
        Integer contractStatus = contractInfo.getContractStatus();
        ContractStatusEnum statusEnumByCode = ContractStatusEnum.getStatusEnumByCode(contractStatus);

        //合同的签署状态判断
        switch (statusEnumByCode) {
            //已签署
            case FINISHED: {
                //签署成功后，进入下一节点
                OrderItemIdReqDTO orderItemIdReqDTO = new OrderItemIdReqDTO();
                orderItemIdReqDTO.setOrderItemId(flow.getOrderItemId());
                contractSignFinish(orderItemIdReqDTO);
                break;
            }
            //统一为待签署
            case PREPARING:
            case WAIT_SIGN:
            case SIGNING: {
                List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
                List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);
                dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH));
                flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH.value());
                String nodeSnapshotJson =
                    JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CONTRACT_DETAIL.buttons()));
                flow.setNodeSnapshoot(nodeSnapshotJson);
                flowDomainService.updateNode(flow);
                break;
            }
            case CANCELED: {
                break;
            }
        }
    }

    public SingleResponse<Boolean> contractInitFulfill(OrderItemIdReqDTO dto, Boolean freeze) {
        log.info("call back contract init full fill,orderItemId:{} ,freeze:{} ", dto.getOrderItemId(), freeze);
        boolean b = RedissonUtils.setIfAbsent(RedisConstants.CONTRACT_INFO_SUBMIT.concat(dto.getOrderItemId()), "0", 3,
            ChronoUnit.SECONDS);
        if (!b) {
            return null;
        }

        //尝试获取交付流程，分为智能交付与普通交付
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());

        if (ObjectUtils.isEmpty(flow)) {
            return SingleResponse.of(true);
        }

        if (!flow.getFlowNode().startsWith(FlowNodeEnum.SIGN_CONTRACT.getNode())) {
            return SingleResponse.of(true);
        }

        //买卖家提交合同资料可能在合同处随时提交，判断只有在交付处于对应节点时才会进行相关逻辑
        List<String> permitNodes =
            ListUtil.of(FlowNodeEnum.SIGN_CONTRACT.value(), FlowNodeEnum.INIT_CONTRACT_WAIT.value(),
                FlowNodeEnum.INIT_CONTRACT_FAIL.value(), FlowNodeEnum.CONTRACT_FREEZE_REJECT.value());
        if (!permitNodes.contains(flow.getFlowNode())) {
            return SingleResponse.of(true);
        }

        List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);
        boolean initContractFinish = false;
        // 是否存在合同已发起节点日志
        for (FlowNodeLogRespDTO flowNodeLogRespDTO : dtos) {
            if (flowNodeLogRespDTO.getFlowNode().equals(FlowNodeEnum.INIT_CONTRACT_FINISH.getNode())) {
                initContractFinish = true;
            }
        }

        if (!initContractFinish) {
            SaveFlowNodeLogBO nodeLogBO =
                SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                    .flowNodeEnum(FlowNodeEnum.INIT_CONTRACT_FINISH).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(nodeLogBO);
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_FINISH));
        }
        // 合同是否冻结
        if (freeze) {
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.CONTRACT_FREEZE_REJECT));
            flow.setFlowNode(FlowNodeEnum.CONTRACT_FREEZE_REJECT.value());
        } else {
            // 合同冻结节点通过
            SaveFlowNodeLogBO nodeLogBO =
                SaveFlowNodeLogBO.builder().orderItemId(flow.getOrderItemId()).flowId(flow.getFlowId())
                    .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.CONTRACT_FREEZE_PASS).build();
            flowDomainService.saveNodeLog(nodeLogBO);
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.CONTRACT_FREEZE_PASS));
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH));
            flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_NOT_FINISH.value());
        }

        flow.setNodeSnapshoot(
            JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CONTRACT_DETAIL.buttons())));

        flowDomainService.updateNode(flow);

        return SingleResponse.of(true);
    }

    /**
     * 合同已签署
     *
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> contractSignFinish(OrderItemIdReqDTO dto) {

        log.info("合同签署完成回调，{}", dto);
        //尝试获取交付流程，分为智能交付与普通交付
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());

        if (ObjectUtils.isEmpty(flow)) {
            return SingleResponse.of(true);
        }

        // 非合同节点 签署合同不处理
        if (!flow.getFlowNode().startsWith(FlowNodeEnum.SIGN_CONTRACT.getNode())) {
            return SingleResponse.of(true);
        }
        // 终止唤醒
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

        List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);

        boolean hasBuyerInfoDetailNode = false;
        boolean hasSellerInfoDetailNode = false;
        boolean hasInitContractNode = false;
        boolean hasContractFreezeNode = false;

        for (FlowNodeLogRespBO flowNodeLogRespBO : logList) {
            if (flowNodeLogRespBO.getFlowNode().equals(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT.getNode())) {
                hasBuyerInfoDetailNode = true;
            }
            if (flowNodeLogRespBO.getFlowNode().equals(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT.getNode())) {
                hasSellerInfoDetailNode = true;
            }
            if (flowNodeLogRespBO.getFlowNode().equals(FlowNodeEnum.INIT_CONTRACT_FINISH.getNode())) {
                hasInitContractNode = true;
            }
            if (flowNodeLogRespBO.getFlowNode().equals(FlowNodeEnum.CONTRACT_FREEZE_PASS.getNode())) {
                hasContractFreezeNode = true;
            }
        }

        if (!hasBuyerInfoDetailNode) {
            // 买家资料收集节点
            SaveFlowNodeLogBO flowNodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(dto.getOrderItemId())
                    .flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(flowNodeLogBO);
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_BUYER_SUBMIT));
        }

        if (!hasSellerInfoDetailNode) {
            // 买家资料收集节点
            SaveFlowNodeLogBO flowNodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(dto.getOrderItemId())
                    .flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(flowNodeLogBO);
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_SELLER_SUBMIT));
        }

        if (!hasInitContractNode) {
            // 买家资料收集节点
            SaveFlowNodeLogBO flowNodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(dto.getOrderItemId())
                    .flowNodeEnum(FlowNodeEnum.INIT_CONTRACT_FINISH).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(flowNodeLogBO);
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.INIT_CONTRACT_FINISH));
        }

        if (!hasContractFreezeNode) {
            // 买家资料收集节点
            SaveFlowNodeLogBO flowNodeLogBO =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(dto.getOrderItemId())
                    .flowNodeEnum(FlowNodeEnum.CONTRACT_FREEZE_PASS).flowUserEnum(FlowUserEnum.ADMIN).build();
            flowDomainService.saveNodeLog(flowNodeLogBO);
            dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.CONTRACT_FREEZE_PASS));
        }

        // 合同签订节点完成
        SaveFlowNodeLogBO flowNodeLogBO =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(dto.getOrderItemId())
                .flowNodeEnum(FlowNodeEnum.SIGN_CONTRACT_FINISH).flowUserEnum(FlowUserEnum.ADMIN).build();
        flowDomainService.saveNodeLog(flowNodeLogBO);

        dtos.add(new FlowNodeLogRespDTO(FlowNodeEnum.SIGN_CONTRACT_FINISH));
        String snapshotJson = JSONObject.toJSONString(new FlowNodeRespDTO(dtos));
        flow.setNodeSnapshoot(snapshotJson);
        flow.setFlowNode(FlowNodeEnum.SIGN_CONTRACT_FINISH.getNode());
        flowDomainService.updateNode(flow);

        // 通知订单【签署合同】
        orderDeliverNodeDomainService.addOrderDeliveryNode(AccountDeliverNodeNewEnum.SIGN_CONTRACT,
            flow.getOrderItemId());

        // 更新节点
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

        // 合同节点完成
        flowDomainService.finishNodeLog(flow.getFlowId(), flow.getOrderItemId(), FlowNodeEnum.SIGN_CONTRACT.getNode());

        // 开启换绑节点
        exchangeBindingAppService.startExchangeBinding(flow.getOrderItemId());

        return SingleResponse.of(true);
    }
}
