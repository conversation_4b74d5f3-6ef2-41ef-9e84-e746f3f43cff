package com.pxb7.mall.trade.ofs.app.delivery.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.dto.ImUserDTO;
import com.pxb7.mall.trade.ofs.app.delivery.automation.DeliveryEndAppService;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.DeliveryResultDTO;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryStatusChangeDTO;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryTypeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryInfoDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryProcessLogDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.DeliveryResultEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.messaging.MQProducer;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryResultDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.OrderItemDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessLog;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryResult;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.OrderItem;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;
import java.util.Objects;

@Service
@Slf4j
public class DeliveryResultAppService {

    @Resource
    private OrderItemDbRepository orderItemRepository;
    @Resource
    private DeliveryEndAppService deliveryEndAppService;
    @Resource
    private DeliveryInfoDomainService orderInfoDomainService;
    @Resource
    private DeliveryResultDbRepository deliveryResultRepository;
    @Resource
    private DeliveryProcessLogDomainService deliveryProcessLogDomainService;

    @Resource
    private MQProducer mqProducer;

    public void completed(String orderItemId) {
        boolean b = RedissonUtils.setIfAbsent(RedisConstants.DELIVERY_COMPLETED.concat(orderItemId), "0", 3,
            ChronoUnit.SECONDS);
        if (!b) {
            return;
        }

        ImUserDTO imUser = ImUserUtil.getImUser();

        LambdaQueryWrapper<DeliveryResult> qw = new LambdaQueryWrapper<>();
        qw.eq(DeliveryResult::getOrderItemId, orderItemId);
        qw.last("limit 1");
        DeliveryResult deliveryResult = deliveryResultRepository.getOne(qw);
        if (deliveryResult == null) {
            deliveryResult = new DeliveryResult();
            deliveryResult.setOrderItemId(orderItemId);
            deliveryResult.setOptCustomerId(imUser.getUserId());
            deliveryResult.setResult(DeliveryResultEnum.COMPLETE.getValue());
            deliveryResultRepository.save(deliveryResult);
        } else {
            LambdaUpdateWrapper<DeliveryResult> uw = new LambdaUpdateWrapper<>();
            uw.eq(DeliveryResult::getId, deliveryResult.getId());
            uw.set(DeliveryResult::getResult, DeliveryResultEnum.COMPLETE.getValue())
                .set(DeliveryResult::getOptCustomerId, imUser.getUserId());
            deliveryResultRepository.update(uw);
        }

        deliveryEndAppService.completed(orderItemId, imUser.getUserId(), imUser.getUserName());

        // 通知合同
//        DeliveryCompleteNotifyMessage deliveryCompleteNotifyMessage =
//            new DeliveryCompleteNotifyMessage().setOrderItemId(orderItemId).setOperatorId(imUser.getUserId())
//                .setFlowId(null).setLabel(2);
//        mqProducer.asyncSend(MqConstants.DELIVERY_COMPLETE_NOTIFY_CONTRACT_TOPIC,
//            MqConstants.DELIVERY_COMPLETE_NOTIFY_CONTRACT_TAG, deliveryCompleteNotifyMessage);

        // 交付状态变更
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO().setOrderItemId(orderItemId)
            .setStatus(DeliveryResultEnum.FINISH.getValue())
            .setOperatorId(imUser.getUserId());

        DeliveryProcessLog processInsLog = deliveryProcessLogDomainService.getProcessInsLog(orderItemId);
        if (processInsLog != null) {
            deliveryStatusChangeDTO.setVersion(DeliveryTypeEnum.AUTOMATION_V3.getCode());
        } else {
            DeliveryInfo deliveryInfo = orderInfoDomainService.getDeliveryInfoByOrderItemId(orderItemId);
            if (Objects.nonNull(deliveryInfo) && Objects.equals(DeliveryTypeEnum.AUTOMATION.getCode(), deliveryInfo.getDeliveryType())) {
                deliveryStatusChangeDTO.setVersion(DeliveryTypeEnum.AUTOMATION.getCode());
            } else {
                deliveryStatusChangeDTO.setVersion(DeliveryTypeEnum.ORDINARY.getCode());
            }
        }
        String msgId = mqProducer.send(MqConstants.DELIVERY_STATUS_CHANGE_TOPIC, deliveryStatusChangeDTO);
        log.info("[流程状态变更消息发送成功] msg:{} msgId:{}",deliveryStatusChangeDTO, msgId);


    }

    public void abort(String orderItemId) {
        LambdaUpdateWrapper<DeliveryResult> uw = new LambdaUpdateWrapper<>();
        uw.eq(DeliveryResult::getOrderItemId, orderItemId);
        uw.set(DeliveryResult::getResult, DeliveryResultEnum.ABORT.getValue());
        deliveryResultRepository.update(uw);

        // 交付状态变更
        DeliveryStatusChangeDTO deliveryStatusChangeDTO =
            new DeliveryStatusChangeDTO().setOrderItemId(orderItemId).setStatus(DeliveryResultEnum.ABORT.getValue())
                .setVersion(DeliveryTypeEnum.AUTOMATION_V3.getCode()).setOperatorId("0");
        String msgId = mqProducer.send(MqConstants.DELIVERY_STATUS_CHANGE_TOPIC, deliveryStatusChangeDTO);
        log.info("[流程状态变更消息发送成功] msg:{} msgId:{}",deliveryStatusChangeDTO, msgId);
    }

    public DeliveryResultDTO getDeliveryResult(String orderItemId) {
        LambdaQueryWrapper<DeliveryResult> wq = new LambdaQueryWrapper<>();
        wq.eq(DeliveryResult::getOrderItemId, orderItemId);
        wq.last("LIMIT 1");
        DeliveryResult deliveryResult = deliveryResultRepository.getOne(wq);

        DeliveryResultDTO result = new DeliveryResultDTO();
        if (deliveryResult == null) {
            LambdaQueryWrapper<OrderItem> qw = new LambdaQueryWrapper<>();
            qw.eq(OrderItem::getOrderItemId, orderItemId);
            qw.eq(OrderItem::getOrderItemStatus, 2);
            OrderItem order = orderItemRepository.getOne(qw);
            if (order == null) {
                return null;
            }
            String userId = ImUserUtil.getUserId();
            deliveryResult = new DeliveryResult();
            deliveryResult.setOrderItemId(orderItemId);
            deliveryResult.setCustomerId(userId);
            deliveryResult.setOptCustomerId("history");
            deliveryResult.setResult(DeliveryResultEnum.RUNNING.getValue());
            deliveryResultRepository.save(deliveryResult);
        }
        result.setOrderItemId(deliveryResult.getOrderItemId());
        result.setOptCustomerId(deliveryResult.getOptCustomerId());
        result.setResult(deliveryResult.getResult());
        result.setCustomerId(deliveryResult.getCustomerId());
        return result;
    }
}
