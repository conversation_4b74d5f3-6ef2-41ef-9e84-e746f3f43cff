package com.pxb7.mall.trade.ofs.app.contract.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: OrderItemEnhancedIndemnity.java
 * @description: 订单增值包赔
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/12/23 11:21
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class OrderItemEnhancedIndemnity implements Serializable {
    /**
     * 增值包赔id
     */
    private String enhancedIndemnityId;
    /**
     * 增值包赔一级类型
     *
     * @see com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeEnum
     */
    private Integer enhancedIndemnityTypeLev1;
    /**
     * 增值包赔二级类型
     *
     * @see com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeLev2Enum
     */
    private Integer enhancedIndemnityTypeLev2;
}
