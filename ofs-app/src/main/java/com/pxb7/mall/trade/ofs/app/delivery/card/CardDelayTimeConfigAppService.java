package com.pxb7.mall.trade.ofs.app.delivery.card;

import java.util.List;

import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ofs.app.delivery.mapping.CardDelayTimeConfigAppMapping;
import com.pxb7.mall.trade.ofs.app.delivery.model.CardDelayTimeConfigReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.model.CardDelayTimeConfigRespDTO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardDelayTimeConfigReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardDelayTimeConfigRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.CardDelayTimeConfigDomainService;

import jakarta.annotation.Resource;

/**
 * 卡片关联的倒计时app服务
 *
 * <AUTHOR>
 * @since 2025-02-13 10:57:34
 */
@Service
public class CardDelayTimeConfigAppService {

    @Resource
    private CardDelayTimeConfigDomainService cardDelayTimeConfigDomainService;

    public CardDelayTimeConfigRespDTO.DetailDTO findById(Long id) {
        CardDelayTimeConfigRespBO.DetailBO detailBO = cardDelayTimeConfigDomainService.findById(id);
        return CardDelayTimeConfigAppMapping.INSTANCE.cardDelayTimeConfigBO2DetailDTO(detailBO);
    }

    public List<CardDelayTimeConfigRespDTO.DetailDTO> list(CardDelayTimeConfigReqDTO.SearchDTO param) {
        CardDelayTimeConfigReqBO.SearchBO searchBO =
            CardDelayTimeConfigAppMapping.INSTANCE.cardDelayTimeConfigDTO2SearchBO(param);
        List<CardDelayTimeConfigRespBO.DetailBO> list = cardDelayTimeConfigDomainService.list(searchBO);
        return CardDelayTimeConfigAppMapping.INSTANCE.cardDelayTimeConfigBO2ListDTO(list);
    }

    public List<CardDelayTimeConfigRespDTO.DetailDTO> findListByCardConfigId(Long cardConfigId) {
        return CardDelayTimeConfigAppMapping.INSTANCE
            .cardDelayTimeConfigBO2ListDTO(cardDelayTimeConfigDomainService.findListByCardConfigId(cardConfigId));
    }

}
