package com.pxb7.mall.trade.ofs.app.contract.dto.response;

import com.pxb7.mall.trade.ofs.app.contract.dto.ContractFileDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractDataRespDTO.java
 * @description: 合同数据返回对象
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/8/27 14:44
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@Data
@Accessors(chain = true)
public class ContractDataRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4318463896280428322L;
    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 合同id
     */
    private String contractDataId;

    /**
     * 合同状态 0准备中 1待签署 2签署中 3已完成 4已取消
     */
    private Integer status;

    /**
     * 游戏名称
     */
    private String gameName;

    /**
     * 游戏账号
     */
    private String gameAccount;
    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品编号
     */
    private String productUniqueNo;

    /**
     * 是否是当前用户
     */
    private Boolean currentUser;

    /**
     * 业务类型 1：成品号 2：充值 3：金币 4：装备 5：初始号
     */
    private Integer businessType;

    /**
     * 合同文件（一条合同可能涉及到多个文件）
     */
    private List<ContractFileDTO> contractFile;

}
