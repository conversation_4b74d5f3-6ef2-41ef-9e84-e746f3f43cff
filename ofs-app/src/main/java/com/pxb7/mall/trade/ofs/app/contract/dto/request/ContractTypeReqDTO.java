package com.pxb7.mall.trade.ofs.app.contract.dto.request;

import com.pxb7.mall.trade.ofs.contract.client.enums.GuaranteeTypeEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractTypeReqDTO.java
 * @description: 合同类型请求参数
 * @author: guo<PERSON>qi<PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/5 15:21
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ContractTypeReqDTO implements Serializable {
    /**
     * 订单id
     */
    @NotBlank(message = "订单id不能为空")
    private String orderItemId;

    /**
     * 买家id
     */
    @NotBlank(message = "买家id不能为空")
    private String buyerId;

    /**
     * 卖家id
     */
    @NotBlank(message = "卖家id不能为空")
    private String sellerId;

    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 买家保障类型 1 包赔证明 2 交易合同
     */
    private Integer buyerGuaranteeType = GuaranteeTypeEnum.INDEMNITY_PROVE.getCode();
}
