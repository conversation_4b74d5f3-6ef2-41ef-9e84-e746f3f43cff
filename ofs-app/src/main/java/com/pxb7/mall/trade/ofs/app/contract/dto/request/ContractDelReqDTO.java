package com.pxb7.mall.trade.ofs.app.contract.dto.request;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/06/23 16:24
 **/
@Data
public class ContractDelReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4019487432978133927L;
    /**
     * 订单id
     */
    @NotBlank(message = "订单id不能为空")
    private String orderItemId;

    /**
     * 合约id
     */
    @NotBlank(message = "合同id不能为空")
    private String contractDataId;
}
