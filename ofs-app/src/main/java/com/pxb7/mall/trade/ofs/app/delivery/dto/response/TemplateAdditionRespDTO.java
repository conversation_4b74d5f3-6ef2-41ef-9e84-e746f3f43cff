package com.pxb7.mall.trade.ofs.app.delivery.dto.response;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TemplateAdditionRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8056020815108443750L;
    private String templateAdditionId;
    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 字段类型,text（文本）,integer（整数）,decimals（小数），ultiple（多选），single（单选）enum（枚举），img图片
     */
    private String fieldType;
    /**
     * 字段校验规则，只支持文本，整数和小数，根据类型判断，文本限制长度，小数整数限制大小区间
     */
    private String fieldAstrict;
    /**
     * 枚举json，分为展示值与传入值，参考[{"display":"男人", "value":"M"},{"display":"女人", "value":"F"}]
     */
    private String enumValues;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否需要账号绑定截图
     */
    private Boolean accountbinding;
    /**
     * 是否是唯一标识
     */
    private Boolean uid;

    private String sampleGraph;
}
