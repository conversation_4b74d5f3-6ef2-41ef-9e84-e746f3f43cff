package com.pxb7.mall.trade.ofs.app.delivery.service;

import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryInfoDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.DeliveryInfoReqDTO;
import com.pxb7.mall.trade.ofs.delivery.client.enums.DeliveryDataCodeEnum;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.BatchSetDeliveryDataReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.DeliveryDataSetReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryDataDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryProcessLogDomainService;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryProcessLog;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.pxb7.mall.trade.ofs.delivery.domain.service.DeliveryInfoDomainService;

import java.util.List;

@Service
public class DeliveryInfoAppService {
    @Resource
    private DeliveryInfoDbRepository deliveryInfoRepository;
    @Resource
    private DeliveryInfoDomainService deliveryInfoDomainService;
    @Resource
    private DeliveryDataDomainService deliveryDataDomainService;
    @Resource
    private DeliveryProcessLogDomainService deliveryProcessLogDomainService;

    public void saveDeliveryInfo(DeliveryInfoReqDTO reqDTO) {
        String orderItemId = reqDTO.getOrderItemId();
        DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(orderItemId);
        if (deliveryInfo == null) {
            deliveryInfo = new DeliveryInfo();
            deliveryInfo.setOrderItemId(reqDTO.getOrderItemId());
            deliveryInfo.setAccountSource(reqDTO.getAccountSource());
            deliveryInfo.setAccountUid(reqDTO.getAccountUid());
            deliveryInfo.setGameAccount(reqDTO.getGameAccount());
            deliveryInfo.setRepackage(reqDTO.isRepackage());
            deliveryInfoRepository.save(deliveryInfo);
            return;
        }
        if (reqDTO.getAccountSource() != null) {
            deliveryInfo.setAccountSource(reqDTO.getAccountSource());
        }
        if (reqDTO.getAccountUid() != null) {
            deliveryInfo.setAccountUid(reqDTO.getAccountUid());
        }
        if (reqDTO.getGameAccount() != null) {
            deliveryInfo.setGameAccount(reqDTO.getGameAccount());
        }
        deliveryInfo.setRepackage(reqDTO.isRepackage());

        deliveryInfoRepository.lambdaUpdate().eq(DeliveryInfo::getOrderItemId, orderItemId).update(deliveryInfo);
    }

    public DeliveryInfoDTO getDeliveryInfo(String orderItemId) {
        return deliveryInfoDomainService.getDeliveryInfo(orderItemId);
    }

    public void updateRepackage(String orderItemId, boolean repackage) {
        DeliveryProcessLog processInsLog = deliveryProcessLogDomainService.getProcessInsLog(orderItemId);
        if (processInsLog != null) {
            BatchSetDeliveryDataReqBO batchSetDeliveryDataReqBO = new BatchSetDeliveryDataReqBO();
            batchSetDeliveryDataReqBO.setProcessInsId(processInsLog.getProcessInsId()).setOrderItemId(orderItemId);
            DeliveryDataSetReqBO deliveryDataSetReqBO = new DeliveryDataSetReqBO();
            deliveryDataSetReqBO.setCode(DeliveryDataCodeEnum.repackage.getCode()).setValue(String.valueOf(repackage));
            batchSetDeliveryDataReqBO.setDataBatch(List.of(deliveryDataSetReqBO));
            deliveryDataDomainService.batchSetDeliveryData(batchSetDeliveryDataReqBO);
            return;
        }

        DeliveryInfo deliveryInfo = deliveryInfoRepository.getDeliveryInfo(orderItemId);
        if (deliveryInfo == null) {
            deliveryInfo = new DeliveryInfo();
            deliveryInfo.setOrderItemId(orderItemId);
            deliveryInfo.setRepackage(repackage);
            deliveryInfoRepository.save(deliveryInfo);
            return;
        }
        deliveryInfoRepository.updateRepackage(orderItemId, repackage);
    }
}
