package com.pxb7.mall.trade.ofs.app.delivery.dto.request;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class TaskCallBackReqDTO {

    /**
     * 流程实例ID
     */
    private String procInsId;

    /**
     * 任务定义ID
     */
    private String taskDefKey;

    /**
     * 任务实例ID
     */
    private String taskId;

    /**
     * 1：流程开启，2：流程结束，3：任务执行开始，4：任务执行结束，5：take；6：任务开始，7：任务结束，8：任务分配
     */
    private Integer eventType;

    /**
     * 业务key
     */
    private String businessKey;
}
