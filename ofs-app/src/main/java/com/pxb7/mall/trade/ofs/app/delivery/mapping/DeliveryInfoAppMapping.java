package com.pxb7.mall.trade.ofs.app.delivery.mapping;

import com.pxb7.mall.trade.ofs.contract.domain.model.DeliveryInfoBO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.model.DeliveryInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DeliveryInfoAppMapping {
    DeliveryInfoAppMapping INSTANCE = Mappers.getMapper(DeliveryInfoAppMapping.class);
    DeliveryInfoBO deliveryInfoDTO2BO(DeliveryInfoDTO  source);
}
