package com.pxb7.mall.trade.ofs.app.contract.v3.service;

import com.pxb7.mall.trade.ofs.app.contract.v3.mapping.AgreementVendorAppMapping;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.v3.AgreementVendorInfoReqDTO;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.v3.AgreementVendorInfoRespDTO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.req.AgreementVendorInfoReqBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.resp.AgreementVendorInfoRespBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.service.CrtSignProcessDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AgreementVendorAppSerivce.java
 * @description: 合同卖家服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/2/24 15:59
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class AgreementVendorAppService {
    @Resource
    private CrtSignProcessDomainService crtSignProcessDomainService;

    public List<AgreementVendorInfoRespDTO> checkAgreementVendorInfo(AgreementVendorInfoReqDTO agreementVendorInfoReqDTO) {
        AgreementVendorInfoReqBO agreementVendorInfoReqBO = AgreementVendorAppMapping.INSTANCE.toAgreementVendorInfoReqBO(agreementVendorInfoReqDTO);
        List<AgreementVendorInfoRespBO> agreementVendorRespList = crtSignProcessDomainService.getAgreementVendorInfo(agreementVendorInfoReqBO);
        return AgreementVendorAppMapping.INSTANCE.toAgreementVendorInfoRespDTOList(agreementVendorRespList);
    }
}
