package com.pxb7.mall.trade.ofs.app.contract.v3.service;

import com.pxb7.mall.trade.ofs.app.contract.v3.dto.request.SignContractReqDTO;
import com.pxb7.mall.trade.ofs.common.config.util.UserUtil;
import com.pxb7.mall.trade.ofs.contract.domain.v3.service.CrtInformationDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: SellerInformationAppService.java
 * @description: 卖家资料服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/2/17 16:15
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class SellerInformationAppService {
    @Resource
    private CrtInformationDomainService crtInformationDomainService;
    @Resource
    private ContractProcessNodeAppService contractProcessNodeAppService;

    public Boolean isSubmittedInformation(String orderItemId) {
        SignContractReqDTO reqDTO = new SignContractReqDTO();
        reqDTO.setOrderItemId(orderItemId);
        contractProcessNodeAppService.validation(reqDTO);

        return crtInformationDomainService.isSubmittedInformation(orderItemId, UserUtil.getUserId());
    }
}
