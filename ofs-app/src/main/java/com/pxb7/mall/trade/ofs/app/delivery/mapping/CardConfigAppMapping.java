package com.pxb7.mall.trade.ofs.app.delivery.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.app.delivery.model.CardConfigReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.model.CardConfigRespDTO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardConfigReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardConfigRespBO;

@Mapper
public interface CardConfigAppMapping {

    CardConfigAppMapping INSTANCE = Mappers.getMapper(CardConfigAppMapping.class);

    CardConfigReqBO.SearchBO cardConfigDTO2SearchBO(CardConfigReqDTO.SearchDTO source);

    CardConfigRespDTO.DetailDTO cardConfigBO2DetailDTO(CardConfigRespBO.DetailBO source);

    List<CardConfigRespDTO.DetailDTO> cardConfigBO2ListDTO(List<CardConfigRespBO.DetailBO> source);

}
