package com.pxb7.mall.trade.ofs.app.contract.service;

import static java.lang.Boolean.TRUE;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.pxb7.mall.trade.ofs.contract.domain.v3.CrtStatusChangeNotifyEvent;
import com.pxb7.mall.trade.order.client.dto.response.order.OrderDeliveryInfoRespDTO;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.im.client.dto.request.SendReminderMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.merchant.client.dto.response.account.MerchantAccountIdentityRespDTO;
import com.pxb7.mall.merchant.client.dto.response.merchant.MerchantBaseInfoRespDTO;
import com.pxb7.mall.product.client.api.GameServiceI;
import com.pxb7.mall.product.client.api.ProductServiceI;
import com.pxb7.mall.product.client.dto.response.game.GameBaseDTO;
import com.pxb7.mall.product.client.dto.response.product.ProductRespDTO;
import com.pxb7.mall.trade.ofs.app.config.ContractConfig;
import com.pxb7.mall.trade.ofs.app.contract.dto.*;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.*;
import com.pxb7.mall.trade.ofs.app.contract.dto.response.*;
import com.pxb7.mall.trade.ofs.app.contract.mapping.ContractDataAppMapping;
import com.pxb7.mall.trade.ofs.app.contract.v3.service.AgreementManualAppService;
import com.pxb7.mall.trade.ofs.app.delivery.automation.ContractAppService;
import com.pxb7.mall.trade.ofs.app.delivery.service.DeliveryInfoAppService;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.enums.ClientTypeEnum;
import com.pxb7.mall.trade.ofs.common.config.enums.ContractStatusEnum;
import com.pxb7.mall.trade.ofs.common.config.enums.GuaranteeTypeEnum;
import com.pxb7.mall.trade.ofs.common.config.enums.TradeTypeEnum;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.*;
import com.pxb7.mall.trade.ofs.contract.client.dto.enumns.ContractTypeEnum;
import com.pxb7.mall.trade.ofs.contract.client.dto.request.*;
import com.pxb7.mall.trade.ofs.contract.client.dto.response.*;
import com.pxb7.mall.trade.ofs.contract.domain.model.*;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractCardDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractDesensitizationDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractDomainService;
import com.pxb7.mall.trade.ofs.contract.domain.service.ContractMaterialDomainService;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractType;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.ContractUserType;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.SignedStatus;
import com.pxb7.mall.trade.ofs.contract.infra.eunms.SysUserType;
import com.pxb7.mall.trade.ofs.contract.infra.model.ContractByGoodsLogReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.ImMessageGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.MerchantGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.UserGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.DownloadContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.model.request.fdd.SmsReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FddGateway;
import com.pxb7.mall.trade.ofs.contract.infra.remote.service.http.FeiShuGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.*;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.*;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.MerchantContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.request.UserContractReqPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.model.response.BuyerIndemnityContractRespPO;
import com.pxb7.mall.trade.ofs.contract.infra.repository.es.model.request.ContractDataReqPO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.DeliveryInfoReqDTO;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.OrderItemIdReqDTO;
import com.pxb7.mall.trade.order.client.api.IndemnityDubboServiceI;
import com.pxb7.mall.trade.order.client.api.OrderItemIndemnityDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.indemnity.ContractIndemnityInfoDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderItemAmountRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.TradeIdentityRespDTO;
import com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeEnum;
import com.pxb7.mall.trade.order.client.enums.indemnity.IndemnityTypeLev2Enum;
import com.pxb7.mall.trade.order.client.enums.order.IndemnityStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple6;
import io.vavr.control.Option;
import io.vavr.control.Try;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 主合同
 *
 * <AUTHOR>
 * @since: 2024-08-02 13:44
 **/
@Service
@Slf4j
public class MainContractAppService {
    @Resource
    private ContractAppService contractAppService;
    @Resource
    private AgreementManualAppService agreementManualAppService;
    @Resource
    private ContractUserAppService contractUserAppService;
    @Resource
    private RiskControlAppService riskControlAppService;
    @Resource
    private ContractFlowAppService contractFlowAppService;
    @Resource
    private ContractRelationAppService contractRelationAppService;
    @Resource
    private DeliveryInfoAppService deliveryInfoAppService;
    @Resource
    private ContractDomainService contractDomainService;
    @Resource
    private ContractCardDomainService contractCardDomainService;
    @Resource
    private ContractMaterialDomainService contractMaterialDomainService;
    @Resource
    private ContractDesensitizationDomainService desensitizationDomainService;
    @Resource
    private ContractConfig contractConfig;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private ContractDataRepository contractDataRepository;
    @Resource
    private ContractSignInfoRepository contractSignInfoRepository;
    @Resource
    private ContractFileRepository contractFileRepository;
    @Resource
    private ContractSellerInfoRepository contractSellerInfoRepository;
    @Resource
    private ContractByGoodsLogRepository contractByGoodsLogRepository;
    @Resource
    private ContractSignChannelRepository contractSignChannelRepository;
    @Resource
    private ContractSignatoryUserRepository contractSignatoryUserRepository;
    @DubboReference(providedBy = "product-c")
    private GameServiceI gameServiceI;
    @DubboReference(providedBy = "product-c")
    private ProductServiceI productServiceI;
    @DubboReference(providedBy = "order")
    private OrderItemIndemnityDubboServiceI orderItemIndemnityDubboServiceI;
    @DubboReference(providedBy = "order")
    private IndemnityDubboServiceI indemnityDubboServiceI;
    @Resource
    private FddGateway fddGateway;
    @Resource
    private ImMessageGateway imMessageGateway;
    @Resource
    private FeiShuGateway feiShuGateway;
    @Resource
    private UserGateway userGateway;
    @Resource
    private MerchantGateway merchantGateway;
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;
    @Resource
    private CrtStatusChangeNotifyEvent crtStatusChangeNotifyEvent;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public String initiateContract(ContractInitiateReqDTO contractInitiateReqDTO) {
        //2.0合同入口
        //return createAgreement(contractInitiateReqDTO);
        //3.0合同流程入口
        return agreementManualAppService.manualCreateAgreement(contractInitiateReqDTO);
    }

    public String createAgreement(ContractInitiateReqDTO contractInitiateReqDTO) {
        Tuple6<String, String, String, ContractIndemnityDTO, Long, Long> tuple = Tuple.of(contractInitiateReqDTO.getOrderItemId()
            .trim(), contractInitiateReqDTO.getBuyerId(), contractInitiateReqDTO.getSellerId(), contractInitiateReqDTO.getNormalIndemnity(), contractInitiateReqDTO.getPreDiscountPrice(), contractInitiateReqDTO.getDiscountPrice());

        boolean b = RedissonUtils.setIfAbsent(String.format(RedisConstants.INITIATE_CONTRACT, tuple._1), "0", 6, ChronoUnit.SECONDS);
        if (!b) {
            throw new BizException(ErrorCode.REPEAT_INITIATE_CONTRACT.getErrCode(), String.format(ErrorCode.REPEAT_INITIATE_CONTRACT.getErrDesc(), tuple._1));
        }

        // 非交易中的订单无法发起合同
        OrderInfoDubboRespDTO orderInfo = orderItemDubboGateway.getOrderInfo(tuple._1);
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new BusinessException(ErrorCode.ORDER_NOT_EXISTS);
        }
        var orderStatus = orderInfo.getOrderItemStatus();
        if (!OrderItemStatusEnum.DEALING.getValue().equals(orderStatus)) {
            throw new BusinessException(ErrorCode.CONTRACT_ORDER_NOT_IN_TRANSACTION);
        }
        // 订单是否有在途资金单据（收款单/退款单）
        Boolean isExistsFundsInTransitReceipt = orderItemDubboGateway.isExistsFundsInTransitReceipt(tuple._1);
        if (isExistsFundsInTransitReceipt) {
            throw new BusinessException(ErrorCode.CONTRACT_EXIST_TRANSIT_FUND_VOUCHER);
        }
        // 当前订单是否存在合同
        ContractData contractDataList = contractDataRepository.getContractDataWithOrder(tuple._1);
        if (!Objects.isNull(contractDataList)) {
            throw new BusinessException(ErrorCode.CONTRACT_ALREADY_EXIST);
        }
        // 订单是否有包赔
        if (ObjectUtil.isEmpty(tuple._4)) {
            throw new BusinessException(ErrorCode.CONTRACT_NO_COMPENSATION);
        }
        GuaranteeTypeEnum guaranteeTypeEnum = GuaranteeTypeEnum.getGuaranteeNameByCode(contractInitiateReqDTO.getBuyerGuaranteeType());
        TradeTypeEnum tradeTypeEnum = TradeTypeEnum.getTradeTypeEnumByCode(contractInitiateReqDTO.getTradeContractType());
        //checkSellerAndBuyer
        contractMaterialDomainService.checkUserInfoOrSignatory(guaranteeTypeEnum, tradeTypeEnum, tuple._2, tuple._3, tuple._1);
        //checkCertInfo
        Tuple2<BuyerInfoDTO, SellerInfoDTO> certInfoRes = checkUserCertInfo(contractInitiateReqDTO);

        String contractId = IdGenUtil.getId();
        // 风控
        ContractRiskReqDTO contractRiskReqDTO = ContractDataAppMapping.INSTANCE.transformContractRiskReqDTO(contractInitiateReqDTO);
        contractRiskReqDTO.setContractId(contractId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean freeze = riskControlAppService.contractRisk(contractRiskReqDTO, certInfoRes);
        stopWatch.stop();
        log.warn(String.format("合同风控校验执行花费时间 {%d}ms，当前合同【%s】风控结果为【%b】", stopWatch.getTotalTimeMillis(), contractId, freeze));

        // 组装主合同信息
        ContractMainInfoBO contractMainInfoBO = ContractDataAppMapping.INSTANCE.toMainBO(contractInitiateReqDTO);
        buildMainContractInfo(contractMainInfoBO, contractId, tuple._1, contractInitiateReqDTO, freeze, certInfoRes);
        contractMainInfoBO.setFreeze(freeze);

        List<ContractIndemnityBO> contractIndemnityBOList = new ArrayList<>();
        List<String> indemnityIdList = new ArrayList<>();

        indemnityIdList.add(contractInitiateReqDTO.getNormalIndemnity().getIndemnityId());
        contractIndemnityBOList.add(ContractDataAppMapping.INSTANCE.toContractIndemnityBO(contractInitiateReqDTO.getNormalIndemnity()));

        if (CollUtil.isNotEmpty(contractInitiateReqDTO.getEnhancedIndemnityList())) {
            contractInitiateReqDTO.getEnhancedIndemnityList().forEach(data -> {
                indemnityIdList.add(data.getIndemnityId());
                contractIndemnityBOList.add(ContractDataAppMapping.INSTANCE.toContractIndemnityBO(data));
            });
        }
        contractMainInfoBO.setIndemnityIds(indemnityIdList);

        //推送合同生成文件消息
        ContractInitiateReqBO reqBO = ContractDataAppMapping.INSTANCE.toTransmitDto(contractInitiateReqDTO);
        reqBO.setFreeze(freeze);
        reqBO.setContractIndemnityInfos(contractIndemnityBOList);
        CompletableFuture.runAsync(() -> {
            transactionTemplate.executeWithoutResult(transactionStatus -> {
                try {
                    contractDomainService.addContractMainInfo(contractMainInfoBO, contractInitiateReqDTO.getBusChannel());
                    //更新账号信息
                    DeliveryInfoReqDTO deliveryInfoReqDTO = ContractDataAppMapping.INSTANCE.transformDeliveryInfoReqDTO(contractInitiateReqDTO);
                    deliveryInfoAppService.saveDeliveryInfo(deliveryInfoReqDTO);
                    //发起合同流程
                    contractFlowAppService.startContractFlow(contractId, reqBO);
                    //无交易合同通知交付环节节点更新
                    handleAutomationCallback(contractInitiateReqDTO);
                } catch (Exception e) {
                    log.error("合同生成失败，失败原因：", e);
                    String errorMsg = e.getMessage();
                    sendImTextInfo(reqBO);
                    sendReminderInfo(reqBO);
                    sendFeiShuMsg(reqBO, errorMsg);
                    throw e;
                }
                //通知流程节点更新
                handleDeliveryFlow(contractId, tuple._1, reqBO);
            });
        });
        return "success";
    }

    private void handleDeliveryFlow(String contractId,
                                    String orderItemId,
                                    ContractInitiateReqBO contractInitiateReqBO) {
        try {
            //通知交付流程合同初始化状态
            OrderItemIdReqDTO orderItemIdReqDTO = new OrderItemIdReqDTO();
            orderItemIdReqDTO.setOrderItemId(orderItemId);
            contractAppService.contractInitFulfill(orderItemIdReqDTO, contractInitiateReqBO.getFreeze());
            if (contractInitiateReqBO.getBuyerGuaranteeType() == 1 && contractInitiateReqBO.getTradeContractType() == 20) {
                contractAppService.contractSignFinish(orderItemIdReqDTO);
            }
            //通知交易中心合同已经生成完毕
            contractDomainService.notifyContractFlowNode(orderItemId, contractInitiateReqBO.getRoomId(), contractInitiateReqBO.getCustomerUserId(), true);
        } catch (Exception e) {
            log.error("通知流程节点更新失败,订单id：{},合同id:{}", orderItemId, contractId, e);
        }
    }

    /**
     * 发送催一催消息
     *
     * @param reqBO
     */
    private void sendReminderInfo(ContractInitiateReqBO reqBO) {
        Supplier<SendReminderMsgReqDTO> supplierReminder = () -> {
            SendReminderMsgReqDTO sendReminderMsgReqDTO = new SendReminderMsgReqDTO();
            sendReminderMsgReqDTO.setGroupId(reqBO.getRoomId());
            sendReminderMsgReqDTO.setUserId(reqBO.getBuyerId());
            return sendReminderMsgReqDTO;
        };
        imMessageGateway.sendReminderMsg(supplierReminder);
    }

    /**
     * 发送文本消息
     *
     * @param reqBO
     */
    private void sendImTextInfo(ContractInitiateReqBO reqBO) {
        Supplier<SendTextMsgReqDTO> supplierText = () -> {
            SendTextMsgReqDTO sendTextMsgReqDTO = new SendTextMsgReqDTO();
            sendTextMsgReqDTO.setTargetId(reqBO.getRoomId());
            sendTextMsgReqDTO.setFromUserId(reqBO.getCustomerUserId());
            sendTextMsgReqDTO.setContent("合同生成失败!");
            return sendTextMsgReqDTO;
        };
        imMessageGateway.sendTextMessage(supplierText);
    }

    /**
     * 发送飞书消息
     *
     * @param reqBO
     * @param errMsg 错误消息
     * @return
     */
    private String sendFeiShuMsg(ContractInitiateReqBO reqBO, String errMsg) {
        StringBuilder sbd = new StringBuilder();
        sbd.append("合同发起失败：")
            .append("\n")
            .append("失败原因：")
            .append(errMsg)
            .append("\n")
            .append("订单号：")
            .append(reqBO.getOrderItemId())
            .append("\n")
            .append("商品编号：")
            .append(reqBO.getProductUniqueNo())
            .append("\n")
            .append("游戏名称：")
            .append(reqBO.getGameName())
            .append("\n")
            .append("通行证账号：")
            .append(reqBO.getPassesNo())
            .append("\n");
        return feiShuGateway.sendFeiShuMsg(sbd.toString());
    }

    /**
     * 合同列表
     *
     * @param req
     * @return
     */
    public Page<ContractListRespDTO> pageQueryContractData(ContractDataReqDTO req) {
        List<ContractListRespDTO> result = new ArrayList<>();
        ContractDataReqPO reqPO = ContractDataAppMapping.INSTANCE.transformContractDataReqDTO(req);
        Set<String> userIds = new HashSet<>();
        String loginUserId = UserUtil.getUserId();
        Boolean isMerchant = UserUtil.isMerchant();
        if (isMerchant) { //号商
            reqPO.setUserType(SysUserType.MERCHANT.getCode());
            MerchantAccountIdentityRespDTO merchantAccountIdentityRespDTO = merchantGateway.getMerchantAccountIdentity(loginUserId);
            userIds.addAll(new HashSet<>(merchantAccountIdentityRespDTO.getUserIds()));
        } else { //散户
            reqPO.setUserType(SysUserType.RETAIL.getCode());
            userIds.add(loginUserId);
        }
        reqPO.setUserIds(userIds);
        if (CollUtil.isEmpty(userIds)) {
            return new Page<>();
        }
        //自己的合同签署信息列表、一定是交易合同【买家、卖家、双方】(包含未签署的合同)
        Set<String> ownerContractIds = new HashSet<>();
        List<ContractSignInfo> contractSignInfoList = contractDomainService.getOwnerContractSignList(userIds, req.getCurrentUser(), req.getCreateStartTime(), req.getCreateEndTime());
        List<String> ownerTradeContractIds = new ArrayList<>();
        contractSignInfoList.forEach(data -> {
            if (data.getSignedStatus() == 0) { //未签署
                if (ObjectUtil.isNotEmpty(req.getCurrentUser())) {
                    if (req.getCurrentUser()) {
                        ownerTradeContractIds.add(data.getContractDataId());
                    }
                } else {
                    ownerContractIds.add(data.getContractDataId());
                }
            } else {
                ownerContractIds.add(data.getContractDataId());
            }
        });
        ownerContractIds.addAll(ownerTradeContractIds);
        //如果是买家，需要查询下合同类型是包赔证明的合同信息(此时不存在签署信息)
        List<BuyerIndemnityContractRespPO> ownerIndemnityContractList = new ArrayList<>();
        if (ObjectUtil.isNull(req.getCurrentUser())) {
            ownerIndemnityContractList = contractDomainService.getOwnerIndemnityContractList(reqPO);
            Set<String> ownerIndemnityContractIds = ownerIndemnityContractList.stream()
                .map(BuyerIndemnityContractRespPO::getContractDataId)
                .collect(Collectors.toSet());

            Map<String /*contractId*/, Integer /*contractStatus*/> indemnityRelatedCrtactStatusMap = new HashMap<>();
            Optional.of(ownerIndemnityContractList)
                .orElse(Collections.emptyList())
                .forEach(data -> indemnityRelatedCrtactStatusMap.put(data.getContractDataId(), data.getStatus()));
            reqPO.setIndemnityRelatedCrtactStatusMap(indemnityRelatedCrtactStatusMap);
            ownerContractIds.addAll(ownerIndemnityContractIds);
        }

        if (CollUtil.isEmpty(ownerContractIds)) {
            return new Page<>();
        }
        //号商数据超过1W条限制，取前1W条
        Set<String> limitOwnerContractIds = ownerContractIds.stream().limit(10000).collect(Collectors.toSet());
        reqPO.setContractIds(limitOwnerContractIds);
        Page<ContractData> dataList = contractDataRepository.selectList(reqPO);

        if (CollUtil.isNotEmpty(dataList.getRecords())) {
            List<ContractData> contractDataList = dataList.getRecords();
            // 根据合同文件id进行分组
            Map<String, ContractSignInfo> signInfoMap = contractSignInfoList.stream()
                .collect(Collectors.toMap(ContractSignInfo::getContractDataId, Function.identity()));

            Map<String, List<BuyerIndemnityContractRespPO>> buyerIndemnityMap = ownerIndemnityContractList.stream()
                .collect(Collectors.groupingBy(BuyerIndemnityContractRespPO::getContractDataId));

            result = contractDataList.stream().map(contractData -> {
                ContractListRespDTO respDTO = ContractDataAppMapping.INSTANCE.toContractListRespDTO(contractData);
                if (reqPO.getUserType().equals(SysUserType.MERCHANT.getCode()) && StrUtil.isNotBlank(loginUserId)) {
                    if (StrUtil.equals(loginUserId, contractData.getBuyerId())) {
                        respDTO.setMerchantSubAccount(contractData.getBuyerPhone());
                    } else if (StrUtil.equals(loginUserId, contractData.getSellerId())) {
                        respDTO.setMerchantSubAccount(contractData.getSellerPhone());
                    }
                }

                ContractSignInfo contractSignInfo = signInfoMap.getOrDefault(contractData.getContractDataId(), null);
                //首先判断是否交易合同，有的话优先返回交易合同签署信息
                if (ObjUtil.isNotNull(contractSignInfo)) {
                    ContractSignInfoDTO ownerSignInfoDTO = ContractDataAppMapping.INSTANCE.toSignInfoDto(contractSignInfo);
                    if (contractSignInfo.getContractDataId()
                        .length() >= 15 && contractSignInfo.getSignedStatus() == 0 && LocalDateTime.now()
                        .minusHours(24)
                        .isAfter(contractSignInfo.getCreateTime())) {
                        ownerSignInfoDTO.setSignUrl(updateSignUrl(contractSignInfo));
                    }
                    ContractFile contractFile = contractFileRepository.getById(contractSignInfo.getContractFileId());
                    respDTO.setDocumentViewUrl(Objects.nonNull(contractFile) ? contractFile.getDocumentViewUrl() : "");
                    respDTO.setSignedFinishTime(ownerSignInfoDTO.getSignedFinishTime());
                    respDTO.setContractType(GuaranteeTypeEnum.TRADE_CONTRACT.getCode());
                    respDTO.setContractSignInfo(ownerSignInfoDTO);
                    if (ObjectUtil.isNotEmpty(req.getCurrentUser())) {
                        respDTO.setCurrentUser(req.getCurrentUser());
                    } else {
                        respDTO.setCurrentUser(contractSignInfo.getSignedStatus() == 0);
                    }
                } else {
                    //没有交易合同签署信息，则判断是否包赔证明合同
                    List<BuyerIndemnityContractRespPO> buyerIndemnityCrtList = buyerIndemnityMap.getOrDefault(contractData.getContractDataId(), Collections.emptyList());
                    if (CollUtil.isNotEmpty(buyerIndemnityCrtList)) {
                        Integer contractStatus = respDTO.getStatus();
                        respDTO.setContractType(GuaranteeTypeEnum.INDEMNITY_PROVE.getCode());
                        respDTO.setSignedFinishTime(buyerIndemnityCrtList.get(0).getUpdateTime());
                        respDTO.setDocumentViewUrl(buyerIndemnityCrtList.get(0).getDocumentViewUrl());
                        respDTO.setStatus(Objects.equals(respDTO.getStatus(), ContractStatusEnum.SIGNING.getCode())
                            ? ContractStatusEnum.FINISHED.getCode()
                            : contractStatus);
                        respDTO.setCurrentUser(true);
                    }
                }
                return respDTO;
            }).toList();
        }
        return new Page<ContractListRespDTO>(dataList.getCurrent(), dataList.getSize(), dataList.getTotal()).setRecords(result);
    }

    /**
     * 合同详情
     *
     * @param contractDataId 合同id
     * @return 合同详情
     */
    public ContractDetailRespDTO contractDetail(String contractDataId) {
        String userId = StrUtil.isNotBlank(LoginUserUtil.getUserId())
            ? LoginUserUtil.getUserId()
            : MerchantUserUtil.getUserId();
        boolean isMerchant = StrUtil.isBlank(LoginUserUtil.getUserId()) && StrUtil.isNotBlank(MerchantUserUtil.getUserId());
        boolean isBuyer;
        // 查询合同主数据
        ContractData contractData = contractDataRepository.findById(contractDataId);
        if (ObjUtil.isEmpty(contractData)) {
            return null;
        }
        ContractDetailRespDTO respDTO = ContractDataAppMapping.INSTANCE.toContractDetailRespDTO(contractData);
        if (isMerchant) {
            //号商
            MerchantAccountIdentityRespDTO merchantAccountIdentityRespDTO = merchantGateway.getMerchantAccountIdentity(MerchantUserUtil.getUserId());
            Set<String> subAccountUserIds = new HashSet<>(merchantAccountIdentityRespDTO.getUserIds());
            if (!subAccountUserIds.contains(userId)) {
                throw new BizException("当前登录子账号不在该号商下面，无权限查看详情");
            }
            if (StrUtil.equals(userId, contractData.getBuyerId())) {
                respDTO.setMerchantSubAccount(contractData.getBuyerPhone());
            } else if (StrUtil.equals(userId, contractData.getSellerId())) {
                respDTO.setMerchantSubAccount(contractData.getSellerPhone());
            }
            isBuyer = subAccountUserIds.contains(contractData.getBuyerId());
        } else {
            //散户
            List<String> belongToUserIds = List.of(contractData.getBuyerId(), contractData.getSellerId());
            if (!belongToUserIds.contains(userId)) {
                throw new BizException("当前用户无权限查看该合同详情");
            }
            isBuyer = StrUtil.equals(userId, contractData.getBuyerId());
        }

        List<ContractFileDTO> contractFileList = new ArrayList<>();
        //一条合同属于自己签署的信息只可能有一条（也可能没有、包赔证明无需签署）
        ContractSignInfo contractSignInfo = contractSignInfoRepository.queryContractSignInfoByUserId(userId, contractData.getContractDataId());
        if (ObjUtil.isNotEmpty(contractSignInfo)) { //交易合同
            //合同文件
            ContractFile contractFile = contractFileRepository.getById(contractSignInfo.getContractFileId());
            ContractFileDTO contractFileDTO = ContractDataAppMapping.INSTANCE.toFileDto(contractFile);
            if (StrUtil.isBlank(contractFile.getDocumentViewUrl())) {
                String viewContractUrl = "";
                ContractSignChannel channel = contractSignChannelRepository.getByRouteId(contractData.getRouteId())
                    .orElseThrow(() -> new BusinessException(ErrorCode.SIGN_CHANNEL_NOT_EXIST));
                DownloadContractReqPO downloadContractReqPO = new DownloadContractReqPO();
                downloadContractReqPO.setContractId(contractData.getContractNo());
                downloadContractReqPO.setAppId(channel.getAppId());
                downloadContractReqPO.setAppSecret(channel.getAppSecret());
                // 无合同文件地址，判断合同生成时间节点【2022-09-04】，老合同脱敏处理
                LocalDateTime parsedDateTime = LocalDateTime.parse("2022-09-04 00:00:00", DATE_TIME_FORMATTER);
                if (contractData.getCreateTime().isBefore(parsedDateTime) && contractFile.getContractType() == 4) {
                    // 获取合同文件URL
                    SingleResponse<JSONObject> downloadContractResp = fddGateway.downloadContract(downloadContractReqPO);
                    if (downloadContractResp.isSuccess()) {
                        // 合同脱敏
                        try {
                            //String url = "https://textapi.fadada.com/api2/downLoadContract.api?contract_id=CT2022051798525057&app_id=501792&timestamp=20241225094710&v=2.0&msg_digest=OTI1QjA2QzQ1MkJGQzg4MjJGRDQ0NDM3MjJBREZGNTBGM0M5Njg2OQ==";
                            // 下载到服务器
                            //String localUrl = DownloadPdfUtil.down(url);
                            String localUrl = DownloadPdfUtil.down(downloadContractResp.getData().getString("url"));
                            viewContractUrl = desensitizationDomainService.contractDesensitization(localUrl, contractFile.getContractFileId());
                            // 更新文件表中合同查看地址
                            if (StrUtil.isNotBlank(viewContractUrl)) {
                                contractFileRepository.updateViewUrlById(contractFile.getContractFileId(), viewContractUrl);
                            }
                            // 删除本地文件
                            DownloadPdfUtil.delLocalFile(localUrl);
                        } catch (Exception e) {
                            log.error("合同脱敏异常contract_file.id-{}", contractFile.getContractFileId(), e);
                        }
                    } else {
                        log.error("查看合同获取法大大合同文件失败-{}", downloadContractResp.getErrMessage());
                    }
                } else {
                    SingleResponse<JSONObject> viewContractResp = fddGateway.viewContract(downloadContractReqPO);
                    viewContractUrl = viewContractResp.getData().getString("url");
                }
                contractFileDTO.setDocumentViewUrl(viewContractUrl);
            }

            //签署信息
            ContractSignInfoDTO contractSignInfoDTO = ContractDataAppMapping.INSTANCE.toSignInfoDto(contractSignInfo);
            if (contractSignInfo.getContractDataId()
                .length() >= 15 && contractSignInfo.getSignedStatus() == 0 && LocalDateTime.now()
                .minusHours(24)
                .isAfter(contractSignInfo.getCreateTime())) {
                contractSignInfoDTO.setSignUrl(updateSignUrl(contractSignInfo));
            }
            contractFileDTO.setContractSignInfo(contractSignInfoDTO);
            contractFileDTO.setFileStatus(contractData.getStatus());
            contractFileDTO.setContractType(2);
            contractFileDTO.setSignedFinishTime(contractSignInfo.getSignedFinishTime());
            contractFileList.add(contractFileDTO);

            if (SignedStatus.WAIT.getCode().equals(contractSignInfo.getSignedStatus())) {
                respDTO.setCurrentUser(true);
            }
            if (SignedStatus.FINISH.getCode()
                .equals(contractSignInfo.getSignedStatus()) && contractSignInfo.getPeerSignStatus()) {
                respDTO.setCurrentUser(false);
            }
        }
        if (isBuyer) {
            //包赔证明(包赔文件可能会有多个)
            List<ContractFile> guaranteeFileList = contractFileRepository.getContractFilesByContractId(contractData.getContractDataId(), 1);
            if (CollUtil.isNotEmpty(guaranteeFileList)) {
                Integer contractStatus = respDTO.getStatus();
                List<ContractFileDTO> fileDTOList = guaranteeFileList.stream().map(item -> {
                    ContractFileDTO contractFileDTO = ContractDataAppMapping.INSTANCE.toFileDto(item);
                    contractFileDTO.setFileStatus(3);
                    contractFileDTO.setContractType(1);
                    contractFileDTO.setSignedFinishTime(item.getUpdateTime());
                    return contractFileDTO;
                }).toList();
                contractFileList.addAll(fileDTOList);
                if (Objects.equals(respDTO.getStatus(), ContractStatusEnum.SIGNING.getCode())) {
                    respDTO.setCurrentUser(Boolean.FALSE);
                } else {

                    respDTO.setCurrentUser(TRUE);
                }
            }
        }
        respDTO.setContractFile(contractFileList);
        return respDTO;
    }

    /**
     * IM合同详情
     *
     * @param id 合同id或者订单id
     * @return 合同详情
     */
    public ContractDetailRespDTO imContractDetail(String id) {
        String userId = UserUtil.getUserId();
        // 查询合同主数据
        ContractData contractData = contractDataRepository.findById(id);
        if (ObjUtil.isEmpty(contractData)) {
            return null;
        }
        String contractDataId = contractData.getContractDataId();
        ContractStatusEnum contractStatusEnum = ContractStatusEnum.getStatusEnumByCode(contractData.getStatus());

        ContractData contractDataWithIndemnity = contractDataRepository.getIndemnityInfoWithContractId(contractDataId);
        ContractDetailRespDTO respDTO = ContractDataAppMapping.INSTANCE.toContractDetailRespDTO(contractData);
        List<ContractIndemnityDTO> contractIndemnityList = new ArrayList<>();
        if (CollUtil.isNotEmpty(contractDataWithIndemnity.getIndemnityIds())) {
            MultiResponse<ContractIndemnityInfoDTO> response = indemnityDubboServiceI.getIndemnityConfigByIds(contractDataWithIndemnity.getIndemnityIds());
            if (response.isSuccess() && CollUtil.isNotEmpty(response.getData())) {
                List<ContractIndemnityInfoDTO> contractIndemnityInfoDTOList = response.getData();
                contractIndemnityInfoDTOList.forEach(item -> {
                    ContractIndemnityDTO contractIndemnityDTO = ContractDataAppMapping.INSTANCE.toContractIndemnityDTO(item);
                    contractIndemnityDTO.setIndemnityTypeLev1Desc(IndemnityTypeEnum.getEnumByCode(item.getIndemnityTypeLev1())
                        .getDesc());
                    contractIndemnityDTO.setIndemnityTypeLev2Desc(IndemnityTypeLev2Enum.getDescByCode(item.getIndemnityTypeLev2()));
                    contractIndemnityList.add(contractIndemnityDTO);
                });
            }
        }
        respDTO.setContractIndemnityList(contractIndemnityList);

        // 查询合同文件数据
        List<ContractFile> contractFile = contractFileRepository.getContractFilesByContractId(contractDataId, null);
        if (CollUtil.isEmpty(contractFile)) {
            return respDTO;
        }

        if (ObjUtil.isEmpty(respDTO.getGameName())) {
            //查询游戏数据
            MultiResponse<GameBaseDTO> response = DubboResultAssert.wrapException(() -> gameServiceI.selectGameBaseList(Collections.singletonList(contractData.getGameId())), ErrorCode.RPC_GET_GAME_LIST_ERROR);
            Map<String, String> gameMap = response.getData()
                .stream()
                .collect(Collectors.toMap(GameBaseDTO::getGameId, GameBaseDTO::getGameName));
            respDTO.setGameName(gameMap.get(contractData.getGameId()));
        }

        List<ContractFileDTO> list = contractFile.stream().map(item -> {
            ContractFileDTO contractFileDTO = ContractDataAppMapping.INSTANCE.toFileDto(item);
            ContractTypeEnum contractTypeEnum = ContractTypeEnum.getContractTypeEnumByCode(contractFileDTO.getContractType());
            if (contractTypeEnum == ContractTypeEnum.INDEMNITY_PROVE) {
                contractFileDTO.setFileStatus(3);
                contractFileDTO.setContractType(GuaranteeTypeEnum.INDEMNITY_PROVE.getCode());
                contractFileDTO.setSignedFinishTime(item.getUpdateTime());
            } else {
                contractFileDTO.setFileStatus(contractData.getStatus());
                contractFileDTO.setContractType(GuaranteeTypeEnum.TRADE_CONTRACT.getCode());
                // 根据合同文件查询签署信息
                List<ContractSignInfo> contractSignInfoList = contractSignInfoRepository.querySignInfoByFileId(item.getContractFileId());
                //买家签署信息
                ContractSignInfo buyerSignInfo = contractSignInfoList.stream()
                    .filter(crtSignInfo -> Objects.equals(crtSignInfo.getSignatoryType(), ContractUserType.BUYER.getCode()))
                    .findFirst()
                    .orElse(null);
                //卖家签署信息
                ContractSignInfo sellerSignInfo = contractSignInfoList.stream()
                    .filter(crtSignInfo -> Objects.equals(crtSignInfo.getSignatoryType(), ContractUserType.SELLER.getCode()))
                    .findFirst()
                    .orElse(null);
                switch (contractTypeEnum) {
                    case BUYER_CONTRACT -> {
                        ContractSignInfoDTO buyerSignInfoDTO = ContractDataAppMapping.INSTANCE.toSignInfoDto(buyerSignInfo);
                        contractFileDTO.setContractSignInfo(buyerSignInfoDTO);
                        contractFileDTO.setSendMsgType(contractTypeEnum.getType());
                        contractFileDTO.setSignedFinishTime(Objects.requireNonNull(buyerSignInfo)
                            .getSignedFinishTime());
                    }
                    case SELLER_CONTRACT -> {
                        ContractSignInfoDTO sellerSignInfoDTO = ContractDataAppMapping.INSTANCE.toSignInfoDto(sellerSignInfo);
                        contractFileDTO.setContractSignInfo(sellerSignInfoDTO);
                        contractFileDTO.setSendMsgType(contractTypeEnum.getType());
                        contractFileDTO.setSignedFinishTime(Objects.requireNonNull(sellerSignInfoDTO)
                            .getSignedFinishTime());
                    }
                    case BOTH_PARTIES_CONTRACT -> {
                        if (contractStatusEnum.equals(ContractStatusEnum.WAIT_SIGN)) {
                            contractFileDTO.setSendMsgType(ContractTypeEnum.BOTH_PARTIES_CONTRACT.getType());
                        } else if (contractStatusEnum.equals(ContractStatusEnum.SIGNING)) {
                            //查询买卖双方的签署状态
                            contractSignInfoList.forEach(crtSignInfo -> {
                                ContractUserType contractUserType = ContractUserType.getByCode(crtSignInfo.getSignatoryType());
                                switch (contractUserType) {
                                    case BUYER -> {
                                        if (SignedStatus.WAIT.getCode().equals(crtSignInfo.getSignedStatus())) {
                                            contractFileDTO.setSendMsgType(ContractTypeEnum.BUYER_CONTRACT.getType());
                                            respDTO.setCurrentUser(true);
                                        }
                                        if (SignedStatus.FINISH.getCode()
                                            .equals(crtSignInfo.getSignedStatus()) && crtSignInfo.getPeerSignStatus()) {
                                            contractFileDTO.setSignedFinishTime(Objects.requireNonNull(buyerSignInfo)
                                                .getSignedFinishTime());
                                            respDTO.setCurrentUser(false);
                                        }
                                    }
                                    case SELLER -> {
                                        if (SignedStatus.WAIT.getCode().equals(crtSignInfo.getSignedStatus())) {
                                            respDTO.setCurrentUser(true);
                                            contractFileDTO.setSendMsgType(ContractTypeEnum.SELLER_CONTRACT.getType());
                                        }
                                        if (SignedStatus.FINISH.getCode()
                                            .equals(crtSignInfo.getSignedStatus()) && crtSignInfo.getPeerSignStatus()) {
                                            contractFileDTO.setSignedFinishTime(Objects.requireNonNull(sellerSignInfo)
                                                .getSignedFinishTime());
                                            respDTO.setCurrentUser(false);
                                        }
                                    }
                                }
                            });
                        }
                    }
                }
            }
            return contractFileDTO;
        }).toList();

        respDTO.setContractFile(list);
        return respDTO;
    }

    /**
     * 根据订单商品查合同数据
     *
     * @param reqDTO 查询参数
     * @return 合同订单信息
     */
    public List<AutomationContractRespDTO> searchContractDataByOrderGoods(AutomationContractReqDTO reqDTO) {
        if (reqDTO.getSellerIdentity() == 1 && StringUtils.isBlank(reqDTO.getPhone())) {
            throw new BusinessException(ErrorCode.QUERY_CONTRACT_BY_GOODS_ERROR_PHONE);
        }
        if (reqDTO.getSellerIdentity() == 2 && StringUtils.isBlank(reqDTO.getSellerMerchantId())) {
            throw new BusinessException(ErrorCode.QUERY_CONTRACT_BY_GOODS_ERROR_MERCHANT);
        }
        // DTO 转 BO
        SearchContractParamBO searchParamBO = ContractDataAppMapping.INSTANCE.transSearchContractParam(reqDTO);
        // 查询合同数据
        List<ContractData> contractData = contractDomainService.searchContract(searchParamBO);
        // 写入查询日志
        ContractByGoodsLogReqPO.AddPO logReqPO = ContractDataAppMapping.INSTANCE.transSearchContractLog(reqDTO);
        // 数据映射
        if (StringUtils.isNotBlank(reqDTO.getProductId())) {
            //商品查询
            List<String> productIds = new ArrayList<>();
            productIds.add(reqDTO.getProductId());
            MultiResponse<ProductRespDTO> response = DubboResultAssert.wrapException(() -> productServiceI.selectList(productIds), ErrorCode.RPC_GET_PRODUCT_LIST_ERROR);
            List<ProductRespDTO> productList = response.getData();
            if (CollectionUtils.isNotEmpty(productList)) {
                logReqPO.setProductUniqueNo(productList.get(0).getProductUniqueNo());
            }
        }

        logReqPO.setUserMerchantId(reqDTO.getSellerMerchantId());
        logReqPO.setUserType(reqDTO.getSellerIdentity());
        logReqPO.setResult(JSONObject.toJSONString(contractData));
        // 查询人信息
        logReqPO.setSysUserId(LoginUserUtil.getUserId());
        logReqPO.setSysUserName(LoginUserUtil.getUserName());
        contractByGoodsLogRepository.insert(logReqPO);
        return ContractDataAppMapping.INSTANCE.transContractDeliveryData(contractData);
    }

    public UserInfoDTO findUserInfoByUserId(String userId) {
        UserShortInfoDTO userInfo = userGateway.getBaseUserInfo(userId);
        UserInfoDTO userInfoDTO = ContractDataAppMapping.INSTANCE.RpcToUserInfoRespDTO(userInfo);
        if (userInfo.getUserType() == 2) {
            MerchantBaseInfoRespDTO merchantInfoRespDTO = merchantGateway.getMerchantBaseInfo(userId);
            userInfoDTO.setMerchantId(merchantInfoRespDTO.getMerchantId());
        }
        return userInfoDTO;
    }

    public InfoSubmitStatusRespDTO notifyInfoSubmitStatus(InfoSubmitStatusReqDTO infoSubmitStatusReqDTO) {
        InfoSubmitStatusBO infoSubmitStatusBO = contractDomainService.notifyInfoSubmitStatus(infoSubmitStatusReqDTO);
        return ContractDataAppMapping.INSTANCE.toInfoSubmitStatusDTO(infoSubmitStatusBO);
    }

    public ContractUserInfoRespDTO contractUserInfoList(String orderItemId, String gameId) {
        ContractUserInfoBO contractUserInfoBO = contractDomainService.contractUserInfo(orderItemId, gameId);
        return ContractDataAppMapping.INSTANCE.toUserInfoRespDTO(contractUserInfoBO);
    }

    /**
     * 个人中心-我方待签署的合同数量
     *
     * @return
     */
    public Long contractCount() {
        String userId = UserUtil.getUserId();
        boolean isMerchant = UserUtil.isMerchant();
        String cacheKey = String.format(RedisConstants.CONTRACT_COUNT_KEY, userId);
        if (isMerchant) {
            Long merchantCount = RedissonUtils.getCacheObject(cacheKey);
            if (ObjectUtil.isNotEmpty(merchantCount)) {
                return merchantCount;
            }
        }
        List<ContractData> contractDataList = contractDataRepository.queryOwnerContractList(userId);
        if (CollUtil.isNotEmpty(contractDataList)) {
            List<String> contractIdList = contractDataList.stream().map(ContractData::getContractDataId).toList();
            Long count = contractSignInfoRepository.contractCount(userId, contractIdList);
            if (isMerchant) {
                RedissonUtils.setCacheObject(cacheKey, count, Duration.ofMinutes(30));
            }
            return count;
        }
        return 0L;
    }

    /**
     * 个人中心-合同关联的游戏列表
     *
     * @return
     */
    public List<ContractGameRespDTO> gameOptionList() {
        List<ContractGameRespDTO> result = new ArrayList<>();
        String userId = UserUtil.getUserId();
        List<ContractData> contractDataList = contractDataRepository.gameOptionList(userId);
        if (CollUtil.isNotEmpty(contractDataList)) {
            Map<String, List<ContractData>> dataMap = contractDataList.stream()
                .collect(Collectors.groupingBy(ContractData::getGameId));
            dataMap.forEach((key, value) -> {
                ContractGameRespDTO gameRespDTO = new ContractGameRespDTO();
                gameRespDTO.setGameId(key);
                gameRespDTO.setGameName(value.get(0).getGameName());
                result.add(gameRespDTO);
            });
        }
        return result;
    }

    /**
     * 发送签署链接短信
     *
     * @param reqDTO 发送签署链接的请求参数
     * @return Boolean
     */
    public Boolean sendSms(ContractSendSmsReqDTO reqDTO) {

        ContractData contractData = Try.of(() -> contractDataRepository.findById(reqDTO.getContractDataId()))
            .getOrElseThrow(() -> new BusinessException(ErrorCode.CONTRACT_NOT_EXIST));
        ContractSignChannel channel = contractSignChannelRepository.getByRouteId(contractData.getRouteId())
            .orElseThrow(() -> new BusinessException(ErrorCode.SIGN_CHANNEL_NOT_EXIST));
        ContractSignInfo signInfo = contractSignInfoRepository.queryContractSignInfoByUserId(reqDTO.getUserId(), reqDTO.getContractDataId());
        if (signInfo == null) {
            throw new BusinessException(ErrorCode.CONTRACT_SIGN_INFO_NOT_EXIST);
        }
        SmsReqPO sms = new SmsReqPO();
        sms.setSourceUrl(signInfo.getSignUrl());
        sms.setMobile(reqDTO.getPhone());
        sms.setAppId(channel.getAppId());
        sms.setAppSecret(channel.getAppSecret());
        return fddGateway.sms(sms);
    }

    /**
     * 推送合同签署完成增加水印卡片
     *
     * @param reqDTO 合同签署完成增加水印卡片请求的DTO
     * @return boolean
     */
    public Boolean sendContractSignFinishCard(ContractFinishCardReqDTO reqDTO) {
        ContractFinishCardBO cardBO = ContractDataAppMapping.INSTANCE.toReqDTO(reqDTO);
        return contractCardDomainService.finishContractCard(cardBO);
    }

    /**
     * 推送作废签署的合同卡片
     *
     * @param reqDTO 作废签署的合同卡片请求DTO
     * @return Boolean
     */
    public Boolean sendContractDeleteCard(ContractDeleteCardReqDTO reqDTO) {
        ContractDeleteCardBO cardBO = ContractDataAppMapping.INSTANCE.toDeleteBO(reqDTO);
        return contractCardDomainService.deleteContractCard(cardBO);
    }

    /**
     * 推送合同签署卡片
     *
     * @param reqDTO 合同签署卡片请求DTO
     * @return String
     */
    public String sendContractSignCard(ContractSignCardReqDTO reqDTO) {
        ContractSignCardBO signBO = ContractDataAppMapping.INSTANCE.toSignBO(reqDTO);
        signBO.setContractType(ContractType.fromCode(reqDTO.getContractTypeCode()));
        return contractCardDomainService.sendSignContractCard(signBO);
    }

    /**
     * 发送合同催促签署卡片
     *
     * @param reqDTO
     * @return
     */
    public Boolean sendSignUrgeCard(UrgeSignCardReqDTO reqDTO) {
        ContractUrgeCardBO contractUrgeCardBO = ContractDataAppMapping.INSTANCE.toUrgeBO(reqDTO);
        contractUrgeCardBO.setUserType(ContractUserType.getByCode(reqDTO.getIdentityType()));
        return contractCardDomainService.sendUrgeCard(contractUrgeCardBO);
    }

    /**
     * 发送催一催消息
     *
     * @param reqDTO
     * @return
     */
    public Boolean sendUrgeMsg(ContractUrgeMsgReqDTO reqDTO) {
        ContractUrgeMsgBO urgeMsgBO = ContractDataAppMapping.INSTANCE.toUrgeMsgBO(reqDTO);
        return contractCardDomainService.sendUrgeMsg(urgeMsgBO);
    }

    /**
     * 推送风控冻结卡片
     *
     * @param reqDTO 合同冻结卡片请求DTO
     * @return Boolean
     */
    public Boolean sendRiskControlFreezeCard(ContractFreezeCardReqDTO reqDTO) {
        ContractFreezeCardBO freezeBO = ContractDataAppMapping.INSTANCE.toFreezeBO(reqDTO);
        return contractCardDomainService.sendFreezeCard(freezeBO);
    }

    /**
     * 获取合同信息
     *
     * @param orderItemId 订单id
     * @return ContractInfoRespDTO
     */
    public ContractInfoRespDTO getContractInfo(String orderItemId) {
        ContractData contract = contractDataRepository.getContractDataWithOrder(orderItemId);
        if (ObjectUtil.isEmpty(contract)) {
            return null;
        }
        String contractDataId = contract.getContractDataId();
        ContractInfoRespDTO contractInfoRespDTO = ContractDataAppMapping.INSTANCE.toContractInfoRespDTO(contract);
        List<ContractFile> contractFileList = contractFileRepository.getContractFilesByContractIds(Collections.singletonList(contractDataId));
        if (CollUtil.isNotEmpty(contractFileList)) {
            contractFileList.forEach(item -> {
                contractInfoRespDTO.setContractType(item.getContractType());
                ContractTypeEnum contractTypeEnum = ContractTypeEnum.getContractTypeEnumByCode(item.getContractType());
                switch (contractTypeEnum) {
                    case INDEMNITY_PROVE -> contractInfoRespDTO.setBuyerSignedStatus(1);
                    case BUYER_CONTRACT, SELLER_CONTRACT, BOTH_PARTIES_CONTRACT -> {
                        List<ContractSignInfo> contractSignInfoList = contractSignInfoRepository.querySignInfoByFileId(item.getContractFileId());
                        if (CollUtil.isNotEmpty(contractSignInfoList)) {
                            contractSignInfoList.forEach(signInfo -> {
                                ContractUserType contractUserType = ContractUserType.getByCode(signInfo.getSignatoryType());
                                switch (contractUserType) {
                                    case BUYER -> {
                                        contractInfoRespDTO.setBuyerSignedStatus(signInfo.getSignedStatus());
                                        if (contractTypeEnum == ContractTypeEnum.BUYER_CONTRACT) {
                                            contractInfoRespDTO.setSellerSignedStatus(1);
                                        }
                                    }
                                    case SELLER -> {
                                        contractInfoRespDTO.setSellerSignedStatus(signInfo.getSignedStatus());
                                        if (contractTypeEnum == ContractTypeEnum.SELLER_CONTRACT) {
                                            contractInfoRespDTO.setBuyerSignedStatus(1);
                                        }
                                    }
                                }
                            });
                        }
                    }
                }
            });
        }
        return contractInfoRespDTO;
    }

    public Boolean checkContractExist(CheckContract contract) {
        return contractDomainService.checkContractExist(contract);
    }

    /**
     * 获取订单信息
     *
     * @param orderItemId 订单id
     * @return
     */
    public ContractOrderItemDTO getOrderItemInfo(String orderItemId) {
        try {
            //从合同的数据中获取
            ContractOrderItemDTO contractOrderItem = contractRelationAppService.loadOrderInfoFromContractData(orderItemId);
            //从商品服务中获取
            ProductRespDTO productResp = Optional.ofNullable(orderItemId)
                .map(o -> orderItemDubboGateway.getOrderInfo(o))
                .map(OrderInfoDubboRespDTO::getProductId)
                .filter(StringUtils::isNotBlank)
                .map(o -> DubboResultAssert.wrapException(() -> productServiceI.selectList(Collections.singletonList(o)), ErrorCode.RPC_GET_PRODUCT_LIST_ERROR))
                .map(MultiResponse::getData)
                .flatMap(o -> o.stream().findFirst())
                .orElse(new ProductRespDTO());

            return ContractOrderItemDTO.builder()
                .gameName(Objects.nonNull(contractOrderItem) && StringUtils.isNotBlank(contractOrderItem.getGameName())
                    ? contractOrderItem.getGameName()
                    : productResp.getGameName())
                .productUniqueNo(Objects.nonNull(contractOrderItem) && StringUtils.isNotBlank(contractOrderItem.getProductUniqueNo())
                    ? contractOrderItem.getProductUniqueNo()
                    : productResp.getProductUniqueNo())
                .productId(Objects.nonNull(contractOrderItem) && StringUtils.isNotBlank(contractOrderItem.getProductId())
                    ? contractOrderItem.getProductId()
                    : productResp.getProductId())
                .orderItemId(orderItemId)
                .build();
        } catch (Exception e) {
            //ignore
            log.warn("getOrderItemInfo,orderItemId:{}", orderItemId, e);
            return ContractOrderItemDTO.builder().orderItemId(orderItemId).build();
        }
    }

    public String viewGraph(CheckContractReqDTO reqDTO) {
        return contractConfig.getGraphFile(reqDTO.getContractType());
    }

    /**
     * 获取订单包赔信息
     *
     * @param orderItemId
     * @return
     */
    public ImRoomOrderInfoRespDTO getImRoomOrderInfo(String orderItemId) {
        ImRoomOrderInfoRespDTO imRoomOrderInfoRespDTO = new ImRoomOrderInfoRespDTO();
        imRoomOrderInfoRespDTO.setOrderItemId(orderItemId);

        List<OrderItemEnhancedIndemnity> orderItemEnhancedIndemnityList = new ArrayList<>();
        MultiResponse<OrderItemIndemnityDubboDTO> multiResponse = orderItemIndemnityDubboServiceI.getIndemnityListByOrderItemId(orderItemId);
        if (multiResponse.isSuccess()) {
            multiResponse.getData()
                .stream()
                .filter(indemnityDTO -> Objects.equals(indemnityDTO.getIndemnityStatus(), IndemnityStatusEnum.ACTIVATE.getValue()) || indemnityDTO.getIndemnityStatus()
                    .equals(IndemnityStatusEnum.CANCEL_PENDING.getValue()))
                .forEach(ide -> {
                    IndemnityTypeEnum indemnityTypeEnum = IndemnityTypeEnum.getEnumByCode(ide.getIndemnityTypeLev1());
                    switch (indemnityTypeEnum) {
                        case NORMAL_INDEMNITY -> {
                            OrderItemNormalIndemnity orderItemNormalIndemnity = new OrderItemNormalIndemnity();
                            orderItemNormalIndemnity.setNormalIndemnityId(ide.getIndemnityId());
                            orderItemNormalIndemnity.setNormalIndemnityTypeLev1(ide.getIndemnityTypeLev1());
                            orderItemNormalIndemnity.setNormalIndemnityTypeLev2(ide.getIndemnityTypeLev2());
                            imRoomOrderInfoRespDTO.setOrderItemNormalIndemnity(orderItemNormalIndemnity);
                        }
                        case ADVANCED_INDEMNITY -> {
                            OrderItemEnhancedIndemnity orderItemEnhancedIndemnity = new OrderItemEnhancedIndemnity();
                            orderItemEnhancedIndemnity.setEnhancedIndemnityId(ide.getIndemnityId());
                            orderItemEnhancedIndemnity.setEnhancedIndemnityTypeLev1(ide.getIndemnityTypeLev1());
                            orderItemEnhancedIndemnity.setEnhancedIndemnityTypeLev2(ide.getIndemnityTypeLev2());
                            orderItemEnhancedIndemnityList.add(orderItemEnhancedIndemnity);
                        }
                    }
                });
        }
        imRoomOrderInfoRespDTO.setOrderItemEnhancedIndemnityList(orderItemEnhancedIndemnityList);

        OrderItemAmountRespDTO orderItemAmountRespDTO = orderItemDubboGateway.getDealPrice(orderItemId);
        if (ObjectUtil.isNotEmpty(orderItemAmountRespDTO)) {
            imRoomOrderInfoRespDTO.setPreDiscountPrice(orderItemAmountRespDTO.getProductAmount());
            imRoomOrderInfoRespDTO.setDiscountPrice(orderItemAmountRespDTO.getProductActualAmount());
        }
        return imRoomOrderInfoRespDTO;
    }

    /**
     * 查询用户有没有作为买家在系统签过合同
     *
     * @param userContractReqDTO
     * @return
     */
    public Boolean checkWhetherSignedContractAsBuyer(UserContractReqDTO userContractReqDTO) {
        UserContractReqPO userContractReqPO = ContractDataAppMapping.INSTANCE.toUserContractReqPO(userContractReqDTO);
        ContractData latestContract = contractDataRepository.queryLatestContractInfo(userContractReqPO);
        if (ObjectUtil.isEmpty(latestContract)) {
            return false;
        }
        return contractSignInfoRepository.querySignInfoAsBuyer(userContractReqDTO.getUserId(), latestContract.getContractDataId());
    }

    /**
     * 查询用户作为买家的已签署合同id
     *
     * @param userContractReqDTO
     * @return
     */
    public String getSignedContractIdAsBuyer(UserContractReqDTO userContractReqDTO) {
        UserContractReqPO userContractReqPO = ContractDataAppMapping.INSTANCE.toUserContractReqPO(userContractReqDTO);
        ContractData latestContract = contractDataRepository.queryLatestContractInfo(userContractReqPO);
        if (ObjectUtil.isEmpty(latestContract)) {
            return null;
        }
        List<ContractFile> files = contractFileRepository.getContractFilesByContractIds(Collections.singletonList(latestContract.getContractDataId()));
        ContractFile contractFile = files.stream()
            .filter(file -> ContractType.fromCode(file.getContractType()).equals(ContractType.CLAIM))
            .findFirst()
            .orElse(null);
        if (ObjectUtil.isEmpty(contractFile)) {
            Boolean isSignedAsBuyer = contractSignInfoRepository.querySignInfoAsBuyer(userContractReqDTO.getUserId(), latestContract.getContractDataId());
            return isSignedAsBuyer ? latestContract.getContractDataId() : null;
        } else {
            return latestContract.getContractDataId();
        }
    }

    /**
     * 查询号商作为买家的已签署的最新的合同id
     *
     * @param merchantContractReqDTO 号商签署的合同请求参数
     * @return
     */
    public List<MerchantContractRespDTO> getLatestCTIdWithMerchantAsBuyer(MerchantContractReqDTO merchantContractReqDTO) {
        List<MerchantContractRespDTO> result = new ArrayList<>();
        MerchantContractReqPO merchantContractReqPO = ContractDataAppMapping.INSTANCE.toMerchantContractReqPO(merchantContractReqDTO);
        List<ContractData> merchantLatestContract = contractDataRepository.queryLatestMerchantContractInfo(merchantContractReqPO);
        if (CollUtil.isEmpty(merchantLatestContract)) {
            return null;
        }
        Map<String, List<ContractData>> dataMap = merchantLatestContract.stream()
            .collect(Collectors.groupingBy(ContractData::getPassesNo));
        Map<String, ContractData> latestContractMap = dataMap.entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                .stream()
                .max(Comparator.comparing(ContractData::getCreateTime))
                .orElseGet(null)));
        if (CollUtil.isNotEmpty(latestContractMap)) {
            latestContractMap.forEach((passesNo, contractData) -> {
                if (ObjectUtil.isNotNull(contractData)) {
                    String contractId = contractData.getContractDataId();
                    if (contractData.getStatus() == 3) {
                        MerchantContractRespDTO merchantContractRespDTO = new MerchantContractRespDTO();
                        merchantContractRespDTO.setGameAccount(passesNo);
                        merchantContractRespDTO.setContractId(contractId);
                        result.add(merchantContractRespDTO);
                    }
                    if (contractData.getStatus() == 2) {
                        Boolean isSignedAsBuyer = contractSignInfoRepository.querySignInfoAsBuyer(contractData.getBuyerId(), contractId);
                        if (isSignedAsBuyer) {
                            MerchantContractRespDTO merchantContractRespDTO = new MerchantContractRespDTO();
                            merchantContractRespDTO.setGameAccount(passesNo);
                            merchantContractRespDTO.setContractId(contractId);
                            result.add(merchantContractRespDTO);
                        }
                    }
                }
            });
        }
        return result;
    }

    public Boolean isSignedContract(GameAccountExitReqDTO req) {
        if (StrUtil.isBlank(req.getUserId()) || StrUtil.isBlank(req.getGameAccount()) || req.getContractUserType() == null) {
            throw new BusinessException("用户id和用户账号和合同用户类型不能为空");
        }
        LambdaQueryChainWrapper<ContractData> wrapper = contractDataRepository.lambdaQuery()
            .in(CollUtil.isNotEmpty(req.getGameIds()), ContractData::getGameId, req.getGameIds())
            .eq(ContractData::getPassesNo, req.getGameAccount());
        switch (req.getContractUserType()) {
            case 1 -> wrapper.eq(ContractData::getBuyerId, req.getUserId());
            case 2 -> wrapper.eq(ContractData::getSellerId, req.getUserId());
            case 3 -> wrapper.and(i -> i.eq(ContractData::getBuyerId, req.getUserId())
                .or()
                .eq(ContractData::getSellerId, req.getUserId()));
            default -> throw new BusinessException("合同用户类型不正确");
        }
        return wrapper.exists();
    }

    //风控详情
    public List<ContractRiskRespDTO> getContractRisk(List<String> contractIds) {
        if (contractIds == null || contractIds.isEmpty()) {
            return null;
        }
        List<ContractData> contractData = contractDataRepository.findContractByIds(contractIds);
        List<ContractRiskRespDTO> data = ContractDataAppMapping.INSTANCE.toContractRisk(contractData);
        // 处理买卖双方为号商时的信息
        List<String> sellerMerchantIds = contractData.stream().map(ContractData::getSellerMerchantId).toList();
        List<String> buyerMerchantIds = contractData.stream().map(ContractData::getBuyerMerchantId).toList();
        List<String> merchantIds = Stream.concat(sellerMerchantIds.stream(), buyerMerchantIds.stream())
            .distinct()
            .toList();

        Map<String, String> merchantMap = new HashMap<>();
        if (CollUtil.isNotEmpty(merchantIds)) {
            List<MerchantListRespDTO> merchantInfo = this.merchantList();
            merchantMap = merchantInfo.stream()
                .collect(Collectors.toMap(MerchantListRespDTO::getMerchantId, MerchantListRespDTO::getMerchantName, (existing, replacement) -> existing));
        }
        Map<String, String> finalMerchantMap = merchantMap;

        data.forEach(contract -> {
            StringBuilder buyerInfo = new StringBuilder();
            StringBuilder sellerInfo = new StringBuilder();
            List<String> userIds = new ArrayList<>();
            userIds.add(contract.getBuyerId());
            userIds.add(contract.getSellerId());
            // 注册信息
            Map<String, UserShortInfoDTO> userShortInfoListMap = new HashMap<>();
            Map<String, UserCertInfoRespDTO> userCertInfoListMap = new HashMap<>();
            Map<String, ContractSignatoryUser> signatoryUserListMap = new HashMap<>();
            userShortInfoListMap.putAll(userGateway.findBaseUserInfoList(userIds).stream().collect(Collectors.toMap(UserShortInfoDTO::getUserId, Function.identity(), (o1, o2) -> o1)));
            userCertInfoListMap.putAll(userGateway.findPersonalUserInfoList(userIds).stream().collect(Collectors.toMap(UserCertInfoRespDTO::getUserId, Function.identity(), (o1, o2) -> o1)));
            //签署信息
            List<ContractSignInfo> contractSignInfoList = contractSignInfoRepository.queryContractSignInfoByContractDataId(contractIds);
            // 签约人信息
            List<String> signUserIds = contractSignInfoList.stream().map(ContractSignInfo::getSignatoryUserId).toList();
            signatoryUserListMap
                .putAll(contractSignatoryUserRepository.findSignatoryUsers(signUserIds).stream().collect(
                    Collectors.toMap(ContractSignatoryUser::getSignatoryUserId, Function.identity(), (o1, o2) -> o1)));
            if (StringUtils.isNotBlank(contract.getBuyerMerchantId()) && StringUtils.isNotBlank(finalMerchantMap.get(contract.getBuyerMerchantId()))) {
                buyerInfo.append("商家名称：").append(finalMerchantMap.get(contract.getBuyerMerchantId())).append("\n");
            } else {
                buyerInfo.append("商家名称：").append("--").append("\n");
            }
            buyerInfo.append("用户ID：").append(contract.getBuyerId()).append("\n");
            buyerInfo.append("注册手机号：")
                .append(!userShortInfoListMap.isEmpty() && Objects.nonNull(userShortInfoListMap.get(contract.getBuyerId()))
                    ? userShortInfoListMap.get(contract.getBuyerId()).getPhone()
                    : "")
                .append("\n");
            buyerInfo.append("注册身份证号：")
                .append(!userCertInfoListMap.isEmpty() && Objects.nonNull(userCertInfoListMap.get(contract.getBuyerId()))
                    ? userCertInfoListMap.get(contract.getBuyerId()).getCertId()
                    : "")
                .append("\n");

            if (StringUtils.isNotBlank(contract.getSellerMerchantId()) && StringUtils.isNotBlank(finalMerchantMap.get(contract.getSellerMerchantId()))) {
                sellerInfo.append("商家名称：")
                    .append(finalMerchantMap.get(contract.getSellerMerchantId()))
                    .append("\n");
            } else {
                sellerInfo.append("商家名称：").append("--").append("\n");
            }
            sellerInfo.append("用户ID：").append(contract.getSellerId()).append("\n");
            sellerInfo.append("注册手机号：")
                .append(!userShortInfoListMap.isEmpty() && Objects.nonNull(userShortInfoListMap.get(contract.getSellerId()))
                    ? userShortInfoListMap.get(contract.getSellerId()).getPhone()
                    : "")
                .append("\n");
            sellerInfo.append("注册身份证号：")
                .append(!userCertInfoListMap.isEmpty() && Objects.nonNull(userCertInfoListMap.get(contract.getSellerId()))
                    ? userCertInfoListMap.get(contract.getSellerId()).getCertId()
                    : "")
                .append("\n");
            contractSignInfoList.forEach(signInfo -> {
                if (Objects.equals(contract.getContractDataId(), signInfo.getContractDataId())) {
                    if (Objects.equals(contract.getBuyerId(), signInfo.getUserId()) && signatoryUserListMap.containsKey(signInfo.getSignatoryUserId())) {

                        buyerInfo.append("签约人手机号：")
                            .append(signatoryUserListMap.get(signInfo.getSignatoryUserId()).getPhone())
                            .append("\n");
                        buyerInfo.append("签约人身份证号：")
                            .append(signatoryUserListMap.get(signInfo.getSignatoryUserId()).getCertNo())
                            .append("\n");
                    }
                    if (Objects.equals(contract.getSellerId(), signInfo.getUserId()) && signatoryUserListMap.containsKey(signInfo.getSignatoryUserId())) {
                        sellerInfo.append("签约人手机号：")
                            .append(signatoryUserListMap.get(signInfo.getSignatoryUserId()).getPhone())
                            .append("\n");
                        sellerInfo.append("签约人身份证号：")
                            .append(signatoryUserListMap.get(signInfo.getSignatoryUserId()).getCertNo())
                            .append("\n");
                    }
                }
            });
            contract.setBuyerInfo(buyerInfo.toString());
            contract.setSellerInfo(sellerInfo.toString());
        });
        return data;
    }

    //商家下拉框数据
    public List<MerchantListRespDTO> merchantList() {
        //调用dubbo接口
        List<MerchantBaseInfoRespDTO> merchantList = merchantGateway.getMerchantList();
        return ContractDataAppMapping.INSTANCE.toMerchantListData(merchantList);
    }

    /**
     * 更新签署链接
     *
     * @param contractSignInfo
     * @return
     */
    private String updateSignUrl(ContractSignInfo contractSignInfo) {
        String contractFileId = contractSignInfo.getContractFileId();
        String contractSignId = contractSignInfo.getContractSignId();
        String signatoryUserId = contractSignInfo.getSignatoryUserId();
        String signUrl = contractDomainService.updateSignUrl(contractFileId, contractSignId, signatoryUserId);
        return StrUtil.isNotBlank(signUrl) ? signUrl : contractSignInfo.getSignUrl();
    }

    /**
     * 组装合同主数据
     *
     * @param contractMainInfoBO
     * @param contractId             合同id
     * @param orderItemId            订单id
     * @param contractInitiateReqDTO 合同发起DTO
     * @param freeze                 是否冻结
     * @param certInfoRes
     * @return
     */
    private ContractMainInfoBO buildMainContractInfo(ContractMainInfoBO contractMainInfoBO,
                                                     String contractId,
                                                     String orderItemId,
                                                     ContractInitiateReqDTO contractInitiateReqDTO,
                                                     Boolean freeze,
                                                     Tuple2<BuyerInfoDTO, SellerInfoDTO> certInfoRes) {
        contractMainInfoBO.setContractDataId(contractId);
        contractMainInfoBO.setOrderItemId(orderItemId);

        BuyerInfoDTO buyerInfoDTO = certInfoRes._1;
        SellerInfoDTO sellerInfoDTO = certInfoRes._2;
        contractMainInfoBO.setBuyerPhone(buyerInfoDTO.getPhone().trim());
        contractMainInfoBO.setSellerPhone(sellerInfoDTO.getPhone().trim());

        contractMainInfoBO.setContractNo("NCT" + System.currentTimeMillis());
        contractMainInfoBO.setBusScenario(1);
        if (freeze) {  //冻结--准备中
            contractMainInfoBO.setStatus(0);
        } else {
            switch (contractInitiateReqDTO.getTradeContractType()) {
                case 20 -> {
                    contractMainInfoBO.setStatus(3);
                    contractMainInfoBO.setFinishTime(LocalDateTime.now());
                }
                case 21, 22 -> contractMainInfoBO.setStatus(2);
                case 23 -> contractMainInfoBO.setStatus(1);
            }
        }
        contractMainInfoBO.setDataSource(1);
        String createUserId = contractInitiateReqDTO.getIntelligentDelivery() ? "系统" : ImUserUtil.getUserId();
        contractMainInfoBO.setCreateUserId(createUserId);
        contractMainInfoBO.setUpdateUserId(createUserId);
        contractMainInfoBO.setBusScenario(1);
        contractMainInfoBO.setServiceMode(1);
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.getClientTypeEnumByCode(contractInitiateReqDTO.getClientType());
        switch (clientTypeEnum) {
            case PC, ANDROID, WAP, IOS, WECHAT_MINI_PROGRAM, HARMONY_OS, IM_CLIENT ->
                contractMainInfoBO.setSourceType(1);
            case ALIPAY_MINI_PROGRAM -> contractMainInfoBO.setSourceType(2);
            case XIAN_YU -> contractMainInfoBO.setSourceType(3);
        }
        return contractMainInfoBO;
    }

    /**
     * 校验用户实名信息
     *
     * @param contractInitiateReqDTO
     * @return
     */
    public Tuple2<BuyerInfoDTO, SellerInfoDTO> checkUserCertInfo(ContractInitiateReqDTO contractInitiateReqDTO) {
        Tuple6<String, String, Integer, String, Integer, GuaranteeTypeEnum> tuple = Tuple.of(contractInitiateReqDTO.getOrderItemId()
            .trim(), contractInitiateReqDTO.getBuyerId(), contractInitiateReqDTO.getBuyerType(), contractInitiateReqDTO.getSellerId(), contractInitiateReqDTO.getSellerType(), GuaranteeTypeEnum.getGuaranteeNameByCode(contractInitiateReqDTO.getBuyerGuaranteeType()));
        TradeIdentityRespDTO tradeIdentityData = orderItemDubboGateway.getTradeIdentity(tuple._1);
        Integer productType = tradeIdentityData == null ? 1 : tradeIdentityData.getProductType();

        BuyerInfoDTO buyerInfoDTO = contractUserAppService.getBuyerInfo(tuple._2, tuple._3, productType);
        boolean isAdult = Option.when(tuple._6.equals(GuaranteeTypeEnum.INDEMNITY_PROVE), () -> IDCardValidator.isAdult(buyerInfoDTO.getCertNo()))
            .getOrElse(TRUE);
        if (!isAdult) {
            throw new BusinessException(ErrorCode.TRANSACTIONS_PROHIBITED_UNDERAGE);
        }
        SellerInfoDTO sellerInfoDTO = contractUserAppService.getSellerInfo(tuple._4, tuple._5, productType);
        return Tuple.of(buyerInfoDTO, sellerInfoDTO);
    }

    private void handleAutomationCallback(ContractInitiateReqDTO contractInitiateReqDTO) {
        Tuple2<String, Integer> tuple = Tuple.of(contractInitiateReqDTO.getOrderItemId()
            .trim(), contractInitiateReqDTO.getTradeContractType());
        try {
            switch (tuple._2) {
                case 20 -> {
                    OrderItemIdReqDTO orderItemIdReqDTO = new OrderItemIdReqDTO();
                    orderItemIdReqDTO.setOrderItemId(tuple._1);
                    contractAppService.contractSignFinish(orderItemIdReqDTO);
                    log.info("执行自动交付回调................................");
                }
            }
        } catch (Exception e) {
            log.error("自动交付回调异常", e);
        }
    }

    /**
     * 校验删除合同权限
     * 
     * @param param : 入参
     */
    public Boolean checkDelPerm(ContractDelReqDTO param) {
        String userId = ImUserUtil.getUserId();
        if (StringUtils.isEmpty(userId)) {
            return Boolean.FALSE;
        }
        ContractData contractData = contractDomainService
            .findOneByContractDataIdAndOrderItemId(param.getContractDataId(), param.getOrderItemId());
        if (Objects.isNull(contractData)) {
            return Boolean.FALSE;
        }
        // 待签署或签署中 允许删除
        if (Objects.equals(ContractStatusEnum.WAIT_SIGN.getCode(), contractData.getStatus())
            || Objects.equals(ContractStatusEnum.SIGNING.getCode(), contractData.getStatus())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteContract(ContractDelReqDTO param) {
        Boolean b = this.checkDelPerm(param);
        if (!b) {
            return Boolean.FALSE;
        }
        contractDomainService.deleteContract(param.getContractDataId(), param.getOrderItemId(), ImUserUtil.getUserId());
        // 删除卖家资料
        contractSellerInfoRepository.delByOrderItemId(param.getOrderItemId());
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //获取交付信息
                OrderDeliveryInfoRespDTO orderDeliveryInfo = orderItemDubboGateway.getOrderDeliveryInfo(param.getOrderItemId());
                //查询合同类型
                List<ContractFile> files = contractFileRepository.queryFileListByContractDataId(param.getContractDataId());
                if (CollUtil.isNotEmpty(files)) {
                    ContractFile contractFile = files.stream()
                        .filter(e -> e.getContractType() != 1)
                        .findFirst()
                        .orElse(null);
                    if (ObjectUtil.isNotEmpty(contractFile)) {
                        List<ContractSignInfo> contractSignInfoList = contractSignInfoRepository.getSignInfoListByFileId(contractFile.getContractFileId());
                        String msgUid = null;
                        if (CollUtil.isNotEmpty(contractSignInfoList)) {
                            msgUid = contractSignInfoList.stream().findFirst().get().getMsgUid();
                        }
                        if (ObjectUtil.isNotEmpty(orderDeliveryInfo)) {
                            contractCardDomainService.sendDeleteContractCard(orderDeliveryInfo, contractFile.getContractType(), msgUid);
                        }
                    }
                    crtStatusChangeNotifyEvent.notifyContractFlowNode(orderDeliveryInfo, false, ContractStatusEnum.TO_BE_INITIATED.getCode());
                }
            }
        });
        return Boolean.TRUE;
    }


}
