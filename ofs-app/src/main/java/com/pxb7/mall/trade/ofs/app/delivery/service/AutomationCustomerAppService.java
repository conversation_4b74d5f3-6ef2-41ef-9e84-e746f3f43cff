package com.pxb7.mall.trade.ofs.app.delivery.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.formdata.FormDataMsgFieldType;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYSX;
import com.pxb7.mall.product.client.dto.response.game.GameAttrRespDTO;
import com.pxb7.mall.product.client.dto.response.game.GameAttrValRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.CommitAccountAdditionReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.FlowNextReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.FormDataMsgContentRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.SellerAccountCollectRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeLogRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.delivery.domain.DeliveryDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.WakeUpBO;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowCardEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowNodeStatusEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.FlowUserEnum;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.CommitAccountRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.DeliveryInfoDbRepository;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.CommitAccount;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.DeliveryInfo;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.Flow;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.FlowNodeLog;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.product.GameGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.im.ImMsgGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.order.OrderInfoGateway;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class AutomationCustomerAppService {

    @Resource
    private CommitAccountRepository commitAccountRepository;

    @Resource
    private DeliveryDomainService deliveryDomainService;

    @Resource
    private ImMsgGateway imMsgGateway;
    @Resource
    private DeliveryInfoDbRepository deliveryInfoDbRepository;

    @Resource
    private FlowDomainService flowDomainService;

    @Resource
    private GameGateway gameGateway;

    @Resource
    private OrderInfoGateway orderInfoGateway;

    /**
     * 用户端 节点快照
     *
     * @param orderItemId
     * @return
     */
    public FlowNodeRespDTO getUserFlowNodeSnapshot(String orderItemId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow == null) {
            return new FlowNodeRespDTO();
        }
        //初始化所有的大节点
        List<FlowNodeLogRespDTO> flowNodeLogRespDTOS = new ArrayList<>();
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.TRADE_ALERT));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.COLLECT));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.BLACK));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.BUYER_AUTHENTICATION_ACCOUNT));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.DELIVERY_FINISH));

        Map<String, FlowNodeLog> nodeLogMap = flowDomainService.getNodeLog(flow.getFlowId(), flow.getOrderItemId());

        for (FlowNodeLogRespDTO respDTO : flowNodeLogRespDTOS) {

            String flowNode = respDTO.getFlowNode();
            FlowNodeLog nodeLog = nodeLogMap.get(flowNode);

            if (nodeLog != null && nodeLog.getParent()) {
                respDTO.setStatus(nodeLog.getStatus());
            }
            // 换绑需要展示子节点
            if (FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.getNode().equals(flowNode)) {
                if (nodeLog == null || StringUtils.isEmpty(nodeLog.getNodeValue())) {
                    continue;
                }

                JSONObject nodeValue = JSONObject.parseObject(nodeLog.getNodeValue());
                List<FlowNodeLogRespDTO> childNodeList =
                    JSONArray.parseArray(nodeValue.getString("childNode"), FlowNodeLogRespDTO.class);

                // 等待节点的索引值
                Integer waitNodeIndex = null;

                for (int i = 0; i < childNodeList.size(); i++) {
                    FlowNodeLogRespDTO childNode = childNodeList.get(i);
                    if (Objects.equals(flow.getFlowNode(), FlowNodeEnum.DELIVERY_FINISH.getNode()) || Objects.equals(
                        flow.getFlowNode(), FlowNodeEnum.DELIVERY_COMPLETED.getNode()) || nodeLogMap.containsKey(
                        childNode.getFlowNode())) {
                        childNode.setStatus(FlowNodeStatusEnum.FINISH.status());
                    } else {
                        childNode.setStatus(FlowNodeStatusEnum.WAIT.status());
                        if (waitNodeIndex == null) {
                            waitNodeIndex = i;
                        }
                    }
                }
                if (waitNodeIndex != null) {
                    for (int i = 0; i < childNodeList.size(); i++) {
                        FlowNodeLogRespDTO dto = childNodeList.get(i);
                        if (i > waitNodeIndex) {
                            dto.setStatus(FlowNodeStatusEnum.READY.status());
                        }
                    }
                }
                respDTO.setChild(childNodeList);
            }

        }

        FlowNodeRespDTO flowNodeRespDTO = new FlowNodeRespDTO();
        flowNodeRespDTO.setFlowNodes(flowNodeLogRespDTOS);
        return flowNodeRespDTO;
    }

    /**
     * 客服端节点快照
     *
     * @param orderItemId
     * @return
     */

    public FlowNodeRespDTO getFlowNodeSnapshot(String orderItemId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow == null) {
            return new FlowNodeRespDTO();
        }
        String flowNodeSnapshot = flow.getNodeSnapshoot();
        FlowNodeRespDTO flowNodeRespDTO = JSONObject.parseObject(flowNodeSnapshot, FlowNodeRespDTO.class);

        if (ObjectUtil.isEmpty(flowNodeSnapshot)) {
            return flowNodeRespDTO;
        }
        //初始化所有的大节点
        ArrayList<FlowNodeLogRespDTO> flowNodeLogRespDTOS = new ArrayList<>();
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.TRADE_ALERT));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.COLLECT));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.BLACK));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.BUYER_AUTHENTICATION_ACCOUNT));
        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH));

        flowNodeLogRespDTOS.add(new FlowNodeLogRespDTO(FlowNodeEnum.DELIVERY_FINISH));

        List<FlowNodeLogRespDTO> flowNodes = flowNodeRespDTO.getFlowNodes();

        //获取最后一个子节点，现在的流程，大节点不会单独存在，一定会有其下的小节点，所以最后一个节点，一定是子节点
        //获取子节点对应的大节点，在流程进行中时，这时大节点的状态应该是进行中
        FlowNodeLogRespDTO lastNode = flowNodes.get(flowNodes.size() - 1);
        String pid = lastNode.getPid();

        for (FlowNodeLogRespDTO flowNodeLogRespDTO : flowNodeLogRespDTOS) {
            for (FlowNodeLogRespDTO flowNode : flowNodes) {
                // 交易完成没有子节点
                if (flowNode.getFlowNode().equals(FlowNodeEnum.DELIVERY_FINISH.getNode())) {
                    flowNodeLogRespDTO.setStatus(FlowNodeStatusEnum.FINISH.status());
                    continue;
                }
                //设置大节点的状态值
                if (flowNode.getParent() && flowNode.getFlowNode().equals(flowNodeLogRespDTO.getFlowNode())) {
                    if (flowNode.getFlowNode().equals(pid)) {
                        flowNodeLogRespDTO.setStatus(FlowNodeStatusEnum.WAIT.status());
                    } else {
                        flowNodeLogRespDTO.setStatus(FlowNodeStatusEnum.FINISH.status());
                    }
                } else if (!flowNode.getParent() && flowNode.getPid().equals(flowNodeLogRespDTO.getFlowNode())) {
                    List<FlowNodeLogRespDTO> child = flowNodeLogRespDTO.getChild();
                    child.add(flowNode);
                }
            }
        }

        flowNodeRespDTO.setFlowNodes(flowNodeLogRespDTOS);
        return flowNodeRespDTO;
    }

    /**
     * 获取客服代填表单数据
     *
     * @param dto
     * @return
     */
    public SingleResponse<SellerAccountCollectRespDTO> getServiceReplace(FlowNextReqDTO dto, String clientType) {
        SellerAccountCollectRespDTO sellerAccountCollectRespDTO = new SellerAccountCollectRespDTO();

        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);

        //获取订单商品信息
        OrderInfoDubboRespDTO orderInfo = orderInfoGateway.getOrderInfo(flow.getOrderItemId());
        if (orderInfo == null) {
            throw new BizException(ErrorCode.GET_PRODUCT_INFO_FAIL_ERROR.getErrCode(),
                ErrorCode.GET_PRODUCT_INFO_FAIL_ERROR.getErrDesc());
        }

        sellerAccountCollectRespDTO.setProductPic(orderInfo.getProductPic());
        sellerAccountCollectRespDTO.setProductName(orderInfo.getProductName());
        sellerAccountCollectRespDTO.setProductSalePrice(orderInfo.getProductSalePrice());
        sellerAccountCollectRespDTO.setProductOriginalPrice(orderInfo.getProductOriginalPrice());
        sellerAccountCollectRespDTO.setPayoutAmount(orderInfo.getPayoutAmount());

        //尝试获取卖家填写资料信息，如果有数据，说明是修改，将用户填写的数据回显到前端
        FlowNodeLog accountInfo = flowDomainService.getAccountInfo(flow.getFlowId(), flow.getOrderItemId());

        // 用户已经填写表单
        if (ObjectUtils.isNotEmpty(accountInfo) && StringUtils.isNotEmpty(
            accountInfo.getNodeValue()) && FlowUserEnum.SELLER.value().equals(accountInfo.getUserType())) {

            DeliveryInfo deliveryInfo = deliveryInfoDbRepository.getDeliveryInfo(flow.getOrderItemId());

            List<FormDataMsgContentRespDTO> formData =
                JSONArray.parseArray(accountInfo.getNodeValue(), FormDataMsgContentRespDTO.class);
            if (deliveryInfo == null) {
                sellerAccountCollectRespDTO.setFormData(formData);
                return SingleResponse.of(sellerAccountCollectRespDTO);
            }

            List<FormDataMsgContentRespDTO> sellerSubmitFormData = new ArrayList<>();
            for (FormDataMsgContentRespDTO item : formData) {
                // 在来源字段后添加 是否续包
                if ("source".equals(item.getFieldKey())) {
                    if (deliveryInfo.getAccountSource() != null) {
                        item.setFieldValue(deliveryInfo.getAccountSource());
                    }
                    sellerSubmitFormData.add(item);
                    List<FormDataMsgContentRespDTO.BoxEnum> guaranteeBoxEnums = guaranteeBox();
                    FormDataMsgContentRespDTO repackageRespDTO =
                        new FormDataMsgContentRespDTO().setFieldType(FormDataMsgFieldType.SINGLE.getCode())
                            .setFieldName("是否续包").setFieldKey("guarantee").setContent(guaranteeBoxEnums);

                    sellerSubmitFormData.add(repackageRespDTO);
                    continue;
                }
                sellerSubmitFormData.add(item);
            }
            sellerAccountCollectRespDTO.setFormData(sellerSubmitFormData);
            return SingleResponse.of(sellerAccountCollectRespDTO);

        }

        CommitAccount commitAccount = commitAccountRepository.getTamplateByGameId(flow.getGameId());

        List<CommitAccountAdditionReqDTO> list = new ArrayList<>();
        if (commitAccount != null) {
            //获取后台配置卖家账号必填信息配置
            String additions = commitAccount.getAdditions();
            list = JSONArray.parseArray(additions, CommitAccountAdditionReqDTO.class);
        }

        ArrayList<FormDataMsgContentRespDTO> sellerFrom = new ArrayList<>();
        List<FormDataMsgContentRespDTO.BoxEnum> boxEnums = accountSourceBox();
        List<FormDataMsgContentRespDTO.BoxEnum> guaranteeBoxEnums = guaranteeBox();

        List<FormDataMsgContentRespDTO.BoxEnum> loginMethodEnums = accountLoginMethodBox();

        //根据卡片展示顺序组装卡片信息
        sellerFrom.add(
            new FormDataMsgContentRespDTO().setFieldType(FormDataMsgFieldType.SINGLE.getCode()).setFieldName("账号来源")
                .setFieldKey("source").setContent(boxEnums));
        if ("8".equals(clientType)) {
            sellerFrom.add(new FormDataMsgContentRespDTO().setFieldType(FormDataMsgFieldType.SINGLE.getCode())
                .setFieldName("是否续包").setFieldKey("guarantee").setContent(guaranteeBoxEnums));
        }
        sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.TEXT.getCode(), "游戏账号", "gameAccount"));
        sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.SINGLE.getCode(), "登录方式", "loginMethod",
            loginMethodEnums));
        sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.TEXT.getCode(), "游戏密码", "gameLoginPwd"));

        if (CollUtil.isNotEmpty(list)) {
            List<String> attrIds = list.stream().map(CommitAccountAdditionReqDTO::getAttrId).toList();
            List<GameAttrRespDTO> gameAttrList = gameGateway.getGameAttrs(attrIds);

            if (CollectionUtils.isNotEmpty(gameAttrList)) {

                //示例图map
                HashMap<String, String> picturesMap = list.stream()
                    .collect(HashMap::new, (map, item) -> map.put(item.getAttrId(), item.getImgUrl()), HashMap::putAll);

                //组装账号必填信息卡片内输入框
                for (GameAttrRespDTO datum : gameAttrList) {
                    switch (datum.getOptionType()) {
                        //操作类型 1 单选 2 多选 3 文本
                        case 1: {
                            List<GameAttrValRespDTO> attrVals = datum.getAttrVals();
                            List<FormDataMsgContentRespDTO.BoxEnum> boxEnumStream = attrVals.stream().map(e -> {
                                FormDataMsgContentRespDTO.BoxEnum boxEnum = new FormDataMsgContentRespDTO.BoxEnum();
                                boxEnum.setLabel(e.getItemName());
                                boxEnum.setValue(e.getItemId());
                                return boxEnum;
                            }).toList();
                            sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.SINGLE.getCode(),
                                datum.getAttrName(), datum.getAttrId(), boxEnumStream,
                                picturesMap.get(datum.getAttrId())));
                            break;
                        }
                        case 2: {
                            List<GameAttrValRespDTO> attrVals = datum.getAttrVals();
                            List<FormDataMsgContentRespDTO.BoxEnum> boxEnumStream = attrVals.stream().map(e -> {
                                FormDataMsgContentRespDTO.BoxEnum boxEnum = new FormDataMsgContentRespDTO.BoxEnum();
                                boxEnum.setLabel(e.getItemName());
                                boxEnum.setValue(e.getItemId());
                                return boxEnum;
                            }).toList();
                            sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.MULTIPLE.getCode(),
                                datum.getAttrName(), datum.getAttrId(), boxEnumStream,
                                picturesMap.get(datum.getAttrId())));
                            break;
                        }
                        case 3: {
                            sellerFrom.add(
                                new FormDataMsgContentRespDTO(FormDataMsgFieldType.TEXT.getCode(), datum.getAttrName(),
                                    datum.getAttrId(), picturesMap.get(datum.getAttrId())));
                            break;
                        }
                    }
                }

            }

            if (!Objects.isNull(commitAccount)) {
                if (Boolean.TRUE.equals(commitAccount.getRealname())) {
                    sellerFrom.add(
                        new FormDataMsgContentRespDTO(FormDataMsgFieldType.TEXT.getCode(), "实名信息", "realname"));
                }
                if (Boolean.TRUE.equals(commitAccount.getScreenshot())) {
                    sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.IMG.getCode(), "二次实名截图",
                        "screenshot"));
                }
                if (Boolean.TRUE.equals(commitAccount.getCardImage())) {
                    sellerFrom.add(new FormDataMsgContentRespDTO(FormDataMsgFieldType.IMG.getCode(), "身份证正面照片",
                        "cardImage"));
                }
            }

            sellerAccountCollectRespDTO.setFormData(sellerFrom);

            return SingleResponse.of(sellerAccountCollectRespDTO);
        }

        if (!Objects.isNull(commitAccount)) {
            if (Boolean.TRUE.equals(commitAccount.getRealname())) {
                sellerFrom.add(
                    new FormDataMsgContentRespDTO(FormDataMsgFieldType.TEXT.getCode(), "实名信息", "realname"));
            }
            if (Boolean.TRUE.equals(commitAccount.getScreenshot())) {
                sellerFrom.add(
                    new FormDataMsgContentRespDTO(FormDataMsgFieldType.IMG.getCode(), "二次实名截图", "screenshot"));
            }
            if (Boolean.TRUE.equals(commitAccount.getCardImage())) {
                sellerFrom.add(
                    new FormDataMsgContentRespDTO(FormDataMsgFieldType.IMG.getCode(), "身份证正面照片", "cardImage"));
            }
        }

        //如果后台没有配置账号必填信息，也返回基础的表单数据
        sellerAccountCollectRespDTO.setFormData(sellerFrom);

        return SingleResponse.of(sellerAccountCollectRespDTO);
    }

    private List<FormDataMsgContentRespDTO.BoxEnum> accountSourceBox() {
        ArrayList<FormDataMsgContentRespDTO.BoxEnum> boxEnums = new ArrayList<>();
        FormDataMsgContentRespDTO.BoxEnum oneself = new FormDataMsgContentRespDTO.BoxEnum();
        oneself.setValue(0);
        oneself.setLabel("自己注册");
        FormDataMsgContentRespDTO.BoxEnum px = new FormDataMsgContentRespDTO.BoxEnum();
        px.setValue(1);
        px.setLabel("螃蟹平台购买");
        FormDataMsgContentRespDTO.BoxEnum other = new FormDataMsgContentRespDTO.BoxEnum();
        other.setValue(2);
        other.setLabel("其他平台购买");
        //        FormDataMsgContentRespDTO.BoxEnum panzhi = new FormDataMsgContentRespDTO.BoxEnum();
        //        panzhi.setValue(3);
        //        panzhi.setLabel("盼之");

        boxEnums.add(oneself);
        boxEnums.add(px);
        boxEnums.add(other);
        //        boxEnums.add(panzhi);
        return boxEnums;
    }

    private List<FormDataMsgContentRespDTO.BoxEnum> guaranteeBox() {
        ArrayList<FormDataMsgContentRespDTO.BoxEnum> boxEnums = new ArrayList<>();
        FormDataMsgContentRespDTO.BoxEnum unGuaranteeBox = new FormDataMsgContentRespDTO.BoxEnum();
        unGuaranteeBox.setValue(0);
        unGuaranteeBox.setLabel("否");
        FormDataMsgContentRespDTO.BoxEnum guaranteeBox = new FormDataMsgContentRespDTO.BoxEnum();
        guaranteeBox.setValue(1);
        guaranteeBox.setLabel("是");

        boxEnums.add(unGuaranteeBox);
        boxEnums.add(guaranteeBox);
        return boxEnums;
    }

    private List<FormDataMsgContentRespDTO.BoxEnum> accountLoginMethodBox() {
        ArrayList<FormDataMsgContentRespDTO.BoxEnum> boxEnums = new ArrayList<>();

        FormDataMsgContentRespDTO.BoxEnum oneself = new FormDataMsgContentRespDTO.BoxEnum();
        oneself.setValue(0);
        oneself.setLabel("密码");
        FormDataMsgContentRespDTO.BoxEnum px = new FormDataMsgContentRespDTO.BoxEnum();
        px.setValue(1);
        px.setLabel("验证码");
        FormDataMsgContentRespDTO.BoxEnum other = new FormDataMsgContentRespDTO.BoxEnum();
        other.setValue(2);
        other.setLabel("扫码登录");

        boxEnums.add(oneself);
        boxEnums.add(px);
        boxEnums.add(other);

        return boxEnums;
    }

    /**
     * 终止交付
     *
     * @param orderItemId
     */
    public void abortDelivery(String orderItemId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow == null) {
            return;
        }

        // 将已经发出去的卖家卡片 设置无效
        if (FlowNodeEnum.COLLECT.getNode().equals(flow.getFlowNode())) {
            //更改扩展信息状态 更新卡片
            SetExpansionDTO sellerCard =
                new SetExpansionDTO().setOperation(FlowCardEnum.SELLER_ACCOUNT_REFUSE.operation())
                    .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYSX());
            imMsgGateway.asyncUpdateExpansion(sellerCard);
        }

        flow.setFlowNode(FlowNodeEnum.TRADE_TERMINATION.getNode());
        // 节点不变，按钮清除
        FlowNodeRespDTO flowNodeRespDTO = JSONObject.parseObject(flow.getNodeSnapshoot(), FlowNodeRespDTO.class);
        flow.setNodeSnapshoot(JSONObject.toJSONString(new FlowNodeRespDTO(flowNodeRespDTO.getFlowNodes())));
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
        flowDomainService.updateNode(flow);

    }

    public SingleResponse<Boolean> abortOrderWakeUpTask(String orderItemId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow == null) {
            return SingleResponse.of(true);
        }
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
        flowDomainService.updateNode(flow);
        return SingleResponse.of(true);
    }
}
