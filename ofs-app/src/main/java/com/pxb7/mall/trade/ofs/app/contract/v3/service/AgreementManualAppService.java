package com.pxb7.mall.trade.ofs.app.contract.v3.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.trade.ofs.app.contract.dto.ContractIndemnityDTO;
import com.pxb7.mall.trade.ofs.app.contract.dto.request.ContractInitiateReqDTO;
import com.pxb7.mall.trade.ofs.app.contract.mapping.ContractDataAppMapping;
import com.pxb7.mall.trade.ofs.app.contract.v3.mapping.AgreementManualAppMapping;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.MqConstants;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.handler.BusinessException;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.contract.domain.model.ContractIndemnityBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.ManualCreateAgreementBO;
import com.pxb7.mall.trade.ofs.contract.domain.v3.model.message.ExtVoucherCreateMessage;
import com.pxb7.mall.trade.ofs.contract.domain.v3.service.CrtAutomationProcessDomainService;
import com.pxb7.mall.trade.ofs.contract.infra.remote.dubbo.OrderItemDubboGateway;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.ContractDataRepository;
import com.pxb7.mall.trade.ofs.contract.infra.repository.db.entity.ContractData;
import com.pxb7.mall.trade.ofs.delivery.infra.messaging.MQProducer;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AgreementManualAppService.java
 * @description: 手动发起合同流程
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/3/31 19:25
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class AgreementManualAppService {
    @Resource
    private MQProducer mqProducer;
    @Resource
    private OrderItemDubboGateway orderItemDubboGateway;
    @Resource
    private ContractDataRepository contractDataRepository;
    @Resource
    private CrtAutomationProcessDomainService crtAutomationProcessDomainService;

    public String manualCreateAgreement(ContractInitiateReqDTO contractInitiateReqDTO) {
        String userId = ImUserUtil.getUserId();
        preCheck(contractInitiateReqDTO, userId);

        ManualCreateAgreementBO manualCreateAgreementBO = AgreementManualAppMapping.INSTANCE.toManualCreateAgreementBO(contractInitiateReqDTO);
        buildIndemnityIdList(contractInitiateReqDTO, manualCreateAgreementBO);
        manualCreateAgreementBO.setCreateUserId(userId);
        manualCreateAgreementBO.setUpdateUserId(userId);

        boolean result = crtAutomationProcessDomainService.manualCreateAgreement(manualCreateAgreementBO);
        return result ? "success" : "failed";
    }

    /**
     * 预校验
     *
     * @param contractInitiateReqDTO 发起合同请求参数
     * @param customerUserId         客服id
     */
    private void preCheck(ContractInitiateReqDTO contractInitiateReqDTO, String customerUserId) {
        Tuple2<String, ContractIndemnityDTO> tuple = Tuple.of(contractInitiateReqDTO.getOrderItemId()
            .trim(), contractInitiateReqDTO.getNormalIndemnity());
        boolean b = RedissonUtils.setIfAbsent(String.format(RedisConstants.INITIATE_CONTRACT, tuple._1), "0", 5, ChronoUnit.SECONDS);
        if (!b) {
            throw new BizException(ErrorCode.REPEAT_INITIATE_CONTRACT.getErrCode(), String.format(ErrorCode.REPEAT_INITIATE_CONTRACT.getErrDesc(), tuple._1));
        }
        // 非交易中的订单无法发起合同
        OrderInfoDubboRespDTO orderInfo = orderItemDubboGateway.getOrderInfo(tuple._1);
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new BusinessException(ErrorCode.ORDER_NOT_EXISTS);
        }
        var orderStatus = orderInfo.getOrderItemStatus();
        if (!OrderItemStatusEnum.DEALING.getValue().equals(orderStatus)) {
            throw new BusinessException(ErrorCode.CONTRACT_ORDER_NOT_IN_TRANSACTION);
        }
        // 订单是否有在途资金单据（收款单/退款单）
        Boolean isExistsFundsInTransitReceipt = orderItemDubboGateway.isExistsFundsInTransitReceipt(tuple._1);
        if (isExistsFundsInTransitReceipt) {
            throw new BusinessException(ErrorCode.CONTRACT_EXIST_TRANSIT_FUND_VOUCHER);
        }
        // 当前订单是否存在合同
        ContractData contractData = contractDataRepository.getContractDataWithOrder(tuple._1);
        if (ObjectUtil.isNotEmpty(contractData)) {
            throw new BusinessException(ErrorCode.CONTRACT_ALREADY_EXIST);
        }
        // 订单是否有包赔
        if (ObjectUtil.isEmpty(tuple._2)) {
            //无包赔发起合同报错，但放款流程不影响
            ExtVoucherCreateMessage extVoucherCreateMessage = new ExtVoucherCreateMessage().setOrderItemId(orderInfo.getOrderItemId());
            if (StrUtil.isNotBlank(customerUserId)) {
                extVoucherCreateMessage.setCreateUserId(customerUserId);
            }
            String result = mqProducer.send(MqConstants.NOTIFY_EXT_VOUCHER_CREATE_TOPIC, MqConstants.NOTIFY_EXT_VOUCHER_CREATE_TAG, extVoucherCreateMessage);
            log.warn("【manualCreateAgreement】send ext_voucher_create_topic message to mq, orderItemId: {}, createUserId:{}, msgId: {}", orderInfo.getOrderItemId(), customerUserId, result);
            throw new BusinessException(ErrorCode.CONTRACT_NO_COMPENSATION);
        }
    }

    /**
     * 构建包赔列表
     *
     * @param contractInitiateReqDTO  发起合同请求参数
     * @param manualCreateAgreementBO 手动创建合同BO
     * @return {@link ManualCreateAgreementBO}
     */
    private ManualCreateAgreementBO buildIndemnityIdList(ContractInitiateReqDTO contractInitiateReqDTO,
                                                         ManualCreateAgreementBO manualCreateAgreementBO) {
        ContractIndemnityBO normalIndemnityBO = ContractDataAppMapping.INSTANCE.toContractIndemnityBO(contractInitiateReqDTO.getNormalIndemnity());
        //增值包赔
        List<ContractIndemnityBO> enhancedIndemnityBOList = new ArrayList<>();

        if (CollUtil.isNotEmpty(contractInitiateReqDTO.getEnhancedIndemnityList())) {
            contractInitiateReqDTO.getEnhancedIndemnityList().forEach(data -> {
                enhancedIndemnityBOList.add(ContractDataAppMapping.INSTANCE.toContractIndemnityBO(data));
            });
        }
        manualCreateAgreementBO.setNormalIndemnityBO(normalIndemnityBO);
        manualCreateAgreementBO.setEnhancedIndemnityBO(enhancedIndemnityBOList);
        return manualCreateAgreementBO;
    }
}
