package com.pxb7.mall.trade.ofs.app.delivery.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ofs.app.delivery.model.CardDelayTimeConfigReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.model.CardDelayTimeConfigRespDTO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardDelayTimeConfigReqBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.CardDelayTimeConfigRespBO;

@Mapper
public interface CardDelayTimeConfigAppMapping {

    CardDelayTimeConfigAppMapping INSTANCE = Mappers.getMapper(CardDelayTimeConfigAppMapping.class);

    CardDelayTimeConfigReqBO.SearchBO cardDelayTimeConfigDTO2SearchBO(CardDelayTimeConfigReqDTO.SearchDTO source);

    CardDelayTimeConfigRespDTO.DetailDTO cardDelayTimeConfigBO2DetailDTO(CardDelayTimeConfigRespBO.DetailBO source);

    List<CardDelayTimeConfigRespDTO.DetailDTO>
        cardDelayTimeConfigBO2ListDTO(List<CardDelayTimeConfigRespBO.DetailBO> source);

}
