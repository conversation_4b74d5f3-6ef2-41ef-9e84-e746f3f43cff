package com.pxb7.mall.trade.ofs.app.contract.dto.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ContractOrderItemDTO.java
 * @description: 合同订单信息DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/27 21:10
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Builder
public class ContractOrderItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4811432107540691829L;
    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 游戏名称
     */
    private String gameName;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品编号
     */
    private String productUniqueNo;
}
