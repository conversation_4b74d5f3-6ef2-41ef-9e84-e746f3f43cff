package com.pxb7.mall.trade.ofs.app.delivery.automation;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonConUrl;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientAndroid;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientPC;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientWap;
import com.pxb7.mall.im.client.dto.request.card.button.styletype.Primary;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeAPI;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkWWC;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYWC;
import com.pxb7.mall.product.client.dto.response.product.ProductRespDTO;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.ExchangeBindingNextNodeDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.FlowNextReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.FlowUserNextReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.request.OrderItemIdReqDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeLogRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.FlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.dto.response.UserChildFlowNodeRespDTO;
import com.pxb7.mall.trade.ofs.app.delivery.mapping.FlowNodeLogAppMapping;
import com.pxb7.mall.trade.ofs.common.config.ErrorCode;
import com.pxb7.mall.trade.ofs.common.config.constants.RedisConstants;
import com.pxb7.mall.trade.ofs.common.config.util.RedissonUtils;
import com.pxb7.mall.trade.ofs.delivery.domain.DeliveryDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.FlowDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.GameFlowAuditDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.OrderDeliverNodeDomainService;
import com.pxb7.mall.trade.ofs.delivery.domain.model.request.SaveFlowNodeLogBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.FlowNodeLogRespBO;
import com.pxb7.mall.trade.ofs.delivery.domain.model.response.WakeUpBO;
import com.pxb7.mall.trade.ofs.delivery.infra.enums.*;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.db.entity.*;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.im.ImMsgGateway;
import com.pxb7.mall.trade.ofs.delivery.infra.repository.remote.dubbo.product.ProductGateway;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.OrderDeliverReqDTO;
import com.pxb7.mall.trade.order.client.enums.order.AccountDeliverNodeNewEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
@Slf4j
public class ExchangeBindingAppService {

    @Resource
    private CardAppService cardAppService;

    @Resource
    private DeliveryDomainService deliveryDomainService;
    @Resource
    private FlowDomainService flowDomainService;
    @Resource
    private GameFlowAuditDomainService gameFlowAuditDomainService;
    @Resource
    private DeliveryEndAppService deliveryEndAppService;
    @Resource
    private OrderDeliverNodeDomainService orderDeliverNodeDomainService;
    @Resource
    private ImMsgGateway imMsgGateway;
    @Resource
    private ProductGateway productGateway;

    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    /**
     * 开启换绑
     *
     * @param orderItemId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PxResponse<Void> startExchangeBinding(String orderItemId) {
        Flow flow = flowDomainService.getFlow(orderItemId);

        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder nodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(orderItemId)
                .flowUserEnum(FlowUserEnum.ADMIN).flowNodeEnum(FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH);

        GameFlowAudit bindingFlowData =
            gameFlowAuditDomainService.getBindingTemplate(flow.getGameId(), flow.getOrderItemId());

        List<BindingFlowNode> bindingFlowNodes = new ArrayList<>();
        if (bindingFlowData != null) {
            bindingFlowNodes =
                gameFlowAuditDomainService.getExchangeBindingNodeSnapshot(bindingFlowData.getBindingTemplateId());

            List<UserChildFlowNodeRespDTO> childNodeValue = new ArrayList<>();
            for (BindingFlowNode bindingFlowNode : bindingFlowNodes) {
                String flowNode = FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value() + "." + bindingFlowNode.getSort();
                String flowNodeName = bindingFlowNode.getNodeName();
                String pid = FlowNodeEnum.EXCHANGE_BINDING_PROCEED.getPid();
                UserChildFlowNodeRespDTO dto = new UserChildFlowNodeRespDTO(flowNode, flowNodeName, pid);
                childNodeValue.add(dto);
            }
            JSONObject nodeValue = new JSONObject();
            nodeValue.put("childNode", childNodeValue);
            nodeValue.put("exchangeNode", bindingFlowNodes);
            nodeLogBOBuilder.nodeValue(JSONObject.toJSONString(nodeValue));
        }
        // 进入换绑节点
        SaveFlowNodeLogBO exchangeBindingNodeLogBO = nodeLogBOBuilder.build();
        exchangeBindingNodeLogBO.setStatus(3);
        flowDomainService.saveNodeLog(exchangeBindingNodeLogBO);

        List<FlowNodeLogRespBO> logList = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(logList);
        List<String> buttons = new ArrayList<>();
        //成功获取到换绑流程配置节点数据，则以后台配置节点为展示节点，否则展示换绑未完成节点
        if (CollectionUtils.isNotEmpty(bindingFlowNodes)) {

            // 换绑节点说明数据不为空，则发送换绑 说明卡片
            cardAppService.sendExchangeExplainCard(flow, bindingFlowNodes);
            BindingFlowNode startNode = bindingFlowNodes.get(0);

            String flowNode = FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value() + "." + startNode.getSort();
            // 换绑节点不为空，则发送换绑接点卡片
            cardAppService.sendNextNodeCard(flow, startNode);

            Integer nodeCompere = startNode.getNodeCompere();
            String wakUpUserId = null;
            if (Objects.equals(nodeCompere, 1)) {
                wakUpUserId = flow.getBuyerId();
            } else if (Objects.equals(nodeCompere, 2)) {
                wakUpUserId = flow.getSellerId();
            }
            // 添加触达
            ProductRespDTO productRespDTO = productGateway.getProduct(flow.getProductId());
            Map<String, Object> param = new HashMap<>();
            param.put("orderNo", orderItemId);
            if (productRespDTO != null && StringUtils.isNotBlank(productRespDTO.getProductUniqueNo())) {
                param.put("productUniqueNo", productRespDTO.getProductUniqueNo());
            } else {
                param.put("productUniqueNo", "");
            }
            WakeUpBO wakeUpBO =
                deliveryDomainService.openNodeWakeUp(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value(), orderItemId,
                    flow.getServiceId(), flow.getRoomId(), wakUpUserId, param);
            List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
            if (wakeUpTaskSnapshot == null) {
                wakeUpTaskSnapshot = new ArrayList<>();
            }
            if (wakeUpBO != null) {
                wakeUpTaskSnapshot.add(wakeUpBO);
            }
            flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

            dtos.add(new FlowNodeLogRespDTO(flowNode, startNode.getNodeName(), FlowNodeStatusEnum.WAIT.status(),
                FlowNodeEnum.EXCHANGE_BINDING_PROCEED.pid(), FlowNodeEnum.EXCHANGE_BINDING_PROCEED.parent()));
            //记录首个换绑节点id，用于进行下一步换绑 后续根据当前换绑模板节点ID binding_flow_node_id 和sort 字段 取下个节点 binding_flow_node_id
            flow.setBindingFlowNodeId(startNode.getBindingFlowNodeId());
            buttons.addAll(FlowNodeButtonsEnum.CHANGE_BINDING.buttons());
        } else {
            dtos.add(FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTORespDTO(
                nodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.EXCHANGE_BINDING_NOT_TEMPLATE).build()));
            buttons.addAll(FlowNodeButtonsEnum.CHANGE_BINDING_NOT_TEMPLATE.buttons());
        }

        //flow表中不以小节点更新flowNode数据，换绑流程中统一为换绑未完成，方便节点控制校验
        flow.setFlowNode(FlowNodeEnum.EXCHANGE_BINDING_WAIT.value());
        flow.setNodeSnapshoot(JSONObject.toJSONString(new FlowNodeRespDTO(dtos, buttons)));

        flowDomainService.updateNode(flow);
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

        return PxResponse.ok();

    }

    /**
     * 智能换绑卡片内数据提交，同时进入下一换绑节点
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PxResponse<FlowNodeRespDTO> exchangeBindingNextNode(ExchangeBindingNextNodeDTO dto) {

        String orderItemId = dto.getOrderItemId();
        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.EXCHANGE_BINDING.concat(orderItemId), "0", 2, ChronoUnit.SECONDS);
        if (!b) {
            throw new BizException(ErrorCode.REPETITIVE_OPERATION_ERROR.getErrCode(),
                ErrorCode.REPETITIVE_OPERATION_ERROR.getErrDesc());
        }
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);

        BindingFlowNode currentBindingFlowNode = getCurrentBindingNode(flow);

        String currentFlowNode = FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value() + "." + currentBindingFlowNode.getSort();
        Integer nodeCompere = currentBindingFlowNode.getNodeCompere();

        // 终止触达
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId());

        // 记录流程日志
        SaveFlowNodeLogBO build = saveFlowNodeLogBOBuilder.build();
        build.setNodeName(currentBindingFlowNode.getNodeName());
        build.setOrderItemId(flow.getOrderItemId());
        //如果没有formData，设置默认值
        build.setNodeValue(StringUtil.isEmpty(dto.getFormData()) ? new JSONObject().toString() : dto.getFormData());
        build.setStatus(1);
        build.setUserType(nodeCompere);
        build.setFlowNode(currentFlowNode);
        build.setParent(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.parent());
        build.setPid(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.pid());
        flowDomainService.saveNodeLog(build);

        String formData = dto.getFormData();
        //更改扩展信息状态
        SetExpansionDTO cardReqDTO =
            new SetExpansionDTO().setOperation(FlowCardEnum.EXCHANGE_BINDING_NEXT_NODE.operation())
                .setMsgUID(dto.getMsgUid()).setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId())
                .setWaterMark(new WaterMarkYWC()).setContent(formData);
        imMsgGateway.asyncUpdateExpansion(cardReqDTO);

        // 智能换绑下一节点
        this.conductNextNodeV2(flow);
        return PxResponse.ok();
    }

    /**
     * 买方确认换绑节点完成
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PxResponse<FlowNodeRespDTO> buyerExchangeBindingFinish(FlowUserNextReqDTO dto) {

        String orderItemId = dto.getOrderItemId();
        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.EXCHANGE_BINDING.concat(orderItemId), "0", 3, ChronoUnit.SECONDS);
        if (!b) {
            throw new BizException(ErrorCode.REPETITIVE_OPERATION_ERROR.getErrCode(),
                ErrorCode.REPETITIVE_OPERATION_ERROR.getErrDesc());
        }
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);

        // 添加放行节点号，在签合同前允许多次提交
        List<String> permitNodes =
            ListUtil.of(FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.value(), FlowNodeEnum.EXCHANGE_BINDING_WAIT.value());

        // 判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }
        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId());

        // 记录流程日志
        flowDomainService.saveNodeLog(
            saveFlowNodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.EXCHANGE_BINDING_FINISH).flowUserEnum(FlowUserEnum.BUYER)
                .build());

        // 订单新增【换绑完成节点】
        orderDeliverNodeDomainService.addOrderDeliveryNode(AccountDeliverNodeNewEnum.ACCOUNT_EXCHANGE_BINDING,
            flow.getOrderItemId());

        // 终止所有唤醒任务
        List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
        if (wakeUpTaskSnapshot == null) {
            wakeUpTaskSnapshot = new ArrayList<>();
        }
        deliveryDomainService.abortAllTask(wakeUpTaskSnapshot);
        flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));
        flow.setBindingFlowNodeId(null);


        //更改扩展信息状态
        SetExpansionDTO finishExpansionDTO =
            new SetExpansionDTO().setOperation(FlowCardEnum.EXCHANGE_BINDING_FINISH.operation())
                .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYWC());
        imMsgGateway.asyncUpdateExpansion(finishExpansionDTO);
        //更改扩展信息状态
        SetExpansionDTO exchangeExpansion =
            new SetExpansionDTO().setOperation(FlowCardEnum.EXCHANGE_BINDING_NEXT_NODE.operation())
                .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYWC());
        imMsgGateway.asyncUpdateExpansion(exchangeExpansion);

        // 换绑节点完成
        flowDomainService.finishNodeLog(flow.getFlowId(), flow.getOrderItemId(),
            FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.getNode());
        imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

        deliveryEndAppService.finish(flow);
        return PxResponse.ok();
    }

    /**
     * 客服点击换绑完成
     *
     * @return
     */
    @Transactional
    public SingleResponse<Boolean> supportBindingFinish(FlowNextReqDTO dto) {
        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);
        // 添加放行节点号，在签合同前允许多次提交
        List<String> permitNodes =
            ListUtil.of(FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.value(), FlowNodeEnum.EXCHANGE_BINDING_WAIT.value());

        // 判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }

        ButtonConUrl android = new ButtonConUrl().setUrl(FlowButtonEnum.EXCHANGE_BINDING_FINISH.androidUrl())
            .setTargetType(new TargetTypeAPI()).setClientType(new ClientAndroid());
        ButtonConUrl pc =
            new ButtonConUrl().setUrl(FlowButtonEnum.EXCHANGE_BINDING_FINISH.pcUrl()).setTargetType(new TargetTypeAPI())
                .setClientType(new ClientPC());
        ButtonConUrl h5 = new ButtonConUrl().setUrl(FlowButtonEnum.EXCHANGE_BINDING_FINISH.wapUrl())
            .setTargetType(new TargetTypeAPI()).setClientType(new ClientWap());

        ButtonConUrl notFinishAndroid =
            new ButtonConUrl().setUrl(FlowButtonEnum.EXCHANGE_BINDING_NOT_FINISH.androidUrl())
                .setTargetType(new TargetTypeAPI()).setClientType(new ClientAndroid());
        ButtonConUrl notFinishPc = new ButtonConUrl().setUrl(FlowButtonEnum.EXCHANGE_BINDING_NOT_FINISH.pcUrl())
            .setTargetType(new TargetTypeAPI()).setClientType(new ClientPC());
        ButtonConUrl notFinishH5 = new ButtonConUrl().setUrl(FlowButtonEnum.EXCHANGE_BINDING_NOT_FINISH.wapUrl())
            .setTargetType(new TargetTypeAPI()).setClientType(new ClientWap());

        // 卡片内按钮

        ButtonCon buttonCon = new ButtonCon().setParam(new OrderItemIdReqDTO(flow.getOrderItemId()))
            .setLabel(FlowButtonEnum.EXCHANGE_BINDING_FINISH.title()).setButtonUrl(List.of(pc, h5, android));
        buttonCon.setType(new Primary());
        ButtonCon notFinishButtonCon = new ButtonCon().setParam(new OrderItemIdReqDTO(flow.getOrderItemId()))
            .setLabel(FlowButtonEnum.EXCHANGE_BINDING_NOT_FINISH.title())
            .setButtonUrl(List.of(notFinishPc, notFinishH5, notFinishAndroid));

        //向买家发送换绑节点完成确认卡片，进入下一节点
        try {
            SendRichTextMsgReqDTO sendRichTextMsgReqDTO =
                new SendRichTextMsgReqDTO().setTitle(FlowCardEnum.EXCHANGE_BINDING_FINISH.title())
                    .setFromUserId(flow.getServiceId()).setTargetId(flow.getRoomId())
                    .setOperation(FlowCardEnum.EXCHANGE_BINDING_FINISH.operation())
                    .setContent(List.of(new RichTextContent(FlowCardEnum.EXCHANGE_BINDING_FINISH.text())))
                    .setOperatorUserIds(List.of(flow.getBuyerId())).setButtons(List.of(notFinishButtonCon, buttonCon));
            imMsgGateway.sendRichTextMsg(sendRichTextMsgReqDTO);
        } catch (Exception e) {
            throw new BizException(ErrorCode.SEND_CARD_FAIL_ERROR.getErrCode(),
                ErrorCode.SEND_CARD_FAIL_ERROR.getErrDesc());
        }

        return SingleResponse.of(true);
    }

    /**
     * 买家换绑完成 不通过
     *
     * @param dto
     * @return
     */
    public PxResponse<FlowNodeRespDTO> buyerExchangeBindingNotFinish(@Valid FlowUserNextReqDTO dto) {
        String orderItemId = dto.getOrderItemId();
        boolean b =
            RedissonUtils.setIfAbsent(RedisConstants.EXCHANGE_BINDING.concat(orderItemId), "0", 3, ChronoUnit.SECONDS);
        if (!b) {
            throw new BizException(ErrorCode.REPETITIVE_OPERATION_ERROR.getErrCode(),
                ErrorCode.REPETITIVE_OPERATION_ERROR.getErrDesc());
        }

        Flow flow = flowDomainService.getFlow(orderItemId);
        flowDomainService.verifyFlowStatus(flow);
        cardAppService.exchangeNotFinish(flow);
        //更改扩展信息状态
        SetExpansionDTO expansionDTO =
            new SetExpansionDTO().setOperation(FlowCardEnum.EXCHANGE_BINDING_FINISH.operation())
                .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkWWC());
        imMsgGateway.asyncUpdateExpansion(expansionDTO);
        return PxResponse.ok();
    }

    /**
     * 客服点击挂ip等待放行
     *
     * @param dto
     * @return
     */
    public PxResponse<FlowNodeRespDTO> supportExchangeBindingWaitPass(FlowNextReqDTO dto) {

        boolean b = RedissonUtils.setIfAbsent(RedisConstants.FLOW_IP_WAIT.concat(dto.getOrderItemId()), "0", 3,
            ChronoUnit.SECONDS);
        if (!b) {
            return PxResponse.ok();
        }

        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);

        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId());

        // 添加放行节点号，在签合同前允许多次提交
        List<String> permitNodes = ListUtil.of(FlowNodeEnum.EXCHANGE_BINDING_WAIT.value());

        //         判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }

        BindingFlowNode bindingFlowNode = getCurrentBindingNode(flow);
        Integer nodeCompere = bindingFlowNode.getNodeCompere();
        if (nodeCompere != 3) {
            throw new BizException(ErrorCode.NODE_COMPERE_ERROR.getErrCode(),
                ErrorCode.NODE_COMPERE_ERROR.getErrDesc());
        }

        // 记录流程日志
        SaveFlowNodeLogBO build = saveFlowNodeLogBOBuilder.build();
        build.setNodeName(bindingFlowNode.getNodeName());
        build.setStatus(1);
        build.setUserType(FlowUserEnum.SERVICE.value());
        build.setFlowNode(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value() + "." + bindingFlowNode.getSort());
        build.setPid(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.pid());
        build.setParent(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.parent());
        flowDomainService.saveNodeLog(build);

        // 客服放行ip后进入下一节点
        conductNextNodeV2(flow);
        return PxResponse.ok();
    }

    /**
     * 获取当前执行节点
     *
     * @return
     */
    private BindingFlowNode getCurrentBindingNode(Flow flow) {
        // 从换绑节点中获取换绑流程快照
        FlowNodeLog nodeLog = flowDomainService.getNodeLog(flow.getFlowId(), flow.getOrderItemId(),
            FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.getNode());

        JSONObject nodeValue = JSONObject.parseObject(nodeLog.getNodeValue());

        List<BindingFlowNode> bindingFlowNodes =
            JSONArray.parseArray(nodeValue.getString("exchangeNode"), BindingFlowNode.class);

        return bindingFlowNodes.stream()
            .filter(o -> Objects.equals(o.getBindingFlowNodeId(), flow.getBindingFlowNodeId())).findFirst()
            .orElse(null);
    }

    private SingleResponse<FlowNodeRespDTO> conductNextNodeV2(Flow flow) {
        // 从换绑节点中获取换绑流程快照
        FlowNodeLog nodeLog = flowDomainService.getNodeLog(flow.getFlowId(), flow.getOrderItemId(),
            FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.getNode());

        JSONObject nodeValue = JSONObject.parseObject(nodeLog.getNodeValue());

        List<BindingFlowNode> bindingFlowNodes =
            JSONArray.parseArray(nodeValue.getString("exchangeNode"), BindingFlowNode.class);

        BindingFlowNode nextBindingFlowNode = null;

        for (int i = 0; i < bindingFlowNodes.size(); i++) {
            BindingFlowNode bindingFlowNode = bindingFlowNodes.get(i);
            if (!Objects.equals(bindingFlowNode.getBindingFlowNodeId(), flow.getBindingFlowNodeId())) {
                continue;
            }
            if (i == bindingFlowNodes.size() - 1) {
                break;
            }
            nextBindingFlowNode = bindingFlowNodes.get(i + 1);
            break;
        }

        // 换绑流程还有剩余流程节点，进入下一个节点
        if (nextBindingFlowNode != null) {
            // 发送智能换绑流程卡片
            cardAppService.sendNextNodeCard(flow, nextBindingFlowNode);

            String nextFlowNode = FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value() + "." + nextBindingFlowNode.getSort();

            Integer nodeCompere = nextBindingFlowNode.getNodeCompere();
            String wakUpUserId = null;
            if (Objects.equals(nodeCompere, 1)) {
                wakUpUserId = flow.getBuyerId();
            } else if (Objects.equals(nodeCompere, 2)) {
                wakUpUserId = flow.getSellerId();
            }
            // 添加触达
            ProductRespDTO productRespDTO = productGateway.getProduct(flow.getProductId());
            Map<String, Object> param = new HashMap<>();
            param.put("orderNo", flow.getOrderItemId());
            if (productRespDTO != null && StringUtils.isNotBlank(productRespDTO.getProductUniqueNo())) {
                param.put("productUniqueNo", productRespDTO.getProductUniqueNo());
            } else {
                param.put("productUniqueNo", "");
            }
            WakeUpBO wakeUpBO = deliveryDomainService.openNodeWakeUp(FlowNodeEnum.EXCHANGE_BINDING_PROCEED.value(),
                flow.getOrderItemId(), flow.getServiceId(), flow.getRoomId(), wakUpUserId, param);
            List<WakeUpBO> wakeUpTaskSnapshot = JSONArray.parseArray(flow.getWakeUpTaskSnapshot(), WakeUpBO.class);
            if (wakeUpTaskSnapshot == null) {
                wakeUpTaskSnapshot = new ArrayList<>();
            }
            if (wakeUpBO != null) {
                wakeUpTaskSnapshot.add(wakeUpBO);
            }
            flow.setWakeUpTaskSnapshot(JSONObject.toJSONString(wakeUpTaskSnapshot));

            List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
            List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);
            dtos.add(new FlowNodeLogRespDTO(nextFlowNode, nextBindingFlowNode.getNodeName(), 3,
                FlowNodeEnum.EXCHANGE_BINDING_PROCEED.pid(), FlowNodeEnum.EXCHANGE_BINDING_PROCEED.parent()));

            // socket 通知前端最新的流程节点
            String jsonString =
                JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CHANGE_BINDING.buttons()));

            flow.setNodeSnapshoot(jsonString);
            flow.setBindingFlowNodeId(nextBindingFlowNode.getBindingFlowNodeId());
            flowDomainService.updateNode(flow);

            //通过融云通知手机端通过接口更新节点信息
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());
            return SingleResponse.of(new FlowNodeRespDTO(dtos));
        } else {
            // 换绑节点完成，进入换绑审核逻辑
            return exchangeBindingAuditV2(flow);
        }
    }

    /**
     * 换绑审核
     *
     * @param flow
     * @return
     */

    private SingleResponse<FlowNodeRespDTO> exchangeBindingAuditV2(Flow flow) {

        List<FlowNodeLogRespBO> bos = flowDomainService.getLogList(flow.getFlowId(), flow.getOrderItemId());
        List<FlowNodeLogRespDTO> dtos = FlowNodeLogAppMapping.INSTANCE.toFlowNodeLogRespDTO(bos);

        // 获取换绑审核流程
        String gameId = flow.getGameId();

        BindingAuditTemplate bindingAuditTemplate =
            gameFlowAuditDomainService.getBindingAuditTemplate(gameId, flow.getOrderItemId());
        // 根据是否配置了换绑审核模板进行相关操作
        if (ObjectUtil.isNotEmpty(bindingAuditTemplate)) {
            dtos.add(
                new FlowNodeLogRespDTO(FlowNodeEnum.EXCHANGE_BINDING_AUDIT.value(), bindingAuditTemplate.getNodeName(),
                    3, FlowNodeEnum.EXCHANGE_BINDING_AUDIT.pid(), FlowNodeEnum.EXCHANGE_BINDING_AUDIT.parent()));

            // socket 通知前端最新的流程节点
            String jsonString =
                JSONObject.toJSONString(new FlowNodeRespDTO(dtos, FlowNodeButtonsEnum.CHANGE_BINDING.buttons()));

            // 换绑流程已完结，进入到换绑审核节点
            flow.setBindingFlowNodeId(bindingAuditTemplate.getBindingAuditTemplateId());
            flow.setFlowNode(FlowNodeEnum.EXCHANGE_BINDING_AUDIT_WAIT.value());
            flow.setNodeSnapshoot(jsonString);
            flowDomainService.updateNode(flow);

            //通过融云通知手机端通过接口更新节点信息
            imMsgGateway.asyncUpdateNode(flow.getServiceId(), flow.getRoomId());

            //发送审核等待时间卡片
            cardAppService.sendAuditWaitCard(flow, bindingAuditTemplate);

            return SingleResponse.of(new FlowNodeRespDTO(dtos));
        } else {
            // 换绑审核完成
            SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder builder =
                SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(flow.getOrderItemId());
            flowDomainService.saveNodeLog(
                builder.flowNodeEnum(FlowNodeEnum.EXCHANGE_BINDING_AUDIT_FINISH).flowUserEnum(FlowUserEnum.SERVICE)
                    .orderItemId(flow.getOrderItemId()).build());
            // 通知订单换绑节点完成
            notifyOrder(flow);

            //更改扩展信息状态
            SetExpansionDTO expansionReqDTO =
                new SetExpansionDTO().setOperation(FlowCardEnum.EXCHANGE_BINDING_NEXT_NODE.operation())
                    .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYWC());
            imMsgGateway.asyncUpdateExpansion(expansionReqDTO);

            //更改扩展信息状态
            SetExpansionDTO finishExpansionReqDTO =
                new SetExpansionDTO().setOperation(FlowCardEnum.EXCHANGE_BINDING_FINISH.operation())
                    .setFormUserId(flow.getServiceId()).setTargetId(flow.getRoomId()).setWaterMark(new WaterMarkYWC());
            imMsgGateway.asyncUpdateExpansion(finishExpansionReqDTO);

            deliveryEndAppService.finish(flow);
            return SingleResponse.of(new FlowNodeRespDTO(dtos));
        }
    }

    /**
     * 客服点击换绑审核放行
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PxResponse<FlowNodeRespDTO> supportExchangeBindingAuditPass(FlowNextReqDTO dto) {

        boolean b = RedissonUtils.setIfAbsent(RedisConstants.FLOW_AUDIT_WAIT.concat(dto.getOrderItemId()), "0", 3,
            ChronoUnit.SECONDS);
        if (!b) {
            return PxResponse.ok();
        }

        Flow flow = flowDomainService.getFlow(dto.getOrderItemId());
        flowDomainService.verifyFlowStatus(flow);
        SaveFlowNodeLogBO.SaveFlowNodeLogBOBuilder saveFlowNodeLogBOBuilder =
            SaveFlowNodeLogBO.builder().flowId(flow.getFlowId()).orderItemId(dto.getOrderItemId());

        // 添加放行节点号，在签合同前允许多次提交
        List<String> permitNodes = ListUtil.of(FlowNodeEnum.EXCHANGE_BINDING_AUDIT_WAIT.value());

        // 获取换绑审核流程
        String gameId = flow.getGameId();

        BindingAuditTemplate bindingAuditTemplate =
            gameFlowAuditDomainService.getBindingAuditTemplate(gameId, flow.getOrderItemId());

        // 判断流程执行是否符合预定流程顺序
        if (!permitNodes.contains(flow.getFlowNode())) {
            throw new BizException(ErrorCode.FLOW_NODE_ORDER_ERROR.getErrCode(),
                ErrorCode.FLOW_NODE_ORDER_ERROR.getErrDesc());
        }

        // 记录换绑审核完成节点
        flowDomainService.saveNodeLog(saveFlowNodeLogBOBuilder.flowNodeEnum(FlowNodeEnum.EXCHANGE_BINDING_AUDIT_FINISH)
            .flowUserEnum(FlowUserEnum.SERVICE).build());

        //以订单id为键，储存一个redisKey，用来标识此次的审核等待已被手动放行
        //bindingAuditTemplate为null，说明是客服点击放款来创建放款单，此时已经进入了放款流程，不必再对审核挂时长进行处理
        if (ObjectUtils.isNotEmpty(bindingAuditTemplate)) {
            RedissonUtils.setIfAbsent(RedisConstants.FLOW_AUDIT_WAIT.concat(flow.getOrderItemId()), "0",
                bindingAuditTemplate.getReviewTime() + (bindingAuditTemplate.getSystemTime() * 2), ChronoUnit.HOURS);
        }

        deliveryEndAppService.finish(flow);
        // 更新节点
        imMsgGateway.updateNode(flow.getServiceId(), flow.getRoomId());

        //通知订单
        notifyOrder(flow);
        return PxResponse.ok();
    }

    /**
     * 通知订单换绑节点完成
     *
     * @param flow
     */
    private void notifyOrder(Flow flow) {
        // 告知订单某个节点完结
        OrderDeliverReqDTO orderDeliverReqDTO = new OrderDeliverReqDTO();
        orderDeliverReqDTO.setNode(FlowNodeEnum.EXCHANGE_BINDING_NOT_FINISH.value());
        orderDeliverReqDTO.setOrderItemId(flow.getOrderItemId());
        orderDeliverReqDTO.setNodeTime(LocalDateTime.now());
        orderInfoDubboServiceI.addDeliverNode(orderDeliverReqDTO);
    }

    public void exchangeBindingHasProblem(String orderItemId, String userId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        flowDomainService.verifyFlowStatus(flow);
        imMsgGateway.asyncSendRemindMsg(flow.getRoomId(), userId);
    }

    public boolean nodePass(String orderItemId, String bindingFlowNodeId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow == null) {
            return true;
        }
        boolean exchangeFinish = flowDomainService.containsNode(flow.getFlowId(), orderItemId,
            FlowNodeEnum.EXCHANGE_BINDING_FINISH.getNode());
        if (exchangeFinish) {
            return true;
        }
        if (Objects.equals(flow.getBindingFlowNodeId(), bindingFlowNodeId)) {
            return false;
        }

        return true;

    }

    public boolean auditPass(String orderItemId) {
        Flow flow = flowDomainService.getFlow(orderItemId);
        if (flow != null && FlowNodeEnum.EXCHANGE_BINDING_AUDIT_WAIT.getNode().equals(flow.getFlowNode())) {
            return false;
        }
        return true;
    }
}
