package com.pxb7.mall.trade.ofs.app.delivery.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 流程节点日志表(DeliveryProcessTaskLog)实体类
 *
 * <AUTHOR>
 * @since 2025-02-13 11:02:56
 */
public class DeliveryProcessTaskLogRespDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        private Long id;
        private String orderItemId;
        private String processInsId;
        private Long processNodeConfigId;
        private String taskInsId;
        private String cardMsgId;
        private Integer taskStatus;
        private String wakeTaskId;
    }
}
