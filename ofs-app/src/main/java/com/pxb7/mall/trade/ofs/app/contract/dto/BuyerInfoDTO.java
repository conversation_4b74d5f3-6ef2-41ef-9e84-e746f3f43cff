package com.pxb7.mall.trade.ofs.app.contract.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: BuyerInfoDTO.java
 * @description: 买家信息
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/14 13:16
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class BuyerInfoDTO implements Serializable {

    private String buyerId;
    private String buyerName;
    private String phone;
    private String certNo;
    /**
     * 用户类型 1:散户 2:号商
     */
    private Integer buyerType;
    /**
     * 号商是否有效
     */
    private Boolean buyerEffect;
}
