package com.pxb7.mall.trade.ofs.app.delivery.model;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * faq主题流程关联表(FaqTopicProcess)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 11:23:29
 */
public class FaqTopicProcessRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class  DetailDTO{
         private Long  id;
         private Long  faqTopicId;
         private Long  processConfigNodeId;
    }
}

